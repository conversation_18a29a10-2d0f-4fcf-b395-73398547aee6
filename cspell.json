// cSpell Settings
{
  // Version of the setting file.  Always 0.2
  "version": "0.2",
  // language - current active spelling language
  "language": "en",
  // words - list of words to be always considered correct
  "words": [
    "<PERSON>sen",
    "Antialiasing",
    "AUTOSAR",
    "Axisd",
    "bbft",
    "bbfv",
    "bottops",
    "Bpearl",
    "bugprone",
    "CALIB",
    "Cbus",
    "centralwidget",
    "chardet",
    "CICD",
    "cmakedefine",
    "combobox",
    "cpack",
    "customwidgets",
    "dialout",
    "DIFOP",
    "dspinbox",
    "dtags",
    "duts",
    "dwntst",
    "Eigen",
    "Emissing",
    "Fout",
    "FUNCSIG",
    "fusa",
    "gbit",
    "gedit",
    "gitsubmodule",
    "gmock",
    "googletest",
    "gprmc",
    "GPTP",
    "groupbox",
    "gtest",
    "hhmmss",
    "hicpp",
    "horstretch",
    "hsizetype",
    "intrin",
    "<PERSON>",
    "<PERSON>",
    "libmems",
    "libqt",
    "librsfsc",
    "lineedit",
    "loguru",
    "LPTOP",
    "lsusb",
    "mainwindow",
    "MEMS",
    "MEMSTCP",
    "MEMSUDP",
    "METATYPE",
    "mseries",
    "MSOP",
    "munubar",
    "NOLINT",
    "NOLINTNEXTLINE",
    "opencv",
    "OPENMP",
    "pcap",
    "pcba",
    "Pixmap",
    "pktcnt",
    "QMESSAGE",
    "qobject",
    "qreal",
    "qresource",
    "qsetting",
    "qsettings",
    "Quaterniond",
    "robosense",
    "rsdata",
    "rsfsc",
    "RSFSCLIB",
    "RSFSCLOG",
    "RSFSCQSettings",
    "rsfsg's",
    "rslidar",
    "Shen",
    "SHIYAN",
    "Sout",
    "spdlog",
    "suteng",
    "tablewidget",
    "tabwidget",
    "thorlabs",
    "topboard",
    "toptobot",
    "tparam",
    "unitless",
    "uptst",
    "usbtmc",
    "utest",
    "VBavg",
    "VBpeak",
    "vbus",
    "vccaux",
    "vccint",
    "verstretch",
    "widgetaction",
    "YAMLCPP",
    "Ying",
    "Zhang",
    "ZHONG"
  ],
  // flagWords - list of words to be always considered incorrect
  // This is useful for offensive words and common spelling errors.
  // For example "hte" should be "the"
  "flagWords": [],
  "dictionaries": [
    "cpp",
    "python",
    "bash",
    "en_us",
    "latex",
    "public-licenses",
    "softwareTerms"
  ]
}
