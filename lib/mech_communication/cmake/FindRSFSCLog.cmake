﻿cmake_minimum_required(VERSION 3.15.0)

if(NOT RSFSCLog_FOUND)
  find_package(RSFSCLog CONFIG QUIET)
  if(RSFSCLog_FOUND)
    message(STATUS "RSFSCLog found, version: ${RSFSCLog_VERSION}")
  endif()
endif()

if(NOT RSFSCLog_FOUND)

  find_package(
    Qt5
    COMPONENTS Widgets
    REQUIRED)

  if(NOT DEFINED RSFSCLOG_SOURCE_DIR)
    if(NOT DEFINED RSFSCLOG_GIT_URL)
      execute_process(
        COMMAND git config --get remote.origin.url
        WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
        OUTPUT_VARIABLE CURRENT_GIT_URL
        OUTPUT_STRIP_TRAILING_WHITESPACE)

      if(CURRENT_GIT_URL MATCHES "^git@")
        set(RSFSCLOG_GIT_URL "***********************:system_codebase/factory_tool/common_lib/rsfsc_log.git")
      elseif(CURRENT_GIT_URL MATCHES "^http://")
        set(RSFSCLOG_GIT_URL "http://gitlab.robosense.cn/system_codebase/factory_tool/common_lib/rsfsc_log.git")
      elseif(CURRENT_GIT_URL MATCHES "^https://")
        set(RSFSCLOG_GIT_URL "https://gitlab.robosense.cn/system_codebase/factory_tool/common_lib/rsfsc_log.git")
      else()
        message(FATAL_ERROR "Unsupported Git URL format: ${CURRENT_GIT_URL}")
      endif()
    endif()

    if(NOT DEFINED RSFSCLOG_TAG)
      set(RSFSCLOG_TAG "v2.2.5")
      message(STATUS "RSFSCLOG_TAG not set, using default value ${RSFSCLOG_TAG}")
    endif()

    if(RSFSCLOG_TAG MATCHES "^v[0-9]+\\.[0-9]+\\.[0-9]+$")
      string(REGEX REPLACE "^v" "" version_check "${RSFSCLOG_TAG}")
    elseif(RSFSCLOG_TAG MATCHES "^[0-9]+\\.[0-9]+\\.[0-9]+$")
      set(version_check "${RSFSCLOG_TAG}")
    else()
      set(version_check "")
    endif()

    include(FetchContent)
    if(NOT DEFINED FETCH_BASE_DIR_SET)
      set(FETCH_BASE_DIR_SET "${CMAKE_SOURCE_DIR}/build/_deps")
      set(FETCHCONTENT_BASE_DIR ${FETCH_BASE_DIR_SET})
      message(STATUS "Set FETCHCONTENT_BASE_DIR to ${FETCHCONTENT_BASE_DIR}")
    endif()
    set(FETCHCONTENT_QUIET OFF)
    FetchContent_Declare(
      rsfsc_log
      GIT_REPOSITORY "${RSFSCLOG_GIT_URL}"
      GIT_TAG "${RSFSCLOG_TAG}"
      USES_TERMINAL_DOWNLOAD TRUE
      GIT_SHALLOW TRUE
      GIT_DEPTH 1
      GIT_CONFIG advice.detachedHead=false)
  else()
    message(STATUS "RSFSCLOG_SOURCE_DIR is set to ${RSFSCLOG_SOURCE_DIR}")
    FetchContent_Declare(rsfsc_log SOURCE_DIR "${RSFSCLOG_SOURCE_DIR}")
    list(APPEND CMAKE_PREFIX_PATH "${RSFSCLOG_SOURCE_DIR}/lib/cmake")
  endif()

  FetchContent_MakeAvailable(rsfsc_log)

  if(version_check)
    find_package(RSFSCLog ${version_check} REQUIRED)
  else()
    find_package(RSFSCLog REQUIRED)
  endif()
  message(STATUS "RSFSCLog found, version: ${RSFSCLog_VERSION}")
  message(STATUS "RSFSCLog include dirs: ${RSFSCLog_INCLUDE_DIRS}")
endif()
