﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef RS_SERIAL_PORT_DRIVER_H
#define RS_SERIAL_PORT_DRIVER_H

#include <deque>
#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#include <vector>

#include <boost/asio/deadline_timer.hpp>
#include <boost/asio/io_service.hpp>
#include <boost/asio/serial_port.hpp>
namespace robosense
{
namespace lidar
{
class SerialPortDriver
{
public:
  enum BaudRate
  {
    BAUD_RATE_1200   = 1200,
    BAUD_RATE_4800   = 4800,
    BAUD_RATE_9600   = 9600,
    BAUD_RATE_19200  = 19200,
    BAUD_RATE_38400  = 38400,
    BAUD_RATE_57600  = 57600,
    BAUD_RATE_115200 = 115200
  };
  enum FlowControl
  {
    FLOW_CONTROL_NONE,
    FLOW_CONTROL_SOFTWARE,
    FLOW_CONTROL_HARDWARE
  };
  enum Parity
  {
    PARITY_NONE,
    PARITY_ODD,
    PARITY_EVEN
  };
  enum StopBits
  {
    STOP_BITS_ONE,
    STOP_BITS_ONEPOINTFIVE,
    STOP_BITS_TWO
  };
  enum CharacterSize
  {
    CHARACTER_SIZE_5 = 5,
    CHARACTER_SIZE_6 = 6,
    CHARACTER_SIZE_7 = 7,
    CHARACTER_SIZE_8 = 8
  };
  explicit SerialPortDriver();
  ~SerialPortDriver();
  bool openSerial(const std::string& _port_name,
                  const BaudRate _baud_rate,
                  const FlowControl _flow_control,
                  const Parity _parity,
                  const StopBits _stop_bits,
                  const CharacterSize _character_size);
  bool closeSerial();
  bool isConnect() const;
  bool write(const std::vector<uint8_t>& _data, const std::size_t _msec = 100);
  bool readAll(std::deque<char>& _data);
  void setMaxBufferSave(const std::size_t _max_size);
  void setMaxBufferReadOnce(const std::size_t _max_size);
  void regDealDataCallback(const std::function<void(std::deque<char>&)>& _callback);

private:
  explicit SerialPortDriver(SerialPortDriver&&)      = delete;
  explicit SerialPortDriver(const SerialPortDriver&) = delete;
  SerialPortDriver& operator=(SerialPortDriver&&) = delete;
  SerialPortDriver& operator=(const SerialPortDriver&) = delete;

  void threadReadMonitor();
  void run(std::chrono::steady_clock::duration _timeout);

  std::unique_ptr<boost::asio::serial_port> ptr_serial_port_;
  std::unique_ptr<boost::asio::io_service> ptr_io_service_;
  std::unique_ptr<std::thread> ptr_thread_;

  // FIFO buffer to save read data, readAll and _callback function will consume it
  volatile std::size_t max_buffer_save_;
  // try to read data from serial port no more than max_buffer_read_ at once
  volatile std::size_t max_buffer_read_;
  std::atomic<bool> flag_thread_run_ { false };
  std::deque<char> read_buffer_;
  std::mutex mutex_read_buffer_;
  std::vector<std::function<void(std::deque<char>&)>> vec_deal_data_cb_;
  struct flock file_lock_;
  int file_id_ = -1;
};

}  // namespace lidar
}  // namespace robosense

#endif  // _RS_SERIAL_PORT_DRIVER_H_
