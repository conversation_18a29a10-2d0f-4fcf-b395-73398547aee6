﻿/******************************************************************************
* Copyright 2020 RoboSense All rights reserved.
* Suteng Innovation Technology Co., Ltd. www.robosense.ai

* This software is provided to you directly by RoboSense and might
* only be used to access RoboSense LiDAR. Any compilation,
* modification, exploration, reproduction and redistribution are
* restricted without RoboSense's prior consent.

* THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
* WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
* DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.
*****************************************************************************/
#ifndef MES_WIDGET_H
#define MES_WIDGET_H

#include "data_struct.h"
#include "rsfsc_utils/csv_utils.h"
#include "widget_log_setting.h"
#include <QObject>

using LogTestStatus = robosense::lidar::rsfsc_lib::LogTestStatus;

class WidgetMes : public robosense::lidar::rsfsc_lib::WidgetLogSetting
{
  Q_OBJECT

public:
  explicit WidgetMes(QWidget* _parent = nullptr);
  WidgetMes(const WidgetMes&) = delete;
  WidgetMes(WidgetMes&&)      = delete;
  WidgetMes& operator=(const WidgetMes&) = delete;
  WidgetMes& operator=(WidgetMes&&) = delete;
  ~WidgetMes() override;
  void showWidget();

  bool checkMesIsOK(int _index, QString& _data_path, QString& _result_path, QString& _temp_path);
  void finishProcess(const int _index,
                     const LogTestStatus& _log_status,
                     const QString& _fail_label = QString::fromUtf8(""),
                     const QString& _fail_msg   = QString::fromUtf8(""));

  bool addMeasureMessage(const int _lidar_index,
                         const QString& _name,
                         const double _data,
                         const robosense::lidar::rsfsc_lib::MeasureDataType _data_type);
  bool addMeasureMessage(const int _lidar_index, const QString& _name, const std::string& _data);
  bool addMeasureMessage(const int _lidar_index,
                         const robosense::lidar::LimitInfo& _limit_info,
                         const QVariant& _value);

  static bool checkWithLimit(const robosense::lidar::LimitInfo& _limit_info, const QVariant& _value);
  static QString dataToString(const QVariant& _value);
  static QString dataToString(const QVariant& _value, const QVariant::Type& _type);

  void closeEvent(QCloseEvent* _event) override;

private:
  std::shared_ptr<robosense::lidar::CsvUtils> limit_csv_utils_ptr_;
};

#endif  // MES_WIDGET_H
