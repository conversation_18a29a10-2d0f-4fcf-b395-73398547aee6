
# refer to https://docs.gitlab.com/ee/ci/variables/predefined_variables.html
# refer to https://docs.gitlab.com/ee/ci/variables/index.html#list-all-environment-variables
# refer to https://docs.gitlab.com/runner/configuration/advanced-configuration.html#the-runners-section
# refer to https://docs.gitlab.com/ee/ci/runners/configure_runners.html#configure-runner-behavior-with-variables
image:
  name: "***********:5000/rs_ubuntu:rsfsc_noetic"

stages:
  - build
  # - analyze
  # - utest
  - delivery

variables:
  GIT_SUBMODULE_STRATEGY: recursive
  GIT_STRATEGY: clone
  # GITLAB_USER_EMAIL: <EMAIL>
  # GITLAB_USER_NAME: HaoQChen
  # git clone path should be in ci builds dir and runner should be at least v11.10
  GIT_CLONE_PATH: $CI_BUILDS_DIR/$CI_PROJECT_NAME
  # CI_BUILDS_DIR: /home/<USER>/ this should be set in config.toml
  # CI_PROJECT_DIR: /opt
  GIT_DEPTH: 1
  GIT_SUBMODULE_DEPTH: 1

before_script:
  - git config --global --add safe.directory '*' 

build:
  stage: build
  tags:
    - ubuntu4factory_tool020
  script:
    - mkdir build && cd build && cmake ../ && make #结合工程实际在cmake命令中附带参数-DBUILD_BETA_VERSION=ON/OFF
    - cd .. && du -sh build
  artifacts:
    expire_in: 20 mins
    paths:
      - build
      # - test/*
      - release/*
      # - script/serial_bind.sh
      # - ui/LGPLv3.txt
  retry: 2
  # artifacts:
  #   paths:
  #     - release/

# check-all:
#   stage: analyze
#   allow_failure: true
#   script: 
#     - echo "check-rules-file-latest:"
#     - .githooks/pre-commit 5
#     - echo "check-code-spell job:"
#     - .githooks/pre-commit 4
#     - echo "check-identifier-naming job:"
#     - .githooks/pre-commit 2
#     - echo "check-format job:"
#     - .githooks/pre-commit 1
#     - echo "check-clang-tidy job:"
#     - .githooks/pre-commit 3
#   tags:
#     - ubuntu4factory_tool020
#   retry: 2

#为节省部分大工程的clone时间，将analyze多个job合并成一个
# check-format:
#   stage: analyze
#   script: .githooks/pre-commit 1
#   tags:
#     - ubuntu4factory_tool020
#   retry: 2

# check-clang-tidy:
#   stage: analyze
#   script: .githooks/pre-commit 3
#   tags:
#     - ubuntu4factory_tool020
#   retry: 2

# check-identifier-naming:
#   stage: analyze
#   script: .githooks/pre-commit 2
#   tags:
#     - ubuntu4factory_tool020
#   retry: 2

# check-code-spell:
#   stage: analyze
#   allow_failure: true
#   script: .githooks/pre-commit 4
#   tags:
#     - ubuntu4factory_tool020
#   retry: 2

# check-rules-file-latest:
#   stage: analyze
#   allow_failure: true
#   script: .githooks/pre-commit 5
#   tags:
#     - ubuntu4factory_tool020
#   retry: 2

# google-test:
#   stage: utest
#   script: cd build && ./test/test_main
#   tags:
#     - ubuntu4factory_tool020
#   artifacts:
#     expire_in: 20 mins
#     paths:
#       - build/*
#       - test/*
#       - release/*
#       - script/serial_bind.sh
#       - ui/LGPLv3.txt
#   retry: 2

# delivery:
#   stage: delivery
#   tags:
#     - ubuntu4factory_tool020
#   only:
#     - tags # 这个作业只会在推送标签时运行
#   script: 
#     - mkdir release
#     - cp CHANGELOG.md release/ && cp script/create_md5.sh release/ && cp script/install.sh release/ && cp -r doc/验证报告/ release/
#     - cd build #根据项目决定是否需要执行cpack操作及md5生成操作
#     - cpack
#     - cd ../release/
#     - ./create_md5.sh
#     - cd ../build/
#     - cp /tmp/delivery_script.sh ./
#     - ./delivery_script.sh $CI_COMMIT_TAG gitlab.robosense.cn/system_release/factory_tool/M1P/m1p_aging_test_release.git #执行cd脚本，第一个参数为tag信息无需变更，第二个参数为发布仓地址按照实际变更
#   retry: 2