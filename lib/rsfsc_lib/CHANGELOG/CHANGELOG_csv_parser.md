﻿# Changelog
## v1.2.2 - 2025-04-27 
### Changed
+ 修改`csv_parser`寄存器获取连续地址的数量的逻辑,不改变name的文件名称

## v1.2.1 - 2024-09-25 
### Changed
+ `LimitInfo`增加可配置参数名称后缀的功能`setNameSuffix`，便于批量使用
  
## v1.2.0 - 2024-08-01 
### Added
+ 增加`LidarAbnormalMonitor`类为`LimitInfo`类的友元类
  
### Changed
+ 修改`LimitInfo`类构造函数为私有，禁止用户代码调用`LimitInfo`类构造函数，所有`LimitInfo`类实例化必须通过`CsvParser`类加载配置文件实现
+ 修改`LimitInfo`类的`name_`和`unit_`属性为私有，禁止用户代码对`LimitInfo`类的`name_`和`unit_`进行修改
+ 修改配置文件解析规则，`LimitInfo`新增文本类型支持，须将`unit`定义为`text`，且`LSL`与`USL`必须一致，否则配置文件解析失败将退出进程，文本类型阈值属性为`limit_text`
+ 修改配置文件校验规则，加载配置文件时对`LimitInfo`的`name`和`unit`属性进行非法字符校验，仅支持a-z A-Z 0-9 -#_. 等，存在非法字符将退出进程，备注信息必须不少于4个英文字符（对应中文字符则为不少于2个字符），建议在不明确备注信息时写TODO便于后期完善

## v1.1.3 - 2023-04-19
### Fixed
- 修复csv_parser使用`getAllLimitInfo`、`getAllRegisterInfo`等时，获取的`is_ok`变量一直是`false`的bug

## v1.1.2 - 2022-11-15
### Added
- 增加CSV文件读取时格式校验，并判定起始行开始读取，文件加载失败或格式校验失败将退出进程
 
## v1.1.1 - 2022-08-03
### Added
- 增加与MEMSTCP的配合，可以直接返回对应索引的地址和值供MEMSTCP直接调用
### Changed
- 删除read_error_str_和find_error_str_，已有RSFSCLog，不再重复输出

## v1.1.0 - 2022-03-23
### Added
- 增加limit中的备注选项，可从limit的csv中获取到备注的字符串

### Changed
- 将寄存器的模式从012三种模式修改为不限制的字符串，可以有任意多种模式
- 将getAllRegisterInfo由返回`std::map`修改为`std::multimap`

## v1.0.0 - 2021-09-06

- 统一寄存器文档的格式，规范
- 实现从CSV解析寄存器的名称，地址，读写模式，相关数值，阈值信息的功能
- 实现读入寄存器信息后，直接通过寄存器名称来索引其对应的地址，模式，数值等信息的功能
- 返回指定读写模式的全部寄存器信息

- 统计阈值参数文档的格式，规范
- 实现从CSV解析参数阈值功能
- 实现读入参数信息后，直接通过参数名称来索引其对应的阈值，单位等信息的功能