﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   widget_log_setting.h
 * <AUTHOR> Chen (<EMAIL>, maintainer)
 *         Andysen Shen(<EMAIL>, involved in MES)
 *         Melo Wang(<EMAIL>, involved in USER MANAGER)
 *         Sloan Xi(<EMAIL>, involved in USER MANAGER, TEST OF WINDOWS )
 *         Octopus Zhang(<EMAIL>, involved in IP POOL)
 *         Vance Huang(<EMAIL>, involved in AUTO EQUIPMENT COMMUNICATION)
 *         Luke Huang(<EMAIL>, involved in AUTO EQUIPMENT COMMUNICATION)
 *         Lennox Wang(<EMAIL>, maintainer)
 *         David Dai(<EMAIL>, involved in rsfsc_log)
 * @brief
 * @version v1.12.1
 * @date 2025-01-13
 **/
#ifndef RSFSCLIB_WIDGET_LOG_SETTING_H
#define RSFSCLIB_WIDGET_LOG_SETTING_H

#include "csv_parser.h"
#include "rsfsc_msg.h"
#include <QtCore/QDateTime>
#include <QtCore/QObject>
#include <QtCore/QString>
#include <QtWidgets/QWidget>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <sys/types.h>
#include <vector>

class QUdpSocket;

namespace robosense
{
namespace lidar
{
namespace rsfsc_lib
{
class WidgetLidarInfo;
class WidgetResultTable;
class BaseMES;
class WidgetLogSettingUI;
class IPPoolClient;
class AutoEquipmentCommunication;

#if defined(_WIN32) && !defined(DLLExport)
// NOLINTNEXTLINE(cppcoreguidelines-avoid-non-const-global-variables)
__declspec(dllimport) std::map<AskData::AskType, std::vector<AskData>> g_ask_data_init;
#else
// NOLINTNEXTLINE(cppcoreguidelines-avoid-non-const-global-variables)
extern std::map<AskData::AskType, std::vector<AskData>> g_ask_data_init;
#endif

enum CheckState
{
  CHECK_STATE_SUCCESS = 0,
  CHECK_STATE_ITEM_EMPTY,
  CHECK_STATE_PROCEDURE_ERROR,
  CHECK_STATE_PATH_NOT_SATISFY,
  CHECK_STATE_IP_POOL_ERROR,
  CHECK_STATE_CABLE_MANAGER_ERROR,
  CHECK_STATE_CHECK_CONFIG_SUM_ERROR,
  CHECK_STATE_SN_IS_NOT_UNIQUE_ERROR
};

enum LogTestStatus
{
  LOG_TEST_STATUS_PASS = 0,  ///< all successfully
  LOG_TEST_STATUS_FAIL,      ///< lidar itself is not satisfy requirement
  LOG_TEST_STATUS_ABORT,  ///< user terminal or some accident happen, but program can not judge if lidar is good or bad
  /// environment require for test is not ready, for example, rotator connect failed, equipment relay controller is not connectable
  /// when set to LOG_TEST_STATUS_NOT_READY and call finishProcess, it will not upload MES, just call auto equipment
  LOG_TEST_STATUS_NOT_READY
};

enum MeasureDataType
{
  MEASURE_DATA_TYPE_FLOAT = 0,
  MEASURE_DATA_TYPE_HEX,
  MEASURE_DATA_TYPE_INT
};

enum class CheckLimitState
{
  LOWER,
  WITHIN,
  UPPER,
  UNKNOWN
};

enum LightType
{
  LIGHT_TYPE_RED,
  LIGHT_TYPE_YELLOW,
  LIGHT_TYPE_GREEN,
  LIGHT_TYPE_BUZZER
};

enum class AutoEquipVersion
{
  V101,
  V102
};

class WidgetLogSetting : public QWidget
{
  Q_OBJECT
public:
  /**
   * @brief     Construct a new Widget Log Setting object
   * 
   * @param     _parent              usually is your MainWindow
   * @param     _auto_equip_version  the version of auto equipment, default is V101
   */
  explicit WidgetLogSetting(QWidget* _parent                           = Q_NULLPTR,
                            const AutoEquipVersion _auto_equip_version = AutoEquipVersion::V101);
  explicit WidgetLogSetting(WidgetLogSetting&&)      = delete;
  explicit WidgetLogSetting(const WidgetLogSetting&) = delete;
  WidgetLogSetting& operator=(WidgetLogSetting&&) = delete;
  WidgetLogSetting& operator=(const WidgetLogSetting&) = delete;
  ~WidgetLogSetting() override;
  /*vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv global settings vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv*/
  /**
   * @brief     init of a WidgetLogSetting
   * @note      you must call this function before MainWindow constructor, so it can catch the crash of MainWindow's constructor and check the USB Key result
   * 
   * @param     _argc              argc of int main(argc, argv) function
   * @param     _argv              argv of int main(argc, argv) function
   * @param     _key_name          the key name of checking USB key, empty means dont check. If check failed, program will exit
   * @param     _program_name        name of the main program that call create WidgetLogSetting 
   * @param     _program_unique_code unique code of your program, you can apply one from
   *                                 http://gitlab.robosense.cn/system_knowledge/teamwork/factory_software_rule/blob/master/MEMS导产软件名称及编号规范.md
   * @param     _major              must within [0, 25]
   * @param     _minor              must within [0, 25]
   * @param     _patch              must within [0, 25]
   * @param     _tweak              this usually is the date release
   * @param     _is_debug_mode      true: 管理员以上权限mes的相关勾选才允许被取消; 软件窗体颜色警告软件为调试版本; false: 所有模式下mes相关设置都默认勾选; 警告取消
   * @param     _cable_manage_status 线束管理，参考CableManageStatus，默认为ENABLE
   * @param     _mes_type           数据交互类型，参考MESType类型
   * 
   * @return    bool               check USB Key result
   */
  static void init(int _argc,
                   char** _argv,
                   const std::string& _key_name,
                   const std::string& _program_name,
                   const std::string& _program_unique_code,
                   unsigned int _major,
                   unsigned int _minor,
                   unsigned int _patch,
                   unsigned int _tweak,
                   const bool _is_debug_mode                    = false,
                   const CableManageStatus _cable_manage_status = CableManageStatus::ENABLE,
                   const MESType _mes_type                      = MESType::MES_TYPE_ROBOSENSE);

  /**
   * @brief     Get the Widget Log Version
   *
   * @return    std::string    version of this lib
   */
  static std::string version();
  /**
   * @brief     Add the directory of the file or a folder to be checked, if a folder is added, all files below the folder will be check
   * 
   * @param _dir     the dir of the file or the dir of the folder to be checked
   */
  void addCheckSumDir(const std::string& _dir);
  /**
   * @brief     set check sn regex or not
   * 
   * @param     _is_check           if is_check set to false, same as 不校验SN项目 set to ALL
   */
  void setCheckSNRegex(bool _is_check);
  /**
   * @brief     Get the Log Path, you can change RSFSCLog's path to here 
   * 
   * @return    QString         log path contain / like /home/<USER>/log/   
   */
  QString getLogPath() const;
  /**
   * @brief     Set the Serial Number
   *
   * @param     _widget_lidar_info  the corresponding WidgetLidarInfo in UI
   */
  void registerWidgetLidarInfo(WidgetLidarInfo* _widget_lidar_info);

  /**
   * @brief     Set the result table
   *
   * @param     _widget_result_table  the WidgetResultTable in UI
   */
  void registerWidgetResultTable(WidgetResultTable* _widget_result_table);

  /**
   * @brief     Set the Widget Lidar Info object
   * 
   * @param     _lidar_index lidar info控件索引,索引从1开始
   * @param     _lidar_sn 需要设置lidar序列号
   * @return    true 设置成功
   * @return    false 设置失败
   */
  bool setLidarSN(const size_t _lidar_index, const std::string& _lidar_sn);

  /*vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv lidar's settings vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv*/
  /**
   * @brief     check if setting in UI is all been set, not guarantee set right
   *            and if procedure check type is not empty, also check if procedure before is finished
   *
   * @param     _lidar_index       the corresponding index you register by registerWidgetLidarInfo
   * @param     _data_path         you should put all your data in this path, it contain / like /home/<USER>/rsdata/MS1001_001_20220326_175200/
   * @param     _temp_path         该路径存储的文件用于标定测试过程存放过渡中转数据
   * @param     _result_path       It is similar as _data_path. But data path should only contain debug data, cause it only save for 2 years, 
   *                               result path should contain the data that very important and will safe for at least 15 years.
   *                               _data_path is same as _result_path for now, but it will be different some day
   * @retval    CHECK_STATE_SUCCESS          all successfully, _data_path and _result_path have be created, mean you can start the lidar's process
   * @retval    CHECK_STATE_ITEM_EMPTY             you miss some setting in code or ui
   * @retval    CHECK_STATE_PROCEDURE_ERROR        check procedure from mes failed
   * @retval    CHECK_STATE_PATH_NOT_SATISFY       path is not writeable or create _data_path or _result_path failed
   * @retval    CHECK_STATE_IP_POOL_ERROR          some error about IP pool
   * @retval    CHECK_STATE_CABLE_MANAGER_ERROR    cable used too many times or sn not set
   * @retval    CHECK_STATE_CHECK_CONFIG_SUM_ERROR config file md5 checksum failed
   */
  CheckState checkAllState(const quint32 _lidar_index, QString& _data_path, QString& _temp_path, QString& _result_path);

  /**
   * @brief    `ASK_TYPE_CALIB_ASK_CALIB_DATA`：根据SN查询对应工序过站信息(包含整机段、振镜段、模具段)
               `ASK_TYPE_CALIB_ASK_MODULE_DATA`：整机段查模组线工站的数据
               `ASK_TYPE_CALIB_ASK_MIRROR_DATA`：整机段查振镜工站的数据
               `ASK_TYPE_CALIB_ASK_RECEIVE_MODULE_DATA`：整机段查接收模组段数据
               `ASK_TYPE_CALIB_ASK_EMITTING_MODULE_DATA`：整机查发射模组段数据
               `ASK_TYPE_CALIB_ASK_MATERIAL_DATA`：根据SN查询料号信息
               `ASK_TYPE_CALIB_ASK_CUSTOMER_SN`：整机段查客户SN信息
               `ASK_TYPE_CALIB_ASK_SN_TO_SUB_SN_DATA`: 通过整机SN查询指定子件的指定测试信息​
               `ASK_TYPE_CALIB_ASK_SUB_SN_TO_DATA`: 通过子件SN查询指定测试信息(未装配的子件SN)
               `ASK_TYPE_KMS_AES_EFUSE_KEY: 查询AES_EFUSE_KEY密钥信息
               `ASK_TYPE_KMS_ENABLE_SECURE_TAG`: 查询ENABLE_SECURE_TAG密钥信息
               `ASK_TYPE_KMS_SIGN_CERT`: KMS签名
   * 
   * @param _lidar_index 当前lidar info控件索引,索引从1开始
   * @param _ask_data 请求数据集
   * @return true 
   * @return false 
   */
  bool requireData(const quint32 _lidar_index,
                   std::map<AskData::AskType, std::vector<AskData>>& _ask_data = g_ask_data_init);
  /**
   * @brief      when you set checkAllState's _is_bound_ip to true, you should try to set lidar's ip and
   *             call this function to confirm bound state, otherwise, IP pool server will not bound ip with sn
   *
   * @param     _lidar_index       the corresponding index you register by registerWidgetLidarInfo
   * @param      _is_bound_ip_successful    bound successful or not
   * @retval     true                       communicate with IP pool server successfully
   * @retval     false                      communicate with IP pool server failed, you should stop program
   */
  bool confirmBoundIPState(const quint32 _lidar_index, const bool _is_bound_ip_successful);
  /**
   * @brief     Set the Firmware Revision
   *
   * @note  just push qint32, but not QString, because some body will set a error QString format
   *
   * @param     _lidar_index       the corresponding index you register by registerWidgetLidarInfo
   * @param     _ps_version        ps version read from lidar with MEMSTCP
   * @param     _pl_version        pl version read from lidar with MEMSTCP
   */
  void setFirmwareRevision(const quint32 _lidar_index, const qint32 _ps_version, const qint32 _pl_version);
  /**
   * @brief     Set the Platform, e.g. M1 or M1P or M2
   *
   * @param     _lidar_index       the corresponding index you register by registerWidgetLidarInfo
   * @param     _platform          the platform name, default is ''
   */
  void setPlatform(const quint32 _lidar_index, const QString& _platform);
  /**
   * @brief     Set the Test Status
   * 
   * @param     _lidar_index       the corresponding index you register by registerWidgetLidarInfo
   * @param     _ts                 test status
   * @param     _fail_label         you must set this if ts is not LOG_TEST_STATUS_PASS, no more than 25 character
   * @param     _fail_msg           you must set this if ts is not LOG_TEST_STATUS_PASS, no limit
   */
  void setTestStatus(const quint32 _lidar_index,
                     const LogTestStatus _ts,
                     const QString& _fail_label = QString::fromUtf8(""),
                     const QString& _fail_msg   = QString::fromUtf8(""));
  /**
   * @brief     add measure message for LimitInfo
   * 
   * @param     _lidar_index       the corresponding index you register by registerWidgetLidarInfo
   * @param     limit_info         LimitInfo from CsvParser
   * @param     data               data from current test
   * @param     data_type          WidgetLogSetting will output your data according data_type 
   * @retval    true               _data is within the _limit_info range
   * @retval    false              _data is not within the _limit_info range
   */
  bool addMeasureMessage(const quint32 _lidar_index,
                         const LimitInfo& _limit_info,
                         const double _data,
                         const MeasureDataType _data_type = MEASURE_DATA_TYPE_FLOAT);

  /**
   * @brief     add measure message for LimitInfo
   * 
   * @param     _lidar_index       the corresponding index you register by registerWidgetLidarInfo
   * @param     limit_info         LimitInfo from CsvParser
   * @param     data               data from current test
   * @param     data_type          WidgetLogSetting will output your data according data_type 
   * @retval    true               _data is within the _limit_info range
   * @retval    false              _data is not within the _limit_info range
   */
  bool addMeasureMessage(const quint32 _lidar_index, const LimitInfo& _limit_info, const std::string& _data);

  /**
   * @brief     call this to save log file to path when process finish
   *
   * @param     lidar_data_folder  where current single lidar's saved path, it should contain an result folder
   * @param     error_msg          when some error happen, log can not be write to path, this will be set
   * @param     _unbound_ip_pool   whether to unbound ip pool, only need in the last not autosar process
   * @retval    true               write successfully, 
   * @retval    false              if some msg required did not set or msg length is longer than require
   **/
  bool finishProcess(const quint32 _lidar_index, QString& _error_msg);

  /**
   * @brief 上传mes设备状态信息
   * 
   * @param _status 设备状态，参考EquipmentStatus
   * @param _error_code 错误代码，参考EquipmentErrorCode
   * @return true 
   * @return false 
   */
  bool uploadEquipmentInfo(const EquipmentStatus _status, const EquipmentErrorCode _error_code = ER_CODE_OTHER);

  /**
   * @brief 上传KMS的使能结果
   * 
   * @param _lidar_index 当前lidar info控件索引,索引从1开始
   * @param _items 输出的数据，key/value形式，e.g. "AesEfuseKeyRes":"7101F039"
   * @return true 
   * @return false 
   */
  bool uploadKmsEnableResult(const quint32 _lidar_index, const std::map<std::string, std::string>& _items);

  /**
   * @brief     设置program的唯一编码
  **/
  static void setProgramUniqueCode(const std::string& _program_unique_code);

  /// get info in WidgetLogSetting UI
  MESType getMESType() const;           // MES类型
  QString getCustomer() const;          // 客户
  QString getBoardStyle() const;        // 板式
  QString getTesterName() const;        // 电脑名
  QString getTestProcess() const;       // 工站名
  QString getAssemblyNumber() const;    // 组件编号
  QString getAssemblyRevision() const;  // 组件版本
  QString getLot() const;               // 产品批次
  QString getFixture() const;           // 治具编号
  QString getLine() const;              // 线体名称
  int getSite() const;                  // 厂区编号
  QString getOperatorID() const;        // 操作员ID
  bool isCheckProcedure() const;        // 是否勾选 MES过站
  bool isRequireData() const;           // 是否勾选 数据请求
  bool isUploadData() const;            // 是否勾选 MES上传
  bool isUseIPPool() const;             // 网络设置--使用IP池
  bool isUseUnifiedSN() const;          // 高级设置--使用统一SN
  QString getStartTime(const quint32 _lidar_index) const;
  static bool checkWithinLimit(const LimitInfo& _limit_info,
                               const double _data,
                               const MeasureDataType _data_type = MEASURE_DATA_TYPE_FLOAT);
  static bool checkWithinLimit(const LimitInfo& _limit_info, const std::string& _data);
  static bool checkWithinLimit(const std::string& _data, const std::string& _limit);
  static bool checkWithinLimit(const std::string& _data, const std::string& _min_th, const std::string& _max_th);
  static bool checkWithinLimit(const double _data,
                               const double _lower_limit,
                               const double _upper_limit,
                               const MeasureDataType _data_type,
                               CheckLimitState& _state);
  static std::string measureData2String(const MeasureDataType _type,
                                        const CheckLimitState _limit_state,
                                        const double _data);
  bool boundLidarIP(const quint32 _lidar_index);
  bool unboundLidarIP(const quint32 _lidar_index);
  QString getAndonRelayPortName();
  std::map<LightType, quint8> getLightPinInfo();
  EquipmentStatus getEquipmentStatus() const;

Q_SIGNALS:
  /**
   * @brief     when login info changed in WidgetLogSetting UI, this signal will be emitted, connect to your slot, and
   *            change you widget state according to _user_authority
   * 
   * @param     _user_authority    the new user authority
   */
  void signalAuthorityUpdate(robosense::lidar::rsfsc_lib::UserAuthority* _user_authority);
  void signalAbort(const quint32 _lidar_index);

  /**
   * @brief this is a private signal, it will be used internal, send a signal to auto equipment communication
   * 
   * @param _dut_index the be tested devices's index, depend on registerDUT, start from 1
   * @param _state the test result is FinishState::FAIL or FinishState::PASS
   * @param _ps not used, it can make sure this signal is private
   * @return true 
   * @return false 
   */
  bool signalFinish(const quint32 _dut_index, const robosense::lidar::rsfsc_lib::FinishState _state, QPrivateSignal);

  /**
   * @brief this is a private signal, it will connect to auto equipment communication
   * 
   * @param _ip 
   * @param _port 
   * @return true 
   * @return false 
   */
  bool signalConnectToAutoEquipment(const QString& _ip, const int& _port, QPrivateSignal);

  void signalUpdateEquipmentStatus(const int _status);

  /**
   * @brief 发送信号灯状态
   * 
   * @param _light_type  灯状态
   */
  void signalUpdateLightStatus(const robosense::lidar::rsfsc_lib::LightType _light_type);

private Q_SLOTS:
  void slotMESTypeUpdate();
  void slotCheckSNRegexUpdate();
  void slotOperatorIDInputFinished();
  void slotUserAuthorityUpdate(const int _ual);
  void slotAutoEquipmentBegin(const quint32 _dut_index, const QString& _sn);
  void slotResetFileCheckSumValue();
  void slotUploadEquipmentInfo(const EquipmentStatus _status);

private:
  friend class WidgetLogSettingTest;
  // void insertFieldInfo(const std::string& name, const char id, const int len, const bool is_required);
  struct MeasureData
  {
    explicit MeasureData(const std::string& _label,  // NOLINT(bugprone-easily-swappable-parameters)
                         const double _data,         // NOLINT(bugprone-easily-swappable-parameters)
                         const double _lower_limit,  // NOLINT(bugprone-easily-swappable-parameters)
                         const double _upper_limit,  // NOLINT(bugprone-easily-swappable-parameters)
                         const std::string& _unit,
                         const MeasureDataType _data_type,
                         const std::string& _detail) :
      label(_label),
      data(_data),
      lower_limit(_lower_limit),
      upper_limit(_upper_limit),
      unit(_unit),
      data_type(_data_type),
      detail(_detail)
    {}
    MeasureData(MeasureData&& _md) = default;

    MeasureData(const MeasureData& _md) = default;

    ~MeasureData() = default;

    MeasureData& operator=(const MeasureData& _other) = default;

    MeasureData& operator=(MeasureData&& _other) = default;
    std::string label;
    double data;
    double lower_limit;
    double upper_limit;
    std::string unit;
    MeasureDataType data_type;
    std::string detail;
  };

  struct MeasureDataString
  {
    // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
    explicit MeasureDataString(const std::string& _label,
                               const std::string& _data,
                               const std::string& _min_th,
                               const std::string& _max_th,
                               const std::string& _detail) :
      label(_label), data(_data), min_th(_min_th), max_th(_max_th), detail(_detail)
    {}
    MeasureDataString(MeasureDataString&& _md) = default;

    MeasureDataString(const MeasureDataString& _md) = default;

    MeasureDataString& operator=(const MeasureDataString&) = default;

    MeasureDataString& operator=(MeasureDataString&&) = default;

    ~MeasureDataString() = default;
    std::string label;
    std::string data;
    std::string min_th;
    std::string max_th;
    std::string detail;
  };

  struct LidarData
  {
    WidgetLidarInfo* widget_lidar_info { Q_NULLPTR };
    QString version_str;
    QDateTime start_date;
    std::vector<MeasureData> measure_data;
    std::vector<MeasureDataString> measure_data_string;
    LogTestStatus ts { LOG_TEST_STATUS_NOT_READY };
    QString fail_label;
    QString fail_msg;
    QString platform;
    WidgetResultTable* widget_result_table { nullptr };
  };
  mutable std::map<quint32, LidarData> map_lidar_data_;

  void updateAllWidgetState();
  void closeEvent(QCloseEvent* _event) override;
  void updateUIData2MESObject();
  bool checkFreeSpace(const int _lidar_index);
  bool checkIPPool(const int _lidar_index, const bool _is_bound_ip);
  bool checkConfigFileCheckSum();
  bool checkCableUseTimes(const quint32 _lidar_index);
  bool checkProcedureState(const quint32 _lidar_index);
  bool checkItemEmpty(const quint32 _lidar_index);
  bool checkLidarSNIsUnique(const quint32 _lidar_index);
  QString setDataToMESObjectWhenFinish(const quint32 _lidar_index);
  QString uploadDataToServer(const quint32 _lidar_index);
  QString writeMeasureData(const quint32 _lidar_index,
                           std::string& _test_data_file_path,
                           bool& _is_exist_fail_item,
                           std::string& _sn,
                           std::string& _start_time);
  QString notifyUploadProcess();
  void saveSetting();
  QString getDataPath() const;
  QString getTempPath() const;
  QString getResultPath() const;
  QString getMESPath() const;
  QString getSummaryPath() const;
  QString getJabilCollectPath() const;
  bool makeSavePath();
  bool getFileMD5(const std::string& _filename, std::string& _md5);
  std::map<std::string, std::string> getAllFileMD5();
  /**
   * @brief     reset all the data you set
   */
  void resetFieldStatus(const quint32 _lidar_index);
  void setSoftwareVersionPrivate();
  /**
   * @brief     start process and get start time, you need to call either QString startProcess()
   *            or void startProcess(const QDateTime& date_time)
   *
   * @return    QString       start time, you should use it to create your folder
   */
  QString startProcess(const quint32 _lidar_index);
  /**
   * @brief     start process with your date time
   *
   * @param     date_time
   */
  //void startProcess(const quint32 _lidar_index, const QDateTime& date_time);

  /**
   * @brief     add measure message for key indicator
   *
   * @param     label              measure label, no more than 25 character
   * @param     data               measure data, no more than 25 character
   * @param     msg                note for label, no limit
   * @retval    true               _data is within the _limit_info range
   * @retval    false              _data is not within the _limit_info range
   * @deprecated We recommend use LimitInfo instead of raw limit data
   */
  // clang-format off
  // [[deprecated("We recommend use LimitInfo instead of raw limit data")]]
  bool addMeasureMessage(const quint32 _lidar_index, const std::string& _label,
                         const double _data,
                         const double _lower_limit,
                         const double _upper_limit,
                         const std::string& _unit,
                         const MeasureDataType _data_type = MEASURE_DATA_TYPE_FLOAT,const std::string& _detail = "");
  // clang-format on

  static void initDump();
  static void sendToNetworkMapper(const std::string& _file_path, const std::string& _file_new_name = "");
  static void sendHttpNotifyInfo(const std::string& _type, const std::string& _msg);
  static void exitAfterSomeTime(const std::size_t _second);

  static QString key_name_;      // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)
  static QString program_name_;  // NOLINT
  static QString
    qsetting_name_;  //qsetting name for log and ui data // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)
  static QString
    qsetting_name_check_sum_;  // qsetting name for saving config file check sum // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)
  static QString program_unique_code_;  // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)

  std::unique_ptr<BaseMES> ptr_mes_;
  std::unique_ptr<IPPoolClient> ptr_ip_pool_;
  std::unique_ptr<AutoEquipmentCommunication> ptr_auto_equipment_client_;
  WidgetLogSettingUI* pimpl_ui_;
  QString error_msg_;
  QUdpSocket* ptr_udp_upload_data_;
  quint32 current_lidar_index_;                 // only update in checkAllState and finishProcess
  static unsigned int software_version_major_;  // NOLINT
  static unsigned int software_version_minor_;  // NOLINT
  static unsigned int software_version_patch_;  // NOLINT
  static unsigned int software_version_tweak_;  // NOLINT

  static int argc_;              // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)
  static char** argv_;           // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)
  static std::mutex mes_mutex_;  // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)
  std::vector<std::string> check_sum_dir_vec_;

  static WidgetLogSetting* latest_widget_log_setting_;  // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)
  static bool is_debug_mode_;                           // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)
  static CableManageStatus cable_manage_status_;        // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)

  EquipmentStatus current_equipment_status_;
};
}  // namespace rsfsc_lib
}  // namespace lidar
}  // namespace robosense

#endif  // RSFSCLIB_WIDGET_LOG_SETTING_H
