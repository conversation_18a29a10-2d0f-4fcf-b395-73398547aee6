﻿## Changelog
## v2.3.0 - 2025-03-26
### Feat
- 优化连接与断开连接的逻辑

## v2.2.4 - 2023-11-28
### Feat
- 增加获取人眼安全状态
- 添加`Helios`的读取`CMD`的包

## v2.2.3 - 2023-11-27
### Feat
- 增加多寄存器读写的一键测试功能
- 增加多寄存器读写的功能

### Fixed
- 修复解析错误时返回true的情况
- 修复`mech`修改网络参数，未收到响应包返回`false`的问题
- 降低混合寄存器的连续读取速度


## v2.1.2 - 2023-09-07

- 增加ping功能
- 增加中断功能

## v2.1.1 - 2023-08-23

- 增加验证MSOP包的有效性

## v2.1.0 - 2023-08-23

- 增加Ruby4.0和BPearl4.0的pack功能

## v2.0.1 - 2023-08-22

- 增加helios的等待启动完成功能

## v2.0.0 - 2023-08-18

- 增加协议的解析器parser，更新协议处理框架

## v1.2.1 - 2023-07-26

- 增加ruby寄存器读写功能

## v1.2.0 - 2023-07-12

- 整体调整为`适配器`的模式
- 添加ruby4协议的打包
- 兼容了过去的代码，使用者不会因为此次改动造成编译出错等问题

## v1.1.0 - 2023-07-03

### Added

- 添加`CI/CD`检查项
- 添加子模块`google test`测试项
- `CMake`添加`Debug`与`Release`版本
- `CMake CTest`添加`google test`
- 更新`rsfsc_log.h`的文件至`v1.2.0`版本