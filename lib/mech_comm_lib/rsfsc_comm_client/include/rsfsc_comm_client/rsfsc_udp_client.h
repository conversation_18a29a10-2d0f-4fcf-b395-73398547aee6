#ifndef RSFSC_UDP_CLIENT_H
#define RSFSC_UDP_CLIENT_H

#include "rsfsc_comm_client/rsfsc_comm_client.h"
#include <boost/asio/ip/udp.hpp>

namespace robosense
{
namespace lidar
{

class RsfscUdpClient : public RsfscCommClient
{
public:
  RsfscUdpClient(const std::string& _host, uint16_t _port);
  ~RsfscUdpClient() override;

  bool connect() override;
  bool disconnect() override;
  bool isConnected() const override;

  std::size_t write(const std::vector<uint8_t>& _data) override;
  std::size_t readSome(std::vector<uint8_t>& _data) override;
  std::vector<uint8_t> readSomeTimeout(uint32_t _timeout_ms) override;

  void asyncWrite(const std::vector<uint8_t>& _data, WriteHandler _handler) override;
  void asyncRead(std::vector<uint8_t>& _data, ReadHandler _handler) override;

  std::vector<uint8_t> request(const std::vector<uint8_t>& _request, uint32_t _timeout_ms) override;

private:
  std::string host_;
  uint16_t port_;
  std::unique_ptr<boost::asio::ip::udp::socket> socket_;
  boost::asio::ip::udp::endpoint remote_endpoint_;
  bool is_connected_;
};

}  // namespace lidar
}  // namespace robosense

#endif  // RSFSC_UDP_CLIENT_H
