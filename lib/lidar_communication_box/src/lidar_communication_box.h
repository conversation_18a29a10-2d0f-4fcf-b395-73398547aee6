﻿/******************************************************************************
* Copyright 2022 RoboSense All rights reserved.
* Suteng Innovation Technology Co., Ltd. www.robosense.ai

* This software is provided to you directly by RoboSense and might
* only be used to access RoboSense LiDAR. Any compilation,
* modification, exploration, reproduction and redistribution are
* restricted without RoboSense's prior consent.

* THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
* WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
* DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.
*****************************************************************************/
#ifndef LIDARCOMMUNICATIONBOX_H
#define LIDARCOMMUNICATIONBOX_H

#include "serial_port_driver.h"
#include <boost/asio.hpp>
#include <deque>
#include <iostream>

namespace robosense
{
namespace lidar
{
class LidarCommunicationBox
{
public:
  LidarCommunicationBox();

public:
  /**
   * @brief     connect box
   *
   * @param     _port_name          serial port name
   * @param     _password           computer password,used to grant permission to a serial port
   * @retval    true                connect box successfully
   * @retval    false               some error happen
  **/
  bool connect(std::string _port_name, std::string _password);
  bool connect(std::string _port_name);

  /**
   * @brief     box connection state
   *
   * @retval    true                connected
   * @retval    false               unconnected
  **/
  bool isConnect() { return is_connect_; }

  /**
   * @brief     disconnect box
   *
   * @retval    true                disconnect box successfully
   * @retval    false               some error happen
  **/
  bool disconnect();

  /**
   * @brief     get box firmware version
   *
   * @param     _version            return box firmware version
   * @retval    true                get box firmware version successfully
   * @retval    false               some error happen
  **/
  bool getFirmwareVersion(std::string& _version);

  /**
   * @brief     get lidar input electricity
   *
   * @param     _lidar_idx          lidar index, index=1~8
   * @param     _electricity        return lidar input electricity
   * @retval    true                get lidar input electricity successfully
   * @retval    false               some error happen
  **/
  bool getElectricity(int _lidar_idx, uint32_t& _electricity);

  /**
   * @brief     get lidar input voltage
   *
   * @param     _lidar_idx          lidar index, index=1~8
   * @param     _voltage            return lidar input voltage
   * @retval    true                get lidar input voltage successfully
   * @retval    false               some error happen
  **/
  bool getVoltage(int _lidar_idx, uint32_t& _voltage);

  /**
   * @brief     get lidar input _sync
   *
   * @param     _lidar_idx          lidar index, index=1~8
   * @param     _voltage            return lidar input voltage
   * @retval    true                get lidar input voltage successfully
   * @retval    false               some error happen
  **/
  bool getSync(int _lidar_idx, uint32_t& _sync);

  /**
   * @brief     turn on\off lidar
   *
   * @param     _lidar_idx          lidar index, index=1~8, when _lidar_idx=0xff, control all lidar
   * @param     _is_turn_on         =true, turn on \ =false, turn off
   * @retval    true                switch lidar power successfully
   * @retval    false               some error happen
  **/
  bool switchLidarPower(int _lidar_idx, bool _is_turn_on);

  /**
   * @brief     turn on\off wake signal
   *
   * @param     _lidar_idx          lidar index, index=1~8, when _lidar_idx=0xff, control all lidar
   * @param     _is_wake            =true, turn on \ =false, turn off
   * @retval    true                switch lidar wake signal successfully
   * @retval    false               some error happen
  **/
  bool switchWakeSignal(int _lidar_idx, bool _is_wake);

  /**
   * @brief     set network standby
   *
   * @param     _lidar_idx          lidar index, index=1~8, when _lidar_idx=0xff, control all lidar
   * @param     _is_standby         =true, standby \ =false, not standby
   * @retval    true                set network standby successfully
   * @retval    false               some error happen
  **/
  bool setNetworkStandby(int _lidar_idx, bool _is_standby);

  /**
   * @brief     get lidar is exist
   *
   * @param     _lidar_idx          lidar index, index=1~8
   * @param     _is_exist           return true, lidar exist \ return false, lidar inexistence
   * @retval    true                switch lidar wake signal successfully
   * @retval    false               some error happen
  **/
  bool getLidarIsExist(int _lidar_idx, bool& _is_exist);

  /**
   * @brief     set lidar input voltage
   *
   * @param     _voltage_mv         will set votlage,_voltage_mv=9000-16000
   * @retval    true                set votlage successfully
   * @retval    false               some error happen
  **/
  bool setLidarInputVotlage(const int _voltage_mv);

private:
  void readCallBack(std::deque<char>& _data);
  bool waitReciveFinish(const uint8_t _function_code);
  bool setSerialPermission(const std::string _password);
  bool checkReturnIsError(const uint8_t _function_code, const uint8_t _lidar_index, const uint8_t _error_code);

private:
  const int LIDAR_NUM = 8;
  std::mutex mutex_;
  std::unique_ptr<SerialPortDriver> serial_port_ = nullptr;
  std::vector<uint8_t> recive_data_buffer_;
  bool recive_finish_  = false;
  uint8_t recive_size_ = 0;
  bool is_connect_     = false;
};
}  // namespace lidar
}  // namespace robosense

#endif  // POWERBOXCONTROL_H
