/* QFrame {
    background-color: rgb(255, 255, 255);
    border-radius: 10px;
    border: 1px solid rgb(100, 169, 139);
}

QPushButton {
    border: 1px solid black;
}

QRadioButton {
    border: 1px solid black;
}

QLabel {
    border: 1px solid black;
}

QGroupBox {
    border: 1px solid gray;
    margin-top: 0.5em;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
}


QWidget#MyWidget {
    border: 1px solid black;
} */