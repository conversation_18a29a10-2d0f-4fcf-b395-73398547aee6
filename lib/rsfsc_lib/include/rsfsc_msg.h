﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef RSFSCLIB_MSG_H
#define RSFSCLIB_MSG_H
#include <QtCore/QObject>
#include <QtCore/QString>
#include <QtCore/QVector>
#include <array>
#include <map>

namespace robosense
{
namespace lidar
{
namespace rsfsc_lib
{
// NOTE
/// @note  PROJECT_NOT_FIND means SN not match project's regex, you should not start your process when PROJECT_NOT_FIND return
/// @note  PROJECT_NOT_FIND always at the end, and never delete or insert a project code, you can only add a new project code just before PROJECT_NOT_FIND
enum ProjectCode
{
  PROJECT_0210 = 0,
  PROJECT_0211,
  PROJECT_0212,
  PROJECT_0213,
  PROJECT_0214,
  PROJECT_0215,
  PROJECT_0216,
  PROJECT_0217,
  PROJECT_0218,
  PROJECT_0219,
  PROJECT_0210_NA,  // 0210 no AUTOSAR
  PROJECT_021A,
  PROJECT_021B,
  PROJECT_021C,
  PROJECT_021D,
  PROJECT_021E,
  PROJECT_021F,
  PROJECT_0220,
  PROJECT_0221,
  PROJECT_0222,
  PROJECT_0223,
  PROJECT_0224,
  PROJECT_0225,
  PROJECT_0226,
  PROJECT_0227,
  PROJECT_0228,
  PROJECT_0229,
  PROJECT_0220_NA,  // M1P no AUTOSAR
  PROJECT_022A,
  PROJECT_022B,
  PROJECT_022C,
  PROJECT_022D,
  PROJECT_022E,
  PROJECT_022F,
  PROJECT_0250,
  PROJECT_0250_NA,
  PROJECT_0251,
  PROJECT_0252,
  PROJECT_0253,
  PROJECT_0254,
  PROJECT_0255,
  PROJECT_0256,
  PROJECT_0257,
  PROJECT_0258,
  PROJECT_0259,
  PROJECT_0270,
  PROJECT_0270_NA,
  PROJECT_0271,
  PROJECT_0272,
  PROJECT_0273,
  PROJECT_0274,
  PROJECT_0275,
  PROJECT_0276,
  PROJECT_0277,
  PROJECT_0278,
  PROJECT_0279,
  PROJECT_027A,
  PROJECT_027B,
  PROJECT_027C,
  PROJECT_027D,
  PROJECT_027E,
  PROJECT_027F,
  PROJECT_0303,
  PROJECT_0320,
  PROJECT_0321,
  PROJECT_0335,
  PROJECT_0350,
  PROJECT_0351,
  PROJECT_0352,
  PROJECT_0360,
  PROJECT_0600,
  PROJECT_0601,
  PROJECT_0602,
  PROJECT_0603,
  PROJECT_0604,
  PROJECT_0605,
  PROJECT_0620,
  PROJECT_0621,
  PROJECT_0800,
  PROJECT_1010,
  PROJECT_NOT_FIND
};

// NOLINTNEXTLINE(cert-err58-cpp)
const QVector<QString> G_MEMS_PROJECT_CODE_STR(
  { "0210", "0211", "0212", "0213",    "0214", "0215", "0216", "0217", "0218",     "0219", "0210_na", "021A",
    "021B", "021C", "021D", "021E",    "021F", "0220", "0221", "0222", "0223",     "0224", "0225",    "0226",
    "0227", "0228", "0229", "0220_na", "022A", "022B", "022C", "022D", "022E",     "022F", "0250",    "0250_na",
    "0251", "0252", "0253", "0254",    "0255", "0256", "0257", "0258", "0259",     "0270", "0270_na", "0271",
    "0272", "0273", "0274", "0275",    "0276", "0277", "0278", "0279", "027A",     "027B", "027C",    "027D",
    "027E", "027F", "0303", "0320",    "0321", "0335", "0350", "0351", "0352",     "0360", "0600",    "0601",
    "0602", "0603", "0604", "0605",    "0620", "0621", "0800", "1010", "not_found" });

inline QString memsProjectCode2QString(const ProjectCode _pc)
{
  if (_pc > PROJECT_NOT_FIND || _pc < PROJECT_0210)
  {
    return G_MEMS_PROJECT_CODE_STR[PROJECT_NOT_FIND];
  }
  return G_MEMS_PROJECT_CODE_STR[_pc];
}

inline ProjectCode memsProjectCodeQString2Enum(const QString& _pc_str)
{
  ProjectCode pc = PROJECT_NOT_FIND;
  for (int i = 0, project_size = G_MEMS_PROJECT_CODE_STR.size(); i < project_size; ++i)
  {
    if (_pc_str == G_MEMS_PROJECT_CODE_STR[i])
    {
      pc = ProjectCode(i);
      break;
    }
  }
  return pc;
}

enum LidarInstallPosition
{
  LIDAR_INSTALL_POSITION_FL = 0,  // front left
  LIDAR_INSTALL_POSITION_FM,      // front middle
  LIDAR_INSTALL_POSITION_FR,      // front right
  LIDAR_INSTALL_POSITION_LF,      // left front
  LIDAR_INSTALL_POSITION_LB,      // left back
  LIDAR_INSTALL_POSITION_RF,      // right front
  LIDAR_INSTALL_POSITION_RB,      // right back
  LIDAR_INSTALL_POSITION_TL,      // top left
  LIDAR_INSTALL_POSITION_TM,      // top middle
  LIDAR_INSTALL_POSITION_TR,      // top right
  LIDAR_INSTALL_POSITION_BL,      // back left
  LIDAR_INSTALL_POSITION_BM,      // back middle
  LIDAR_INSTALL_POSITION_BR,      // back right
  LIDAR_INSTALL_POSITION_NOT_FIND
};

// NOLINTNEXTLINE(cert-err58-cpp)
const QVector<QString> G_LIDAR_INSTALL_POSITION_STR({ "FL", "FM", "FR", "LF", "LB", "RF", "RB", "TL", "TM", "TR", "BL",
                                                      "BM", "BR", "not_found" });

inline QString lidarInstallPosition2QString(const LidarInstallPosition _lip)
{
  if (_lip < LIDAR_INSTALL_POSITION_FL || _lip > LIDAR_INSTALL_POSITION_NOT_FIND)
  {
    return G_LIDAR_INSTALL_POSITION_STR[LIDAR_INSTALL_POSITION_NOT_FIND];
  }
  return G_LIDAR_INSTALL_POSITION_STR[_lip];
}

inline LidarInstallPosition lidarInstallPositionQString2Enum(const QString& _lip_str)
{
  LidarInstallPosition lip = LIDAR_INSTALL_POSITION_NOT_FIND;
  for (int i = 0, position_size = G_LIDAR_INSTALL_POSITION_STR.size(); i < position_size; ++i)
  {
    if (_lip_str == G_LIDAR_INSTALL_POSITION_STR[i])
    {
      lip = LidarInstallPosition(i);
      break;
    }
  }
  return lip;
}

enum MESType
{
  MES_TYPE_JABIL = 0,
  MES_TYPE_ROBOSENSE,
  MES_TYPE_ROBOSENSE_EDI,
  MES_TYPE_ROBOSENSE_SY
};

enum class CableManageStatus
{
  DISABLE,  // 不启动进行线束管理，零部件(组件)使用
  ENABLE    // 启动进行线束管理，整机使用
};

/**
 * @brief     you can construct a object of AskData and put it in WidgetLogSetting::checkLogState, 
 *            checkLogState will ask MES for the data you need
 */
struct AskData
{
  enum AskType
  {
    ASK_TYPE_CALIB_ASK_CALIB_DATA = 0,
    ASK_TYPE_CALIB_ASK_MODULE_DATA,
    ASK_TYPE_CALIB_ASK_MIRROR_DATA,
    ASK_TYPE_CALIB_ASK_RECEIVE_MODULE_DATA,
    ASK_TYPE_CALIB_ASK_EMITTING_MODULE_DATA,
    ASK_TYPE_CALIB_ASK_MATERIAL_DATA,
    ASK_TYPE_ASK_LIDAR_INFO,
    ASK_TYPE_CALIB_ASK_CUSTOMER_SN,
    ASK_TYPE_CALIB_ASK_SUB_SN_TO_DATA,
    ASK_TYPE_CALIB_ASK_SN_TO_SUB_SN_DATA,
    ASK_TYPE_KMS_AES_EFUSE_KEY,
    ASK_TYPE_KMS_ENABLE_SECURE_TAG,
    ASK_TYPE_KMS_SIGN_CERT
  };
  explicit AskData(const std::string& _station,
                   std::initializer_list<std::string> _items_name,
                   const std::string& _unassembled_sub_item_sn = "") :
    station_ask_for(_station), unassembled_sub_item_sn(_unassembled_sub_item_sn)
  {
    for (const auto& item : _items_name)
    {
      items.insert(std::pair<std::string, std::string>(item, ""));
    }
  };

  AskData(const AskData& _ad) = default;
  AskData()                   = default;
  AskData& operator           =(const AskData& _other)
  {
    if (this != &_other)
    {
      station_ask_for = _other.station_ask_for;
      items           = _other.items;
      is_ask_success  = _other.is_ask_success;
      sub_item_sn     = _other.sub_item_sn;
      sub_item_index  = _other.sub_item_index;
    }
    return *this;
  }
  AskData(AskData&& _ad) = default;
  AskData& operator=(AskData&& _other) = default;
  ~AskData()                           = default;
  /// the station or material you ask for
  std::string station_ask_for;
  /// the items in the station or items of material you want to ask for
  std::map<std::string, std::string> items;
  /// the unassembled sub item sn
  std::string unassembled_sub_item_sn;
  /// ask successfully or failed
  bool is_ask_success { false };
  /// if ask for sub item like module(which has 5 modules) or mirror, its sn will be return by this member
  std::string sub_item_sn;
  /// if ask for sub item like module(which has 5 modules), this member will be set to indicate which module is
  std::string sub_item_index;
};
// NOLINTNEXTLINE(cert-err58-cpp)
const QVector<QString> G_ASK_TYPE_STR({ "CalibAskCalib", "CalibAskModule", "CalibAskMirror", "CalibAskReceiveModule",
                                        "CalibAskEmittingModule", "CalibAskMaterial", "ASK_LIDAR_INFO",
                                        "CalibAskCustomerSN", "CalibAskSubSNToData", "CalibAskSNToSubSNData",
                                        "AES_EFUSE_KEY", "ENABLE_SECURE_TAG", "SIGN_CERT" });
/**
 * @brief     when user authority change, WidgetLogSetting::signalAuthorityUpdate will be send, so you need to connect
 *            this signal to your slot, and change you widget's permission according to UserAuthority
 */
class UserAuthority : public QObject
{
  Q_OBJECT
public:
  enum class Level
  {
    LEVEL_DEVELOPER = 0,
    LEVEL_MANAGER,
    LEVEL_TECHNICIAN,
    LEVEL_OPERATOR,
    LEVEL_NULL
  };
  explicit UserAuthority(Level _level = Level::LEVEL_NULL, QObject* _parent = nullptr) :
    QObject(_parent), level_(_level) {};
  ~UserAuthority() override      = default;
  UserAuthority(UserAuthority&&) = delete;
  UserAuthority& operator=(UserAuthority&&) = delete;
  UserAuthority(const UserAuthority& _other) : level_(_other.level_) {};
  UserAuthority& operator=(const UserAuthority& _other)
  {
    if (this != &_other)
    {
      level_ = _other.level_;
    }
    return *this;
  }
  UserAuthority& operator=(const Level _level)
  {
    level_ = _level;
    return *this;
  }
  /**
   * @brief     check if current user authority is not less than other user or other level
   * 
   * @param     _other_user        other user to compare
   * @return    true               current user authority is not less than other user's 
   * @return    false              current user authority is less than other user's
   */
  bool isNotLessThan(const UserAuthority& _other_user) const { return level_ <= _other_user.level_; };
  bool isNotLessThan(const Level _level) const { return level_ <= _level; };
  QString getName() const { return QString::fromUtf8(LEVEL_STR.at(static_cast<int>(level_))); };

private:
  Level level_;
  constexpr static std::array<const char*, 5> LEVEL_STR = { "开发者", "管理员", "技术员", "操作员", "无权限" };
};

enum class FinishState
{
  FAIL,
  PASS
};

enum EquipmentStatus
{
  EQUIPMENT_STATUS_UNKNOWN = 0,   // 无
  EQUIPMENT_STATUS_AUTO_RUNNING,  // 自动运行
  EQUIPMENT_STATUS_ERROR,         // 报警
  EQUIPMENT_STATUS_PAUSE,         // 暂停
  EQUIPMENT_STATUS_UNSAFE,        // 安全条件不满足
  EQUIPMENT_STATUS_MAINTAIN,      // 保养
  EQUIPMENT_STATUS_CHANGE_MODEL,  // 换型
  EQUIPMENT_STATUS_WAIT_PRODUCT   // 待料
};

enum EquipmentErrorCode
{
  ER_CODE_OTHER = 0,        // 其他设备错误
  ER_CODE_RELAY_DEV,        // ER001，继电器控制故障
  ER_CODE_ROTATOR_DEV,      // ER002，转台设备故障
  ER_CODE_EMPTY_POINT_DEV,  // ER003，点云空洞设备故障
  ER_CODE_CAR_DEV,          // ER004，小车设备故障
  ER_CODE_THERMOSTAT_DEV    // ER005，温箱设备故障
};
}  // namespace rsfsc_lib
}  // namespace lidar
}  // namespace robosense
#endif  // RSFSCLIB_MSG_H
