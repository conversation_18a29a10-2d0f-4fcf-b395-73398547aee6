﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef RUBY_DATA_STRUCT_H
#define RUBY_DATA_STRUCT_H

#include "mech.h"
#include <array>
#include <cstdint>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
namespace ruby
{
constexpr std::array<uint8_t, 4> MSOP_FRAME_FLAG = { 0x55, 0xaa, 0x05, 0x5a };
using ConfigPara                                 = mech::ConfigPara;
struct MonitorData
{
  uint32_t top_2v5;
  uint32_t top_vbus;
  uint32_t top_tx5;
  uint32_t top_a5;
  uint32_t top_hv;
  uint32_t top_n5v;
  uint32_t machine_vbus;
  uint32_t bot_5v;
  uint32_t bot_28v;
  uint32_t top_5v5_shunt_v;
  uint32_t top_5v5_bus_v;
  uint32_t top_5v5_power;
  uint32_t bot_24v_shunt_v;
  uint32_t bot_24v_bus_v;
  uint32_t bot_24v_power;
  uint32_t top_5v5_current;
  uint32_t machine_current;
  uint32_t bot_current;
  uint32_t bot_28v_current;
  float apd_temp;
  float top_under_temp;
  float top_above_temp;
  float bot_motor_temp;
  float bot_magnetic_rings_temp;
  float top_fpga_rx_temp;
  float chip_on_temp;
};
}  // namespace ruby

}  // namespace lidar
}  // namespace robosense

#endif  // RUBY_DATA_STRUCT_H