﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef WIDGET_PARA_SETTING_H
#define WIDGET_PARA_SETTING_H
#include <QDialog>
#include <QSpinBox>
#include <QTreeWidget>
#include <QWidget>
#include <memory>

class QLineEdit;
class QComboBox;
class QCheckBox;
class QSpinBox;
class QDoubleSpinBox;
class QSlider;
class QRadioButton;

constexpr auto PARA_SUFFIX     = "_para_";
constexpr auto PARA_VAR_SUFFIX = "_para_var_";

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define DECLARE_SETTING_PROPERTY(TYPE, NAME, FUNC_NAME, PARA_TYPE)                                                  \
public:                                                                                                             \
  Q_PROPERTY(QVariant NAME##_para_var_ READ get##FUNC_NAME##ParaVar WRITE set##FUNC_NAME##ParaVar)                  \
public:                                                                                                             \
  [[nodiscard]] decltype(TYPE::curr_val) get##FUNC_NAME() const { return NAME##_para_var_.value<TYPE>().curr_val; } \
  void set##FUNC_NAME(const decltype(TYPE::curr_val)& _value)                                                       \
  {                                                                                                                 \
    TYPE temp_para     = NAME##_para_var_.value<TYPE>();                                                            \
    temp_para.curr_val = _value;                                                                                    \
    NAME##_para_var_   = QVariant::fromValue(temp_para);                                                            \
  }                                                                                                                 \
                                                                                                                    \
private:                                                                                                            \
  [[nodiscard]] QVariant get##FUNC_NAME##ParaVar() const { return NAME##_para_var_; }                               \
  void set##FUNC_NAME##ParaVar(const QVariant& _var) { NAME##_para_var_ = _var; }                                   \
                                                                                                                    \
private:                                                                                                            \
  QVariant NAME##_para_var_ = QVariant::fromValue(PARA_TYPE);

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define DECLARE_PARA_PROPERTY(TYPE, NAME, FUNC_NAME)                         \
  Q_PROPERTY(TYPE NAME##_para_var_ READ get##FUNC_NAME WRITE set##FUNC_NAME) \
public:                                                                      \
  TYPE get##FUNC_NAME() const { return NAME##_para_var_; }                   \
  void set##FUNC_NAME(const TYPE& _value) { (NAME##_para_var_) = _value; }   \
                                                                             \
private:                                                                     \
  TYPE NAME##_para_var_

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

class ParaInfo : public QObject
{
  Q_OBJECT
public:
  explicit ParaInfo(QObject* _parent = nullptr);
  explicit ParaInfo(ParaInfo&&) = delete;
  ParaInfo(const ParaInfo&);
  ParaInfo& operator=(ParaInfo&&) = delete;
  ParaInfo& operator=(const ParaInfo&) = delete;
  ~ParaInfo() override;

  void copyProperties(const ParaInfo* _source);

  [[nodiscard]] std::shared_ptr<ParaInfo> clone() const;
  ParaInfo* clone(QObject* _parent) const;
  void clone(const ParaInfo& _para_info);
  void clone(const ParaInfo* _para_info);
  void clone(std::shared_ptr<ParaInfo> _para_info);

  DECLARE_PARA_PROPERTY(int, lidar_num, LidarNum);
  DECLARE_PARA_PROPERTY(QString, config_459_path, Config459Path);
  DECLARE_PARA_PROPERTY(bool, is_retest, IsRetest);
  DECLARE_PARA_PROPERTY(int, neg_vol_sleep_time, NegVolSleepTime);
  DECLARE_PARA_PROPERTY(int, time_sync_num, TimeSyncNum);
  DECLARE_PARA_PROPERTY(int, power_wait, PowerWait);
  DECLARE_PARA_PROPERTY(int, light_err_test_time, LightErrTestTime);

private:
};

class WidgetParaSetting : public QDialog
{
  Q_OBJECT
public:
  explicit WidgetParaSetting(QWidget* _parent = nullptr);
  explicit WidgetParaSetting(WidgetParaSetting&&)      = delete;
  explicit WidgetParaSetting(const WidgetParaSetting&) = delete;
  WidgetParaSetting& operator=(WidgetParaSetting&&) = delete;
  WidgetParaSetting& operator=(const WidgetParaSetting&) = delete;
  ~WidgetParaSetting() override;

public Q_SLOTS:
  void show();
  void slotButtonSaveParaClicked();

public:
  struct ParaInt
  {
    QString disp_group;
    QString disp_name;
    int default_val;
    int min_val;
    int max_val;
    int curr_val;
    QSpinBox* container;

    [[nodiscard]] QString toString() const
    {
      return QString("{disp_group: %1, disp_name: %2, default_val: %3, min_val: %4, max_val: %5, curr_val: %6}")
        .arg(disp_group)
        .arg(disp_name)
        .arg(default_val)
        .arg(min_val)
        .arg(max_val)
        .arg(curr_val);
    }
  };
  struct ParaDouble
  {
    QString disp_group;
    QString disp_name;
    double default_val;
    double min_val;
    double max_val;
    double curr_val;
    QDoubleSpinBox* container;

    [[nodiscard]] QString toString() const
    {
      return QString("{disp_group: %1, disp_name: %2, default_val: %3, min_val: %4, max_val: %5, curr_val: %6}")
        .arg(disp_group)
        .arg(disp_name)
        .arg(default_val)
        .arg(min_val)
        .arg(max_val)
        .arg(curr_val);
    }
  };
  struct ParaPath
  {
    QString disp_group;
    QString disp_name;
    QString default_val;
    QString curr_val;
    QLineEdit* container;

    [[nodiscard]] QString toString() const
    {
      return QString("{disp_group: %1, disp_name: %2, default_val: %3, curr_val: %4}")
        .arg(disp_group)
        .arg(disp_name)
        .arg(default_val)
        .arg(curr_val);
    }
  };
  struct ParaBool
  {
    QString disp_group;
    QString disp_name;
    bool default_val;
    bool curr_val;
    QCheckBox* container;

    [[nodiscard]] QString toString() const
    {
      return QString("{disp_group: %1, disp_name: %2, default_val: %3, curr_val: %4}")
        .arg(disp_group)
        .arg(disp_name)
        .arg(static_cast<int>(default_val))
        .arg(static_cast<int>(curr_val));
    }
  };
  std::shared_ptr<ParaInfo> getCurrParaInfo();
  [[nodiscard]] bool compareProperties(const ParaInfo& _para_info) const;

  DECLARE_SETTING_PROPERTY(ParaInt, lidar_num, LidarNum, ParaInt({ "App参数", "雷达数量", 1, 1, 16 }));
  DECLARE_SETTING_PROPERTY(ParaPath, config_459_path, Config459Path, ParaPath({ "测试参数", "配置文件", "", "" }));
  DECLARE_SETTING_PROPERTY(ParaBool, is_retest, IsRetest, ParaBool({ "测试参数", "是否复测", false, false }));
  DECLARE_SETTING_PROPERTY(ParaInt,
                           neg_vol_sleep_time,
                           NegVolSleepTime,
                           ParaInt({ "测试参数", "负压延迟时间s", 10, 0, 1000 }));
  DECLARE_SETTING_PROPERTY(ParaInt, time_sync_num, TimeSyncNum, ParaInt({ "测试参数", "获取同步次数", 5, 1, 1000 }));
  DECLARE_SETTING_PROPERTY(ParaInt, power_wait, PowerWait, ParaInt({ "测试参数", "功率延迟时间s", 5, 0, 1000 }));
  DECLARE_SETTING_PROPERTY(ParaInt,
                           light_err_test_time,
                           LightErrTestTime,
                           ParaInt({ "测试参数", "单次误码测试时间s", 15, 1, 9999 }));

private:
  void createPropertyContainers();
  void updateCurrToWidgetValue();
  void updateWidgetToCurrValue();
  void readParaInfoSettings();
  void writeParaInfoSettings();

  void readLayoutSettings();
  void writeLayoutSettings();

  void closeEvent(QCloseEvent* _event) override;

  QTreeWidget* tree_widget_;
  QPushButton* button_save_para_;
  std::map<QString, QTreeWidgetItem*> group_item_map_;
};

}  // namespace lidar
}  // namespace robosense

Q_DECLARE_METATYPE(robosense::lidar::WidgetParaSetting::ParaInt);
Q_DECLARE_METATYPE(robosense::lidar::WidgetParaSetting::ParaBool);
Q_DECLARE_METATYPE(robosense::lidar::WidgetParaSetting::ParaDouble);
Q_DECLARE_METATYPE(robosense::lidar::WidgetParaSetting::ParaPath);

#endif  // WIDGET_PARA_SETTING_H