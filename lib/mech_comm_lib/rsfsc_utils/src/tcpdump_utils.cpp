﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "rsfsc_utils/tcpdump_utils.h"
#include <QProcess>
#include <QRegularExpression>
#include <optional>
#include <rsfsc_log/rsfsc_log.h>
#include <sstream>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

static uint32_t ipToUint32(const std::string& _ip)
{
  std::stringstream s_str(_ip);
  uint32_t result = 0;
  uint32_t octet  = 0;

  for (int i = 0; i < 4; ++i)
  {
    char dot = 0;
    s_str >> octet;
    result = (result << 8U) | (octet & 0xFFU);  // 右移并合并
    if (i < 3)
    {
      s_str >> dot;  // 读取 `.`，避免最后多读一次
    }
  }

  return result;
}

TcpdumpUtils::~TcpdumpUtils()
{
  // 在析构函数中调用 abort 和 resetProcess 确保资源释放
  abort();
  resetProcess();
}

void TcpdumpUtils::abort()
{
  is_abort_ = true;
  // 如果进程正在运行，终止它
  resetProcess();
}

std::optional<TcpdumpUtils::NetInfo> TcpdumpUtils::captureOneNetInfo(const Filter& _filter)
{
  resetProcess();
  process_ = std::make_unique<QProcess>();

  QStringList arguments;

  QString program = "timeout";

  // 使用timeout命令包装tcpdump
  process_->setProgram(program);

  arguments << QString("%1").arg(_filter.timeout_s)  // timeout时间
            << "tcpdump"                             // 实际执行的命令
            << "-c"
            << "1"
            << "-nqle";
  auto interface = _filter.network_interface;
  if (interface.empty())
  {
    interface = "any";
  }
  arguments << "-i" << QString::fromStdString(interface);

  auto filter_str = fmt::format("(udp) and net {}", _filter.ip_mask);
  if (!_filter.dst_ip.empty())
  {
    filter_str += fmt::format(" and dst {}", _filter.dst_ip);
  }
  if (!_filter.exclude_mac_addr_vec.empty())
  {
    filter_str += " and not (";
    for (size_t i = 0; i < _filter.exclude_mac_addr_vec.size(); i++)
    {
      filter_str += fmt::format("ether host {}", _filter.exclude_mac_addr_vec[i]);
      if (i != _filter.exclude_mac_addr_vec.size() - 1)
      {
        filter_str += " and ";
      }
    }
    filter_str += ")";
  }
  if (!_filter.custom_filter.empty())
  {
    filter_str += fmt::format(" and ({})", _filter.custom_filter);
  }

  RSFSCLog::getInstance(log_index_)->debug("tcpdump命令: {} {} '{}'", program, arguments.join(" "), filter_str);
  arguments << filter_str.c_str();
  // RSFSCLog::getInstance(log_index_)->debug("tcpdump命令: {} {}", program, arguments.join(" "));

  is_abort_ = false;
  process_->setArguments(arguments);

  std::string res;

  process_->start();

  // 等待完成或超时
  process_->waitForFinished();

  if (process_->exitCode() == 124)
  {
    RSFSCLog::getInstance(log_index_)
      ->debug("tcpdump抓包{}秒超时, err: {}", _filter.timeout_s, process_->readAllStandardError());
    resetProcess();
    return {};
  }
  // 检查执行结果
  QString output = process_->readAllStandardOutput();
  QString error  = process_->readAllStandardError();
  RSFSCLog::getInstance(log_index_)
    ->debug("tcpdump success exit code: {}, output: {}, error: {}", process_->exitCode(), output.toStdString(),
            error.toStdString());
  res = output.toStdString();

  if (res.empty())
  {
    RSFSCLog::getInstance(log_index_)->error("tcpdump出错或者超时{}秒失败", _filter.timeout_s);
    resetProcess();
    return {};
  }

  // 解析输出获取IP和端口信息
  std::optional<NetInfo> ip_port;

  // 使用Qt正则表达式匹配tcpdump输出
  // 示例 14:52:45.466673  In 40:2c:76:08:4a:cc *************.49607 > *************.7788: UDP, length 1248
  QRegularExpression ip_port_regex(R"((\d+\.\d+\.\d+\.\d+)\.(\d+) > (\d+\.\d+\.\d+\.\d+)\.(\d+))");
  QRegularExpressionMatch match = ip_port_regex.match(QString::fromStdString(res));
  QRegularExpression mac_regex(R"(([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2}))");
  QRegularExpressionMatch mac_match = mac_regex.match(QString::fromStdString(res));

  if (match.hasMatch() && mac_match.hasMatch())
  {
    RSFSCLog::getInstance(log_index_)->debug("ip captured text: {}", match.captured(0));
    RSFSCLog::getInstance(log_index_)->debug("mac addr captured text: {}", mac_match.captured(0));
    QString src_mac_addr = mac_match.captured(0);

    QString src_ip   = match.captured(1);
    QString src_port = match.captured(2);
    QString dst_ip   = match.captured(3);
    QString dst_port = match.captured(4);

    // 初始化optional
    ip_port = NetInfo();

    // 我们需要源IP，不是目标IP (*************)
    ip_port->ip         = src_ip.toStdString();
    ip_port->mac_addr   = src_mac_addr.toStdString();
    ip_port->msop_port  = ipToUint32(src_ip.toStdString()) & 0xFFFFU;
    ip_port->difop_port = static_cast<uint16_t>(ip_port->msop_port + 1);
    RSFSCLog::getInstance(log_index_)
      ->debug("获取到IP: {}, msop_port: {}, difop_port: {}", ip_port->ip, ip_port->msop_port, ip_port->difop_port);

    // 特殊处理*************
    if (ip_port->ip == "*************")
    {
      ip_port->msop_port  = 6699;
      ip_port->difop_port = 7788;
      RSFSCLog::getInstance(log_index_)
        ->debug("获取到*************, msop_port: {}, difop_port: {}", ip_port->msop_port, ip_port->difop_port);
    }
  }
  else
  {
    RSFSCLog::getInstance(log_index_)->error("解析获取IP失败： {}", res);
    resetProcess();
    return {};
  }

  resetProcess();
  return ip_port;
}

std::optional<TcpdumpUtils::NetInfo> TcpdumpUtils::captureOneAiryNetInfo(Filter _filter)
{
  if (!_filter.custom_filter.empty())
  {
    _filter.custom_filter += " and ";
  }
  _filter.custom_filter += "(udp[8:4] == 0xaa555a05 or udp[8:4] == 0x55aa055a or udp[8:4] == 0xa5ff005a)";

  return captureOneNetInfo(_filter);
}

void TcpdumpUtils::resetProcess()
{
  if (process_ && process_->state() != QProcess::NotRunning)
  {
    RSFSCLog::getInstance(log_index_)->trace("正在结束tcpdump");
    process_->terminate();
    RSFSCLog::getInstance(log_index_)->trace("已terminate tcpdump");
    if (!process_->waitForFinished(1000))  // 等待1秒
    {
      process_->kill();  // 强制终止
      RSFSCLog::getInstance(log_index_)->trace("强制kill tcpdump");
    }
    RSFSCLog::getInstance(log_index_)->trace("tcpdump已经结束");
  }
}

bool TcpdumpUtils::startTcpdump(const std::string& _dump_file_path, const Filter& _filter)
{
  resetProcess();
  process_ = std::make_unique<QProcess>();

  QStringList arguments;

  QString program = "tcpdump";
  arguments << "-w" << QString::fromStdString(_dump_file_path);
  auto interface = _filter.network_interface;
  if (interface.empty())
  {
    interface = "any";
  }
  arguments << "-i" << QString::fromStdString(interface);

  if (!_filter.file_size.empty())
  {
    arguments << "-C" << QString::fromStdString(_filter.file_size) << "-W"
              << "1";  // 限制dump的文件大小
  }

  std::string filter_str;
  if (!_filter.protocol.empty())
  {
    if (!filter_str.empty())
    {
      filter_str += " and ";
    }
    filter_str = fmt::format("({})", _filter.protocol);
  }
  if (!_filter.src_ip.empty())
  {
    if (!filter_str.empty())
    {
      filter_str += " and ";
    }
    filter_str += fmt::format("src {}", _filter.src_ip);
  }
  if (!_filter.host_ip.empty())
  {
    if (!filter_str.empty())
    {
      filter_str += " and ";
    }
    filter_str += fmt::format("host {}", _filter.host_ip);
  }
  if (!_filter.dst_ip.empty())
  {
    if (!filter_str.empty())
    {
      filter_str += " and ";
    }
    filter_str += fmt::format("dst {}", _filter.dst_ip);
  }
  if (!_filter.custom_filter.empty())
  {
    if (!filter_str.empty())
    {
      filter_str += " and ";
    }
    filter_str += fmt::format("({})", _filter.custom_filter);
  }

  auto cmd_str = fmt::format("{} {} '{}'", program, arguments.join(" "), filter_str);
  if (!filter_str.empty())
  {
    arguments << filter_str.c_str();
  }
  RSFSCLog::getInstance(log_index_)->debug("tcpdump命令: {}", cmd_str);

  process_->setProgram(program);
  process_->setArguments(arguments);
  process_->start();

  if (!process_->waitForStarted(3000))
  {
    RSFSCLog::getInstance(log_index_)->error("tcpdump进程启动失败");
    resetProcess();
    return false;
  }

  return true;
}
void TcpdumpUtils::stopTcpdump() { resetProcess(); }

std::string TcpdumpUtils::getNetworkInterFaceByIP(const std::string& _ip)
{
  // ip -o -4 addr show | awk '$4 ~ /*************/ {print $2}'
  auto process = std::make_unique<QProcess>();
  process->setProgram("sh");
  process->setArguments(
    QStringList() << "-c"
                  << QString("ip -o -4 addr show | awk '$4 ~ /%1/ {print $2}'").arg(QString::fromStdString(_ip)));
  // RSFSCLog::getInstance(log_index_)->debug("ip命令: {}", process->arguments().join(" "));
  process->start();
  process->waitForFinished(1000);
  QString output = process->readAllStandardOutput().trimmed();
  // RSFSCLog::getInstance(log_index_)->debug("ip命令输出: {}", output.toStdString());
  return output.toStdString();
}

void TcpdumpUtils::killAllTcpdump()
{
  auto process = std::make_unique<QProcess>();
  process->setProgram("sh");
  process->setArguments(QStringList() << "-c"
                                      << "pkill -f tcpdump");
  process->start();
  process->waitForFinished(1000);
  RSFSCLog::getInstance()->debug("killAllTcpdump命令输出: {}",
                                 process->readAllStandardOutput().trimmed().toStdString());
}

}  // namespace lidar

}  // namespace robosense
