﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "lidar_ctl.h"
#include "app_event.h"
#include "config.h"
#include "mech_udp.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QDateTime>
#include <QDebug>
#include <QString>
#include <chrono>
#include <cstdint>
#include <string>
#include <vector>
#include <widget_log_setting.h>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

ConnectivityWorkHandler::~ConnectivityWorkHandler()
{
  abort();
  wake();
}
ActionState ConnectivityWorkHandler::handleState()
{
  ActionState next_state = ActionState::STATE_END;
  try
  {
    next_state = handle();
  }
  catch (const InterruptException& exception)
  {
    LOG_INDEX_ERROR("触发中断: {}", exception.what());
    getWorkModel()->abort();
    next_state = ActionState::STATE_ABORT;
  }
  catch (const std::exception& exception)
  {
    LOG_INDEX_ERROR("触发抛出异常，未知异常: {}", exception.what());
    getWorkModel()->abort();
    next_state = ActionState::STATE_ABORT;
  }
  catch (...)
  {
    LOG_INDEX_ERROR("触发抛出异常，未知异常");
    getWorkModel()->abort();
    next_state = ActionState::STATE_ABORT;
  }
  return next_state;
}

bool ConnectivityWorkHandler::sleep(uint32_t _sec)
{
  std::unique_lock<std::mutex> lock(mutex_);
  uint32_t interval = 2;  // 每隔2秒检查一次
  uint32_t elapsed  = 0;  // 已经过去的时间

  while (elapsed < _sec)
  {
    // 等待一段时间或直到被唤醒
    if (condition_.wait_for(lock, std::chrono::seconds(interval), [&] { return isAbort(); }))
    {
      LOG_INDEX_DEBUG("Aborted after {} s", elapsed);
      return false;  // 如果中途isAbort()变为true，则提前退出
    }

    elapsed += interval;  // 增加已等待时间
    if (elapsed > _sec)
    {
      elapsed = _sec;  // 防止时间溢出
    }
    LOG_INDEX_INFO("等待中... {}/{} s", elapsed, _sec);

    if (elapsed == _sec)
    {
      break;  // 如果时间到了，退出循环
    }
  }

  return true;  // 完成全部等待
}

bool ConnectivityWorkHandler::msleep(uint32_t _msec)
{
  LOG_INDEX_DEBUG("sleep {} ms", _msec);
  std::unique_lock<std::mutex> lock(mutex_);
  return (!isAbort() && !condition_.wait_for(lock, std::chrono::milliseconds(_msec), [&] { return isAbort(); }));
}

void ConnectivityWorkHandler::abort()
{
  getWorkModel()->abort();
  std::unique_lock<std::mutex> lock(mutex_);
  WorkHandler<ConnectivityWorkModel>::abort();
  wake();
}

void ConnectivityWorkHandler::wake() { condition_.notify_all(); }

std::string ConnectivityWorkHandler::pingWait(const std::vector<std::string>& _ip_vec, uint32_t _msec)
{
  auto start_time = std::chrono::system_clock::now();
  auto end_time   = std::chrono::system_clock::now();
  auto duration   = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

  uint32_t total_steps = _msec / 2000;
  int current_step     = 0;

  std::string ip_vec_str;
  for (const auto& each_ip : _ip_vec)
  {
    ip_vec_str += each_ip + ",";
  }

  while (!isAbort() && duration.count() < _msec)
  {
    for (const auto& each_ip : _ip_vec)
    {
      if (getLidarManager()->ping(each_ip))
      {
        return each_ip;
      }
    }
    end_time = std::chrono::system_clock::now();
    duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    current_step++;
    LOG_INDEX_INFO("等待雷达[{}] ping响应... {}/{}", ip_vec_str, current_step, total_steps);
    sleep(2);
  }

  return "";
}

bool ConnectivityWorkHandler::pingWait(const QString& _ip_addr, uint32_t _msec)
{
  std::vector<std::string> ip_vec = { _ip_addr.toStdString() };
  return !pingWait(ip_vec, _msec).empty();
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool ConnectivityWorkHandler::waitForStartUpComplete(const QString& _ip, const int _port, const int _timeout)
{
  return waitForOneMsopPacket(_ip, _port, _timeout);
}
bool ConnectivityWorkHandler::waitForOneMsopPacket(const QString& _ip, const int _port, const int _timeout)
{
  int pkt_count = 0;
  std::mutex mtx;
  std::condition_variable cond_v;

  auto ptr_udp_client = std::make_unique<robosense::lidar::MechUdp>(1248);
  ptr_udp_client->regRecvCallback([&](const char* _udp_data) {
    mech::MsopPacket msop_pkt {};
    std::memcpy(&msop_pkt, _udp_data, sizeof(mech::MsopPacket));
    if (!msop_pkt.isValid())
    {
      return;
    }
    std::lock_guard<std::mutex> lock(mtx);
    pkt_count++;
    cond_v.notify_one();  // 收到有效的包后通知等待线程
  });

  ptr_udp_client->start(_ip.toStdString(), _port);
  auto start_time = std::chrono::steady_clock::now();

  std::unique_lock<std::mutex> lock(mtx);
  while (!isAbort() && pkt_count == 0)
  {
    // 使用条件变量等待包，设置超时时间为2秒
    if (cond_v.wait_for(lock, std::chrono::seconds(2), [&] { return pkt_count > 0; }))
    {
      break;  // 成功收到包，退出循环
    }

    // 检查超时条件
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    if (duration.count() > _timeout)
    {
      break;  // 超时退出
    }
    // 计算已等待的总时长
    auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
    auto waited_seconds = total_duration / 1000;
    int total_steps     = _timeout / 2000;
    int current_step    = static_cast<int>(waited_seconds / 2 + 1);
    LOG_INDEX_INFO("等待雷达[{}:{}]启动完成...  {}/{} ", _ip, _port, current_step, total_steps);
  }

  return pkt_count != 0;  // 返回是否收到包的结果
}

void ConnectivityWorkHandler::abortAndMaintainEnv(const QString& _msg)
{
  AppEvent::getInstance()->signalShowErrorMessageBox(
    QString("[%1] %2，已保留现场，请尽快排查处理").arg(getLidarIndex()).arg(_msg));
  app()->getWidgetMes()->finishProcess(getLidarIndex(), LogTestStatus::LOG_TEST_STATUS_FAIL, "Aging", _msg);
}

void ConnectivityWorkHandler::setWorkModelFailMsg(const QString& _fail_label, std::string_view _fail_msg)
{
  getWorkModel()->setFailLabel(_fail_label);
  getWorkModel()->setFailMsg(QString::fromStdString(std::string(_fail_msg)));
  LOG_INDEX_ERROR("{}, {}", _fail_label, _fail_msg);
}
void ConnectivityWorkHandler::setWorkModelFailMsg(std::string_view _fail_msg)
{
  setWorkModelFailMsg(className(), _fail_msg);
}

ActionState CheckMes::handle()
{
  getWorkModel()->setTestState(TestState::RUNNING);
  QString data_path;
  QString result_path;
  QString temp_path;

  getWorkModel()->resetAbort();

  LOG_INDEX_INFO("雷达数量: {}", getParaInfo().getLidarNum());

  if (!app()->getWidgetMes()->checkMesIsOK(getLidarIndex(), data_path, result_path, temp_path))
  {
    LOG_ERROR("MES检查失败");
    getWorkModel()->setTestState(TestState::FAILED);
    return ActionState::STATE_FINAL;
  }
  app()->getWidgetMes()->setFirmwareRevision(getLidarIndex(), 0, 0);

  ConnectivityWorkModel::Path path;
  path.data_dir   = data_path;
  path.result_dir = result_path;
  path.temp_dir   = temp_path;
  if (!getWorkModel()->initPath(path))
  {
    LOG_INDEX_ERROR("初始化路径失败");
    return ActionState::STATE_ABORT;
  }

  if (!getWorkModel()->requireVbd())
  {
    LOG_INDEX_ERROR("获取VBD电压失败");
    setWorkModelFailMsg("获取VBD电压失败");
    getWorkModel()->setTestState(TestState::FAILED);
    return ActionState::STATE_FAIL;
  }

  app()->setChangingIpIndex(getLidarIndex());
  return ActionState::STATE_WRITE_SN;
}

ActionState WriteSn::handle()
{
  LOG_INDEX_INFO("开始写入SN");
  getWorkModel()->powerOn();
  if (!getWorkModel()->addMeasureMessage("fsm_ping_lidar", pingWait(getLidarInfo()->getIP(), 40000)))
  {
    setWorkModelFailMsg(fmt::format("ping雷达{}失败", getLidarInfo()->getIP()));
    return ActionState::STATE_FAIL;
  }
  if (!getWorkModel()->addMeasureMessage("fsm_connect_lidar", getLidarManager()->connect(40000)))
  {
    setWorkModelFailMsg("连接雷达失败");
    return ActionState::STATE_FAIL;
  }

  if (!getParaInfo().getIsRetest())
  {
    uint32_t config_version = 0;
    if (!getWorkModel()->addMeasureMessage("fsm_get_config_param",
                                           getLidarManager()->readConfigParamater() &&
                                             getWorkModel()->readRegDataByKey("config_version", config_version)))
    {
      setWorkModelFailMsg("获取雷达配置参数失败");
      return ActionState::STATE_FAIL;
    }
    auto config_para = getLidarManager()->getConfigParaCache();
    app()->getWidgetMes()->setFirmwareRevision(getLidarIndex(), config_para.getPlVersion(), config_para.getPsVersion());

    bool is_pass = getWorkModel()->addMeasureMessage("fsm_bot_firmware_version", config_para.getPsVersion(),
                                                     rsfsc_lib::MEASURE_DATA_TYPE_HEX);
    is_pass &= getWorkModel()->addMeasureMessage("fsm_app_firmware_version", config_para.getSoftwareVersion(),
                                                 rsfsc_lib::MEASURE_DATA_TYPE_HEX);
    is_pass &=
      getWorkModel()->addMeasureMessage("fsm_total_config_version", config_version, rsfsc_lib::MEASURE_DATA_TYPE_HEX);

    if (!is_pass)
    {
      setWorkModelFailMsg("底板，APP固件版本不正确，无法进行连通性测试，请确认好固件版本");
      return ActionState::STATE_ABORT;
    }
  }
  // if (!getWorkModel()->writeSn())
  // {
  //   setWorkModelFailMsg("写入SN失败");
  //   return ActionState::STATE_FAIL;
  // }
  // sleep(2);

  if (!getWorkModel()->addMeasureMessage("fsm_set_time_sync_mode",
                                         getWorkModel()->setTimeSyncModeGps() || getWorkModel()->setTimeSyncModeGps()))
  {
    setWorkModelFailMsg("设置时间同步模式失败");
    return ActionState::STATE_FAIL;
  }
  sleep(5);

  return ActionState::STATE_CONNECT_LIDAR;
}

ActionState ConnectLidar::handle()
{
  app()->getWidgetMes()->setFirmwareRevision(getLidarIndex(), 0, 0);
  getLidarManager()->disconnect();

  bool is_pass = true;
  // if (!getWorkModel()->addMeasureMessage("fsm_box_lidar_power_on",
  //                                        getWorkModel()->getBoxPtr()->switchLidarPower(getLidarIndex(), true)))
  // {
  //   setWorkModelFailMsg(fmt::format("上电雷达{}失败", getLidarIndex()));
  //   is_pass = false;
  // }
  LOG_INFO("上电后等待5s...");
  sleep(5);

  uint32_t curr = 0;
  getWorkModel()->getBoxPtr()->getElectricity(getLidarIndex(), curr);
  if (!getWorkModel()->addMeasureMessage("fsm_box_lidar_current_wait_bot", curr, rsfsc_lib::MEASURE_DATA_TYPE_INT))
  {
    setWorkModelFailMsg(fmt::format("雷达{}底板启动电流检测超限", getLidarIndex()));
    is_pass = false;
  }

  auto ip_addr = getWorkModel()->getLidarManager()->getLidarInfo()->getIP();
  auto port    = getWorkModel()->getLidarManager()->getLidarInfo()->getMSOPPort();

  if (!getWorkModel()->addMeasureMessage("fsm_ping_lidar", pingWait(ip_addr, 20000)))
  {
    setWorkModelFailMsg(fmt::format("ping雷达[{}]失败", ip_addr));
    is_pass = false;
    return ActionState::STATE_FAIL;
  }
  LOG_INDEX_INFO("ping雷达成功，IP为[{}]", ip_addr);

  int time_out = 40000;
  // waitForStartUpComplete(ip_addr, port, time_out);
  if (!getWorkModel()->addMeasureMessage("fsm_connect", getWorkModel()->getLidarManager()->connect()))
  {
    setWorkModelFailMsg("连接雷达失败");
    is_pass = false;
    return ActionState::STATE_FAIL;
  }

  waitForStartUpComplete(ip_addr, port, time_out);
  if (!getLidarManager()->readConfigParamater())
  {
    sleep(1);
    if (!getWorkModel()->addMeasureMessage("fsm_get_config_param", getLidarManager()->readConfigParamater()))
    {
      setWorkModelFailMsg("获取雷达配置参数失败");
      is_pass = false;
      return ActionState::STATE_FAIL;
    }
  }
  auto config_para = getLidarManager()->getConfigParaCache();
  app()->getWidgetMes()->setFirmwareRevision(getLidarIndex(), config_para.getPlVersion(), config_para.getPsVersion());
  // 计算连接时间
  QDateTime current_date_time = getWorkModel()->getLastPowerOnTime();
  auto end_time               = QDateTime::currentDateTime();
  auto duration               = current_date_time.secsTo(end_time);

  LOG_INDEX_INFO("已检测到点云包，等待{}s功率稳定...", getParaInfo().getPowerWait());
  sleep(getParaInfo().getPowerWait());

  curr          = 0;
  uint32_t volt = 0;
  getWorkModel()->getBoxPtr()->getVoltage(getLidarIndex(), volt);
  if (!getWorkModel()->addMeasureMessage("fsm_box_lidar_voltage", static_cast<float>(volt) / 1000,
                                         rsfsc_lib::MEASURE_DATA_TYPE_FLOAT))
  {
    setWorkModelFailMsg(fmt::format("雷达{}电压检测超限", getLidarIndex()));
    is_pass = false;
  }
  getWorkModel()->getBoxPtr()->getElectricity(getLidarIndex(), curr);
  if (!getWorkModel()->addMeasureMessage("fsm_box_lidar_current_wait_top", curr, rsfsc_lib::MEASURE_DATA_TYPE_INT))
  {
    setWorkModelFailMsg(fmt::format("雷达{}电流检测超限", getLidarIndex()));
    is_pass = false;
  }
  if (!getWorkModel()->addMeasureMessage("fsm_box_lidar_power",
                                         static_cast<float>(volt) / 1000 * static_cast<float>(curr) / 1000,
                                         rsfsc_lib::MEASURE_DATA_TYPE_FLOAT))
  {
    setWorkModelFailMsg(fmt::format("雷达{}功率检测超限", getLidarIndex()));
    is_pass = false;
  }

  if (!getWorkModel()->addMeasureMessage("fsm_read_time_sync_mode", static_cast<int>(config_para.time_sync_mode),
                                         rsfsc_lib::MEASURE_DATA_TYPE_INT))
  {
    setWorkModelFailMsg(fmt::format("时间同步模式[{}:{}]不为GPS，不符合测试", config_para.time_sync_mode,
                                    static_cast<int>(config_para.time_sync_mode)));
    is_pass = false;
  }

  if (!getWorkModel()->addMeasureMessage("fsm_msop_time", static_cast<int>(duration), rsfsc_lib::MEASURE_DATA_TYPE_INT))
  {
    setWorkModelFailMsg("获取msop包超时");
    return ActionState::STATE_FAIL;
  }

  if (!is_pass)
  {
    return ActionState::STATE_FAIL;
  }

  LOG_INFO("connect finished");
  LOG_INFO("等待雷达启动完成");
  return ActionState::STATE_INIT_LIDAR;
}

ActionState InitLidar::handle()
{
  LOG_INDEX_INFO("开始初始化雷达");
  getLidarManager()->disconnect();
  if (!getLidarManager()->connect())
  {
    setWorkModelFailMsg("初始化时的连接失败");
    return ActionState::STATE_FAIL;
  }

  if (!getWorkModel()->writeCsvData("time_sync"))
  {
    setWorkModelFailMsg("写入time_sync初始化寄存器数据失败");
    return ActionState::STATE_FAIL;
  }

  LOG_INDEX_INFO("初始化完成等待2s");
  sleep(2);
  return ActionState::STATE_CHECK_MSOP;
}

ActionState CheckMsop::handle()
{
  LOG_INDEX_INFO("开始检查MSOP");

  bool is_pass = getWorkModel()->checkMsopData();

  uint32_t version = 0;
  if (!getWorkModel()->readRegDataByKey("config_version", version))
  {
    setWorkModelFailMsg("读取整机配置版本失败");
    is_pass = false;
  }
  if (!getParaInfo().getIsRetest())
  {
    if (!getWorkModel()->addMeasureMessage("fsm_459_config_version", version, rsfsc_lib::MEASURE_DATA_TYPE_HEX))
    {
      setWorkModelFailMsg("整机配置版本超限，不在版本范围内");
      is_pass = false;
    }
  }

  if (!is_pass)
  {
    return ActionState::STATE_FAIL;
  }

  return ActionState::STATE_CHECK_DIFOP;
}

ActionState CheckDifop::handle()
{
  LOG_INDEX_INFO("开始检查DIFOP");
  LOG_INDEX_INFO("等待{}s...负压数据稳定", getParaInfo().getNegVolSleepTime());
  sleep(getParaInfo().getNegVolSleepTime());
  bool is_pass = getWorkModel()->checkDifopData();
  bool res     = true;

  uint32_t sync = 0;
  for (int i = 0; i < getParaInfo().getTimeSyncNum(); ++i)
  {
    res = getWorkModel()->getBoxPtr()->getSync(getLidarIndex(), sync);
    if (res && sync == 1)
    {
      break;
    }
    sleep(1);
  }

  // if (sync != 1)
  // {
  //   LOG_INDEX_INFO("正在进一步测试同步信号");
  //   getWorkModel()->getBoxPtr()->switchLidarPower(getLidarIndex(), false);
  //   sleep(3);
  //   getWorkModel()->getBoxPtr()->switchLidarPower(getLidarIndex(), true);
  //   pingWait(getLidarInfo()->getIP(), 40000);
  //   getLidarManager()->connect();
  //   getLidarManager()->waitForTopStartUp();
  //   sleep(getParaInfo().getNegVolSleepTime());
  // }
  // for (int i = 0; i < getParaInfo().getTimeSyncNum(); ++i)
  // {
  //   res = getWorkModel()->getBoxPtr()->getSync(getLidarIndex(), sync);
  //   if (res && sync == 1)
  //   {
  //     break;
  //   }
  //   sleep(1);
  // }

  is_pass &= res;
  if (!res)
  {
    setWorkModelFailMsg("获取盒子同步状态失败");
    res = getWorkModel()->addMeasureMessage("fsm_box_sync_status", NAN, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
  }
  else
  {
    res = getWorkModel()->addMeasureMessage("fsm_box_sync_status", static_cast<int>(sync),
                                            rsfsc_lib::MEASURE_DATA_TYPE_INT);
    LOG_INFO("盒子同步状态: {}", sync);
  }
  is_pass &= res;
  if (!res)
  {
    setWorkModelFailMsg("盒子同步状态异常");
  }

  if (!is_pass)
  {
    return ActionState::STATE_FAIL;
  }

  sleep(1);
  return ActionState::STATE_CHECK_LIGHT_ERROR;
}

ActionState CheckLightError::handle()
{
  LOG_INDEX_INFO("开始进行光通误码测试");
  if (!getWorkModel()->stopUpOpticalErrorTest())
  {
    setWorkModelFailMsg("停止上行光通误码测试失败");
    getWorkModel()->addMeasureMessage("fsm_up_light_test_en", false);
    return ActionState::STATE_FAIL;
  }
  msleep(200);
  if (!getWorkModel()->startUpOpticalErrorTest())
  {
    setWorkModelFailMsg("启动上行光通误码测试失败");
    getWorkModel()->addMeasureMessage("fsm_up_light_test_en", false);
    return ActionState::STATE_FAIL;
  }
  LOG_INDEX_INFO("正在进行上行光通误码测试，等待{}s...", getParaInfo().getLightErrTestTime());
  sleep(getParaInfo().getLightErrTestTime());
  if (!getWorkModel()->stopUpOpticalErrorTest())
  {
    setWorkModelFailMsg("停止上行光通误码测试失败");
    getWorkModel()->addMeasureMessage("fsm_up_light_test_en", false);
    return ActionState::STATE_FAIL;
  }

  msleep(1000);
  uint32_t error = 0;
  uint32_t total = 0;
  if (!getWorkModel()->getUpOpticalError(error, total))
  {
    setWorkModelFailMsg("获取上行光通误码数据失败");
    getWorkModel()->addMeasureMessage("fsm_up_light_test_en", false);
    return ActionState::STATE_FAIL;
  }

  bool is_pass = true;
  if (!getWorkModel()->addMeasureMessage("fsm_up_light_test_total", total, rsfsc_lib::MEASURE_DATA_TYPE_INT))
  {
    is_pass = false;
    setWorkModelFailMsg("获取上行光通总数据量需要大于0");
  }
  double error_rate = static_cast<double>(error) / total;
  if (!getWorkModel()->addMeasureMessage("fsm_up_light_test_error", error, rsfsc_lib::MEASURE_DATA_TYPE_INT))
  {
    is_pass = false;
    getWorkModel()->addMeasureMessage("fsm_up_light_test_error_rate", error_rate, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
    setWorkModelFailMsg(fmt::format("获取上行光通误码数为{}", error));
  }
  else
  {
    getWorkModel()->addMeasureMessage("fsm_up_light_test_error_rate", 0, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
  }

  if (!getWorkModel()->stopDownOpticalErrorTest())
  {
    setWorkModelFailMsg("停止下行光通误码测试失败");
    return ActionState::STATE_FAIL;
  }
  msleep(200);
  if (!getWorkModel()->startDownOpticalErrorTest())
  {
    setWorkModelFailMsg("启动下行光通误码测试失败");
    return ActionState::STATE_FAIL;
  }
  LOG_INDEX_INFO("正在进行下行光通误码测试，等待{}s...", getParaInfo().getLightErrTestTime());
  sleep(getParaInfo().getLightErrTestTime());
  if (!getWorkModel()->stopDownOpticalErrorTest())
  {
    setWorkModelFailMsg("停止下行光通误码测试失败");
    return ActionState::STATE_FAIL;
  }
  msleep(1000);
  error = 0;
  total = 0;
  if (!getWorkModel()->getDownOpticalError(error, total))
  {
    setWorkModelFailMsg("获取下行光通误码数据失败");
    return ActionState::STATE_FAIL;
  }

  if (!getWorkModel()->addMeasureMessage("fsm_down_light_test_total", total, rsfsc_lib::MEASURE_DATA_TYPE_INT))
  {
    is_pass = false;
    setWorkModelFailMsg("获取下行光通总数据量需要大于0");
  }
  error_rate = static_cast<float>(error) / total;
  if (!getWorkModel()->addMeasureMessage("fsm_down_light_test_error", error, rsfsc_lib::MEASURE_DATA_TYPE_INT))
  {
    is_pass = false;
    getWorkModel()->addMeasureMessage("fsm_down_light_test_error_rate", error_rate, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
    setWorkModelFailMsg(fmt::format("获取下行光通误码数为{}", error));
  }
  else
  {
    getWorkModel()->addMeasureMessage("fsm_down_light_test_error_rate", 0, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
  }

  std::vector<LidarManager::MotorStatus> resp_vec {};
  for (size_t i = 0; i < 10; i++)
  {
    if (auto resp = getLidarManager()->readMotorStatus(1000))
    {
      resp_vec.push_back(*resp);
      msleep(10);
      continue;
    }
    LOG_INDEX_ERROR("获取电机信息失败");
    msleep(10);
  }

  if (resp_vec.empty())
  {
    setWorkModelFailMsg("获取电机信息失败");
    return ActionState::STATE_FAIL;
  }

  float motor_curr  = 0;
  float motor_speed = 0;
  for (const auto& each_resp : resp_vec)
  {
    motor_curr += static_cast<float>(each_resp.motor_current);
    motor_speed += static_cast<float>(each_resp.motor_speed);
  }
  motor_curr /= static_cast<float>(resp_vec.size());
  motor_curr /= 100;
  motor_speed /= static_cast<float>(resp_vec.size());
  uint32_t motor_status = resp_vec.back().motor_status;

  LOG_INDEX_INFO("直接获取电机转速: {}rpm, 母线电流: {}mA, 编码器状态: {:#x}", motor_speed, motor_curr, motor_status);

  is_pass &= getWorkModel()->addMeasureMessage("fsm_direct_motor_speed", motor_speed, rsfsc_lib::MEASURE_DATA_TYPE_INT);
  is_pass &= getWorkModel()->addMeasureMessage("fsm_motor_curr", motor_curr, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);

  for (uint32_t i = 0; i < 6; ++i)
  {
    is_pass &= getWorkModel()->addMeasureMessage(fmt::format("fsm_motor_coder_status_bit{}", i).c_str(),
                                                 (motor_status >> i) & 1U, rsfsc_lib::MEASURE_DATA_TYPE_INT);
  }

  if (!is_pass)
  {
    return ActionState::STATE_FAIL;
  }

  if (!getWorkModel()->addMeasureMessage("fsm_restore_time_sync_mode", getWorkModel()->restoreTimeSyncMode()))
  {
    setWorkModelFailMsg("恢复时间同步模式失败");
    return ActionState::STATE_FAIL;
  }

  return ActionState::STATE_SUCCESS;
}

ActionState SuccessHandler::handle()
{
  LOG_INFO("SuccessHandler");
  app()->getWidgetMes()->finishProcess(getLidarIndex(), LogTestStatus::LOG_TEST_STATUS_PASS);
  getWorkModel()->setTestState(TestState::PASS);
  return ActionState::STATE_FINAL;
}

ActionState FailHandler::handle()
{
  LOG_INFO("FailHandler");
  app()->getWidgetMes()->finishProcess(getLidarIndex(), LogTestStatus::LOG_TEST_STATUS_FAIL,
                                       getWorkModel()->getFailLabel(), getWorkModel()->getFailMsg());
  getWorkModel()->setTestState(TestState::FAILED);

  return ActionState::STATE_FINAL;
}

ActionState AbortHandler::handle()
{
  LOG_INFO("AbortHandler");
  app()->getWidgetMes()->finishProcess(getLidarIndex(), LogTestStatus::LOG_TEST_STATUS_ABORT,
                                       getWorkModel()->getFailLabel(), getWorkModel()->getFailMsg());
  getWorkModel()->setTestState(TestState::ABORT);
  return ActionState::STATE_FINAL;
}

ActionState FinalHandler::handle()
{
  LOG_INFO("FinalHandler");
  getLidarManager()->disconnect();
  getWorkModel()->powerOff();
  LOG_INDEX_DEBUG("状态机流程结束");
  return ActionState::STATE_END;
}

}  // namespace lidar
}  // namespace robosense
