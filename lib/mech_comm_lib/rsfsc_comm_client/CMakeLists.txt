cmake_minimum_required(VERSION 3.10)

project(
  rsfsc_comm_client
  VERSION 1.0.0
  LANGUAGES CXX)

# 设置包含目录变量
set(COMM_CLIENT_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)

# 查找并链接Boost库
find_package(Boost REQUIRED COMPONENTS system)

# TCP客户端库
add_library(rsfsc_tcp_client STATIC src/rsfsc_comm_client.cpp src/rsfsc_tcp_client.cpp src/rsfsc_serial_client.cpp)
add_library(rsfsc::tcp_client ALIAS rsfsc_tcp_client)

target_include_directories(rsfsc_tcp_client PUBLIC $<BUILD_INTERFACE:${COMM_CLIENT_INCLUDE_DIR}>
                                                   $<INSTALL_INTERFACE:include>)

target_link_libraries(rsfsc_tcp_client PUBLIC Boost::system Boost::boost)

# UDP客户端库
add_library(rsfsc_udp_client STATIC src/rsfsc_comm_client.cpp src/rsfsc_udp_client.cpp)
add_library(rsfsc::udp_client ALIAS rsfsc_udp_client)

target_include_directories(rsfsc_udp_client PUBLIC $<BUILD_INTERFACE:${COMM_CLIENT_INCLUDE_DIR}>
                                                   $<INSTALL_INTERFACE:include>)

target_link_libraries(rsfsc_udp_client PUBLIC Boost::system Boost::boost)

# 串口客户端库
add_library(rsfsc_serial_client STATIC src/rsfsc_comm_client.cpp src/rsfsc_serial_client.cpp)
add_library(rsfsc::serial_client ALIAS rsfsc_serial_client)

target_include_directories(rsfsc_serial_client PUBLIC $<BUILD_INTERFACE:${COMM_CLIENT_INCLUDE_DIR}>
                                                      $<INSTALL_INTERFACE:include>)

target_link_libraries(rsfsc_serial_client PUBLIC Boost::system Boost::boost)

file(GLOB SOURCES CONFIGURE_DEPENDS "src/*.cpp")

add_library(rsfsc_comm_client STATIC ${SOURCES})

target_include_directories(rsfsc_comm_client PUBLIC $<BUILD_INTERFACE:${COMM_CLIENT_INCLUDE_DIR}>
                                                    $<INSTALL_INTERFACE:include>)

target_link_libraries(rsfsc_comm_client PUBLIC rsfsc_tcp_client rsfsc_udp_client rsfsc_serial_client)

# 设置所有目标的编译特性
foreach(target rsfsc_tcp_client rsfsc_udp_client rsfsc_serial_client)
  target_compile_features(${target} PUBLIC cxx_std_17)
endforeach()
