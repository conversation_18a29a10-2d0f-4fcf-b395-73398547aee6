﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "message_browser.h"

#include <QtCore/QSettings>
#include <QtGui/QPalette>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QTextBrowser>
#include <QtWidgets/QVBoxLayout>

#include <iostream>

namespace robosense
{
namespace lidar
{
namespace rsfsc_lib
{
constexpr std::array<const char*, MessageBrowser::TEXT_TYPE_SIZE> MessageBrowser::TEXT_COLOR;
constexpr std::array<const char*, MessageBrowser::TEXT_TYPE_SIZE> MessageBrowser::TEXT_TYPE_STR;
constexpr std::array<const char*, MessageBrowser::TEXT_TYPE_SIZE> MessageBrowser::TEXT_TYPE_NAME;

MessageBrowser::MessageBrowser(const QString& _project_name, QWidget* _parent) :
  QWidget(_parent), text_browser_(new QTextBrowser(this)), is_refresh_(true), project_name_(_project_name)
{
  QVBoxLayout* layout_main = new QVBoxLayout;
  layout_main->setContentsMargins(0, 0, 0, 0);
  layout_main->setSpacing(0);

  QHBoxLayout* layout_control = new QHBoxLayout;
  layout_control->setContentsMargins(0, 0, 0, 0);
  QPushButton* pushbutton_clear = new QPushButton(QString::fromUtf8("clear"), this);
  QPushButton* pushbutton_hold  = new QPushButton("hold", this);
  layout_control->addWidget(pushbutton_clear);
  layout_control->addWidget(pushbutton_hold);

  QHBoxLayout* layout_label = new QHBoxLayout;
  layout_label->setContentsMargins(0, 0, 0, 0);
  text_browser_->document()->setMaximumBlockCount(500);
  QColor background_color = text_browser_->palette().color(QPalette::ColorRole::Base);
  for (int i = 0; i < TEXT_TYPE_SIZE; ++i)
  {
    QLabel* label = new QLabel(this);
    label->setText(QString::fromUtf8(TEXT_TYPE_NAME.at(i)));
    label->setAlignment(Qt::AlignCenter);
    QPalette pe;
    pe.setColor(label->foregroundRole(), TEXT_COLOR.at(i));
    pe.setColor(label->backgroundRole(), background_color);
    label->setAutoFillBackground(true);
    label->setPalette(pe);
    layout_label->addWidget(label);
  }
  layout_main->addWidget(text_browser_);
  layout_main->addLayout(layout_control);
  layout_main->addLayout(layout_label);
  this->setLayout(layout_main);

  QObject::connect(pushbutton_clear, &QPushButton::clicked, this, &MessageBrowser::slotClear);
  QObject::connect(pushbutton_hold, &QPushButton::clicked, this, &MessageBrowser::slotHold);

  QSizePolicy size_policy = this->sizePolicy();
  size_policy.setHorizontalPolicy(QSizePolicy::Expanding);
  size_policy.setVerticalPolicy(QSizePolicy::Expanding);
  this->setSizePolicy(size_policy);
  readSetting();
}

MessageBrowser::~MessageBrowser()
{
  QSettings settings("RoboSense", project_name_ + "/message_browser");
  settings.setValue("message_browser_geometry", saveGeometry());
}

void MessageBrowser::readSetting()
{
  QSettings settings("RoboSense", project_name_ + "/message_browser");
  this->restoreGeometry(settings.value("message_browser_geometry").toByteArray());
}

void MessageBrowser::closeEvent(QCloseEvent* _event)
{
  QSettings settings("RoboSense", project_name_ + "/message_browser");
  settings.setValue("message_browser_geometry", saveGeometry());
  QWidget::closeEvent(_event);
}

void MessageBrowser::showEvent(QShowEvent* _event)
{
  readSetting();
  QWidget::showEvent(_event);
}

int MessageBrowser::parserMessage(const QString& _msg, QString& _valid_msg)
{
  int anti_bracket_index = _msg.indexOf("] ");
  if (_msg.startsWith("[") && anti_bracket_index > 0)
  {
    QString msg_type_str = _msg.mid(1, anti_bracket_index - 1);
    for (int type = 0; type < TEXT_TYPE_SIZE; ++type)
    {
      if (msg_type_str.compare(QString(TEXT_TYPE_STR.at(type))) == 0)
      {
        _valid_msg = _msg.right(_msg.size() - 1 - anti_bracket_index - 1);
        return type;
      }
    }
  }
  return TEXT_TYPE_SIZE;
}

void MessageBrowser::slotShowMessage(const QString& _msg)
{
  QString valid_msg = _msg;
  TextType type     = static_cast<TextType>(parserMessage(_msg, valid_msg));
  if (type < TEXT_TYPE_SIZE)
  {
    text_browser_->append(QString("<font color=") + QString::fromUtf8(TEXT_COLOR.at(type)) + QString(">") + valid_msg +
                          QString("</font> "));
    refresh();
  }
}

[[deprecated("Use slotShowMessage instance, which support RSFSCLog")]]//
void MessageBrowser::slotShowErrorText(const QString& _msg)
{
  text_browser_->append(QString("<font color=") + QString::fromUtf8(TEXT_COLOR.at(TEXT_TYPE_ERROR)) + QString(">") +
                        _msg + QString("</font> "));
  refresh();
}

[[deprecated("Use slotShowMessage instance, which support RSFSCLog")]]//
void MessageBrowser::slotShowWarningText(const QString& _msg)
{
  text_browser_->append(QString("<font color=") + QString::fromUtf8(TEXT_COLOR.at(TEXT_TYPE_WARNING)) + QString(">") +
                        _msg + QString("</font> "));
  refresh();
}

[[deprecated("Use slotShowMessage instance, which support RSFSCLog")]]//
void MessageBrowser::slotShowInfoText(const QString& _msg)
{
  text_browser_->append(QString("<font color=") + QString::fromUtf8(TEXT_COLOR.at(TEXT_TYPE_INFO)) + QString(">") +
                        _msg + QString("</font> "));
  refresh();
}

[[deprecated("Use slotShowMessage instance, which support RSFSCLog")]]//
void MessageBrowser::slotShowInfoVariant(const QString& _name, const QVariant& _value)
{
  QString msg = _name + QString(": ") + _value.toString();
  slotShowMessage(msg);
}

void MessageBrowser::refresh()
{
  if (is_refresh_)
  {
    text_browser_->moveCursor(text_browser_->textCursor().End);
  }
  // QApplication::processEvents(); 加上这个会导致多线程死锁，msg打印很快速，不使用该操作不会造成卡顿ui
}

void MessageBrowser::slotClear() { text_browser_->clear(); }

void MessageBrowser::slotHold()
{
  QPushButton* ptr_sender = qobject_cast<QPushButton*>(sender());
  if (QString("hold") == ptr_sender->text())
  {
    is_refresh_ = false;
    ptr_sender->setText("refresh");
  }
  else
  {
    is_refresh_ = true;
    ptr_sender->setText("hold");
  }
}
}  // namespace rsfsc_lib
}  // namespace lidar
}  // namespace robosense
