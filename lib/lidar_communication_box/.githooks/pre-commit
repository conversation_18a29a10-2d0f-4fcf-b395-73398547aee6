#! /usr/bin/env python3
# -*- coding: UTF-8 -*-

################################################################################
# Copyright 2022 RoboSense All rights reserved.
# Suteng Innovation Technology Co., Ltd. www.robosense.ai

# This software is provided to you directly by RoboSense and might
# only be used to access RoboSense LiDAR. Any compilation,
# modification, exploration, reproduction and redistribution are
# restricted without RoboSense's prior consent.

# THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
# WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
# OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
# INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
# HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
# STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
# IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.
################################################################################
# @file   pre-commit(.py)
# <AUTHOR> Liang, Antoine Chen (<EMAIL>)
# @brief     This file is used to check cpp code and cpp project's style according to rsfsg's rules
# @version 1.1.0
# @date 2022-12-14

import os
import re
import sys
import chardet
import time
from git import Repo
import gitlab
import subprocess
import colorama

# 有这些字符的文件不检查文件名。例：['CMakeList|CHANGELOG|LICENSE']
NOT_CHECK_NAME = ['.clang-format', '.clang-tidy', '.cmake-format.py']
# 单个文件不能超过设定的大小（单位为MB）
FILE_SIZE_MAX = 15
# 单个代码文档（包含C++和Python）不能超过设定的行数
CODE_LINE_MAX = 5000
# 文本文件
TEXT_FILE = ['.txt', '.csv', '.cpp', '.h',
             '.hpp', '.cc', '.hh', '.c', '.py', '.md', '.sh', '.desktop']
# 要求utf-8 with bom的文本文件
TEXT_FILE_START_WITH_BOM = ['.txt', '.csv', '.cpp',
                            '.h', '.hpp', '.cc', '.hh', '.c', '.md']
# C++代码文件
CPP_CODE_FILE = ['.cpp', '.h', '.hpp', '.cc', '.hh']
# C++源代码文件
CPP_SOURCE_CODE_FILE = ['.cpp', '.cc']
# 代码文件
CODE_FILE = ['.cpp', '.h', '.hpp', '.cc', '.hh', '.c', '.py', '.sh']

# 必需存在的文件
NEED_FILE_LIST = ['README.md', 'CHANGELOG.md',
                  'LICENSE', '.gitignore', '.clang-format', '.clang-tidy']
# 必需存在的路径（以仓库作为根目录的路径，注意开头不要带路径连接符。示例：['src', 'include/']）
NEED_PATH_LIST = []
# STRING_CHECK_LIST1 = ['init'] # 检查某些应有字符串是否存在
# 检查某些不应有字符串是否存在(待添加：QtChart)
STRING_CHECK_LIST2 = ['[\W]192.168.1.20[\W]',
                      'SIGNAL\(', 'SLOT\(', 'fromLocal8Bit', '<<<<<<< HEAD']
# 可执行文件的列表，其不用检查文件权限
EXECUTABLE_FILE_LIST = ['.sh', '.desktop', '.so',
                        '.exe']  # python文件既可以是脚本，也可以是普通代码文件，不检查
# 首层列表参数
FIRST_LAY_LIST = ['package.xml', 'cmake', '.gitlab', '.githooks', 'example', 'src', 'include', 'lib', 'README.md', 'CHANGELOG.md', 'LICENSE', '.gitignore', '.gitmodules', '.gitlab-ci.yml', 'doc', 'img',
                  'config', 'data', 'test', 'script', 'resource', 'launch', 'msg', 'ui', 'CMakeLists.txt', '.gitignore', '.clang-format', '.clang-tidy', 'cspell.json']

# file_path = './' # 仓库的路径，用get_project_base_path返回的就可以
# 审查的白名单，按相对路径填写，每行填写一个文件。(示例：src/main.cpp(按照"/"填写相对路径))
check_ignore_path = './.rs_check_ignore'
# 汇总错误项目文件的保存路径
check_fail_list_path = './rs_check_fail_list.csv'

# TODO:
# 1. desktop要不要check code spell
# 2. cpp_check
# 3. clang static analyzer


class RoboSenseCheck:
    def __init__(self) -> None:
        self.get_project_base_path()
        self.check_ros_package()
        self.get_white_list()
        self.get_git_ls_file()
        self.get_changed_files()
        self.is_check_all_successfully = True

    def check_self_define_rules(self):
        self.check_format(self.diff_files_list)
        self.check_identifier_naming(self.diff_files_list)

        self.check5(self.git_ls_file)
        self.check13(self.git_ls_file)
        for file_name in self.git_ls_file:
            if os.path.isdir(os.path.join(self.base_path, file_name)):
                continue
            print('checking: ' + file_name + '...')
            if not os.path.exists(os.path.join(self.base_path, file_name)):
                print("\n\n\nerror: 仓库内文件无法在路径中找到，请先git add!")
                os._exit(-1)
            code = ''
            file_name_suffix = os.path.splitext(file_name)[1]
            if file_name_suffix in TEXT_FILE:
                with open(file=os.path.join(self.base_path, file_name), mode='r') as file:
                    code = file.read()
            self.check1(file_name)
            self.check2(file_name)
            self.check3(file_name)
            self.check4(file_name)
            self.check6(file_name, code)
            self.check7(file_name, code)
            self.check8(file_name, code)
            self.check9(file_name)
            self.check10(file_name, code)
            self.check11(file_name, code)
            self.check12(file_name)

    def get_project_base_path(self):
        current_path = os.path.dirname(os.path.realpath(__file__))
        self.base_path = current_path[0: current_path.rfind('/') + 1]
        self.git = Repo(self.base_path).git
        print('base_path: ' + self.base_path[0:-1])
        # 支持docker上当前用户与git文件所属用户不同的情况
        # self.git.config("--global", "--add", "safe.directory",
        #                 self.base_path[0:-1])
        # 配置git可识别中文路径
        self.git.config("--global", "core.quotepath", "false")

    def check_ros_package(self):
        package_file = os.path.join(self.base_path, 'package.xml')
        if os.path.exists(package_file):
            self.is_ros_package = True
            self.compile_commands_json_path = os.path.join(self.base_path, "../../build")
        else:
            self.is_ros_package = False
            self.compile_commands_json_path = os.path.join(self.base_path, "build")

    def get_white_list(self):
        abs_ignore_path = os.path.join(self.base_path, check_ignore_path)
        if os.path.exists(abs_ignore_path):
            with open(file=abs_ignore_path, mode='r') as file:
                white_list_line = file.readline()
                self.white_list = []
                while white_list_line != '':
                    white_list_line = white_list_line.strip('\n')
                    self.white_list.append(white_list_line)  # 去掉最后一个字符\n
                    white_list_line = file.readline()
        else:
            self.white_list = []
        print('white_list: ' + str(self.white_list))

    def is_file_in_white_list(self, file_name):
        for white_file in self.white_list:
            if re.match(white_file, file_name):
                return True
        return False

    def is_folder_in_git(self, folder_name):
        if not os.path.isdir(os.path.join(self.base_path, folder_name)):
            return False
        for git_file in self.org_git_ls_file:
            if os.path.join(self.base_path, folder_name) + '/' in os.path.join(self.base_path, git_file):
                return True
        return False

    def get_changed_files(self):
        diff_files_str = self.git.diff(
            "--cached", "--name-only", "--diff-filter=ACM")
        org_diff_files_list = diff_files_str.split()
        self.diff_files_list = []
        for file_name in org_diff_files_list:
            if not self.is_file_in_white_list(file_name):
                self.diff_files_list.append(file_name)

    def get_git_ls_file(self):
        git_files = self.git.ls_files()
        self.org_git_ls_file = re.split('\n', git_files)
        self.git_ls_file = []
        for file_name in self.org_git_ls_file:
            if not self.is_file_in_white_list(file_name):
                self.git_ls_file.append(file_name)

    def check_format(self, check_file_list, is_in_place=False):
        print('checking: format...')
        cpp_files_list = []
        for file_name in check_file_list:
            file_name_suffix = os.path.splitext(file_name)[1]
            if file_name_suffix in CPP_CODE_FILE:
                cpp_files_list.append(os.path.join(self.base_path, file_name))
        if not len(cpp_files_list) == 0:
            cpp_files_str = ' '.join(cpp_files_list)
            # print('check files: ' + cpp_files_str)
            if is_in_place:
                return_code = subprocess.call(
                    "clang-format-13 -style=file -i " + cpp_files_str, shell=True)
            else:
                return_code = subprocess.call(
                    "clang-format-13 -style=file -Werror -n --verbose " + cpp_files_str, shell=True)
            if return_code != 0:
                self.is_check_all_successfully = False

    def check_code_spell(self, check_file_list):
        print('checking: code spell...')
        cpp_files_list = []
        for file_name in check_file_list:
            file_name_suffix = os.path.splitext(file_name)[1]
            # some definitions in CMakeLists.txt is illegal word, and there is no harm when CMakeLists.txt has an error code spell
            if file_name_suffix in TEXT_FILE and 'CMakeLists.txt' not in file_name:
                cpp_files_list.append(os.path.join(self.base_path, file_name))
        if not len(cpp_files_list) == 0:
            cpp_files_str = ' '.join(cpp_files_list)
            print('check files: ' + cpp_files_str)
            return_code = subprocess.call(
                "cd " + self.base_path + " && cspell --show-suggestions --show-context " + cpp_files_str, shell=True)
            if return_code != 0:
                self.is_check_all_successfully = False

    def check_identifier_naming(self, check_file_list):
        print('checking: identifier naming...')
        cpp_source_files_list = []
        for file_name in check_file_list:
            file_name_suffix = os.path.splitext(file_name)[1]
            tmp = file_name.split("/")
            name = tmp[len(tmp) - 1]
            if file_name_suffix in CPP_SOURCE_CODE_FILE:
                cpp_source_files_list.append(
                    os.path.join(self.base_path, file_name))
        if not len(cpp_source_files_list) == 0:
            cpp_source_files_str = ' '.join(cpp_source_files_list)
            print('check source files: ' + cpp_source_files_str)
            return_code = subprocess.call("clang-tidy-13 -p " + self.compile_commands_json_path +
                                          " -header-filter=.* -checks=-*,readability-identifier-naming " + cpp_source_files_str, shell=True)
            if return_code != 0:
                self.is_check_all_successfully = False

    def check_clang_tidy(self, check_file_list):
        print('checking: clang-tidy...')
        cpp_source_files_list = []
        for file_name in check_file_list:
            file_name_suffix = os.path.splitext(file_name)[1]
            if file_name_suffix in CPP_SOURCE_CODE_FILE:
                cpp_source_files_list.append(
                    os.path.join(self.base_path, file_name))
        if not len(cpp_source_files_list) == 0:
            cpp_source_files_str = ' '.join(cpp_source_files_list)
            print('check source files: ' + cpp_source_files_str)
            return_code = subprocess.call("clang-tidy-13 -p " + self.compile_commands_json_path +
                                          " -header-filter=.* " + cpp_source_files_str, shell=True)
            if return_code != 0:
                self.is_check_all_successfully = False
    
    def check_files_latest(self, in_place = False):
        check_result = True
        print('checking: if rules file is latest...')
        my_git_url = "http://gitlab.robosense.cn"  # gitlab的域名
        my_git_private_token = 'A9B1iRAehMHPiQcZ7f-u'
        gl = gitlab.Gitlab(url=my_git_url, private_token=my_git_private_token, timeout=2)
        try:
            project = gl.projects.get(id=4252)
        except:
            self.log_warning("网络连接失败，请检查网络；或者token无效，请联系本检查脚本的开发者进行更新")
            return False
        check_file_list = [".githooks/pre-commit", ".clang-format",
                         ".clang-tidy", ".cmake-format.py", ".gitignore", ".gitlab/merge_request_templates/Merge_Request.md"]  # 要读取的文件相对路径
        for check_file in check_file_list:
            remote_file = project.files.get(file_path=check_file, ref='master')
            # 第一次decode获得bytes格式的内容
            remote_file_content = remote_file.decode()
            # 第二次decode获得str
            remote_file_content = remote_file_content.decode()
            if not os.path.exists(os.path.join(self.base_path, check_file)):
                self.log_error(check_file + ' does not exists, fail to check')
                check_result = False
                continue
            with open(file=os.path.join(self.base_path, check_file), mode='r') as local_file:
                local_file_content = local_file.read()
            if check_file != ".gitignore" :
                if local_file_content != remote_file_content:
                    check_result = False
                    if in_place:
                        with open(file=os.path.join(self.base_path, check_file), mode='w') as local_file:
                            local_file.write(remote_file_content)
                        self.log_warning(check_file + ' is replace to latest')
                    else:
                        self.log_warning(check_file + ' is not same as project_template\'s master, please update your rule file')
            else:
                local_file_global_content = local_file_content[0 : local_file_content.find('# project define')]
                local_file_self_content = local_file_content[local_file_content.find('# project define') : -1]
                remote_file_content = remote_file_content[0 : remote_file_content.find('# project define')]
                if not remote_file_content in local_file_global_content:
                    check_result = False
                    if in_place:
                        with open(file=os.path.join(self.base_path, check_file), mode='w') as local_file:
                            local_file.write(remote_file_content + local_file_self_content)
                        self.log_warning(check_file + ' is replace to latest')
                    else:
                        self.log_warning(check_file + ' is not same as project_template\'s master, please update your rule file')
        return check_result

    def check1(self, file_name):
        """检查文件名(只检查代码文件和可执行文件)
        """
        for not_check_name in NOT_CHECK_NAME:
            if re.search(not_check_name, file_name) != None:
                return
        revised_file_name = file_name[file_name.rfind('/') + 1:]
        revised_file_name_prefix = os.path.splitext(revised_file_name)[0]
        revised_file_name_suffix = os.path.splitext(revised_file_name)[1]
        if revised_file_name_suffix in CODE_FILE or revised_file_name_suffix in EXECUTABLE_FILE_LIST:
            if re.match('^[a-z0-9_]+$', revised_file_name_prefix) == None:
                self.log_file_error(file_name, error[0])
                self.is_check_all_successfully = False
            else:
                pass
        else:
            pass

    def check2(self, file_name):
        """检查文本保存格式
        """
        file_name_suffix = os.path.splitext(file_name)[1]
        if file_name_suffix in TEXT_FILE_START_WITH_BOM:
            with open(file=os.path.join(self.base_path, file_name), mode='rb') as file:
                data = file.read()
                encoding = chardet.detect(data)['encoding']
            if encoding != None and encoding != 'utf-8-sig' and encoding != 'UTF-8-SIG':  # 文件是空时没有encoding
                self.log_file_error(file_name, error[1])
                self.is_check_all_successfully = False
            else:
                pass
        else:
            pass

    def check3(self, file_name):
        """检查文件大小
        """
        file_size = os.path.getsize(os.path.join(self.base_path, file_name))
        file_size = file_size / float(1024 * 1024)
        file_size = round(file_size, 2)
        if file_size > FILE_SIZE_MAX:
            self.log_file_error(file_name, error[2])
            self.is_check_all_successfully = False
        else:
            pass

    def check4(self, file_name):
        """检查代码文件行数
        """
        file_name_suffix = os.path.splitext(file_name)[1]
        if file_name_suffix in CODE_FILE:
            with open(file=os.path.join(self.base_path, file_name), mode='r') as file:
                code_line = len(file.readlines())
            if code_line > CODE_LINE_MAX:
                self.log_file_error(file_name, error[3])
                self.is_check_all_successfully = False
            else:
                pass
        else:
            pass

    def check5(self, file_list):
        """检查某些路径和文件的存在
        """
        print('checking: ' + error[4] + ' ...')
        # .cmake-format.py is place to .rs_check_ignore, cause it will be treated as python file, and be checked by other checker
        if not os.path.exists(os.path.join(self.base_path, '.cmake-format.py')):
            print('checking: ' + '.cmake-format.py' + '...')
            self.log_file_error('.cmake-format.py', error[4])
            self.is_check_all_successfully = False
        for need_file in NEED_FILE_LIST:
            need_file_exist = False
            for file_name in file_list:
                file_name = os.path.basename(file_name)
                if need_file == file_name:
                    need_file_exist = True
                    break
                else:
                    pass
            if need_file_exist == False:
                self.log_file_error('', error[4]+': '+need_file)
                self.is_check_all_successfully = False
            else:
                pass
        for need_path in NEED_PATH_LIST:
            if not os.path.exists(os.path.join(self.base_path, need_path)):
                self.log_file_error('', error[4]+': '+need_path)
                self.is_check_all_successfully = False
            else:
                pass

    def check6(self, file_name, code):
        """检查代码文件是否有应有的字符串
        """
        if file_name == 'CMakeLists.txt':
            if re.search('config core.hooksPath .githooks', code) == None or re.search("CMAKE_EXPORT_COMPILE_COMMANDS ON", code) == None or re.search("find_package\(Git QUIET\)", code) == None:
                self.log_file_error(
                    file_name, error[5]+': config core.hooksPath .githooks or set(CMAKE_EXPORT_COMPILE_COMMANDS ON) or find_package(Git QUIET)')
                self.is_check_all_successfully = False
        revised_file_name = file_name[file_name.rfind('/') + 1:]
        file_name_suffix = os.path.splitext(revised_file_name)[1]
        # if revised_file_name == "main.cpp":
        #     if re.search('::init[\W]', code) == None:
        #         log_error(file_name, error[5]+': init')
        #         global self.is_check_all_successfully
        #         self.is_check_all_successfully = False
        # else:
        #     pass
        if file_name_suffix == '.h':
            if re.search('ifndef', code) == None or re.search('endif', code) == None:
                self.log_file_error(file_name, error[5]+': ifndef/endif')
                self.is_check_all_successfully = False
        else:
            pass

    def check7(self, file_name, code):
        """检查desktop文件的sleep 5
        """
        file_name_suffix = os.path.splitext(file_name)[1]
        if file_name_suffix == '.desktop':
            if re.search('sleep', code, re.I) == None:
                self.log_file_error(file_name, error[6])
                self.is_check_all_successfully = False
        else:
            pass

    def check8(self, file_name, code):
        """检查版权声明
        """
        file_name_suffix = os.path.splitext(file_name)[1]
        if file_name_suffix in CODE_FILE:
            if re.search('Copyright [0-9]{4} RoboSense All rights reserved.', code) == None or \
                    re.search('Suteng Innovation Technology Co., Ltd. www.robosense.ai', code) == None:
                self.log_file_error(file_name, error[7])
                self.is_check_all_successfully = False
            else:
                pass
        else:
            pass

    def check9(self, file_name):
        """检查是否有gitignore中的文件
        """
        try:
            check_ignore_result = self.git.check_ignore("-v", file_name)
            if check_ignore_result != '':  # 如果有输出就说明是要被忽略的
                self.log_file_error(file_name, error[8])
                self.is_check_all_successfully = False
            else:  # 没有输出时会exit 1
                pass
        except:
            pass

    def check10(self, file_name, code):
        """检查markdown路径
        """
        file_name_suffix = os.path.splitext(file_name)[1]
        if file_name_suffix == '.md':
            # 判断没有绝对路径，ubuntu下路径的开头是(或"
            if re.search(r'[!w][A-Za-z]:\\|[!w][A-Za-z]:/', code) != None or re.search(r'\(/home/<USER>"/home/', code) != None:
                self.log_file_error(file_name, error[9])
                self.is_check_all_successfully = False
            else:
                pass
        else:
            pass

    def check11(self, file_name, code):
        """检查代码文件是否有不应有的字符串
        """
        file_name_suffix = os.path.splitext(file_name)[1]
        if file_name_suffix in CODE_FILE:
            for need_check_string in STRING_CHECK_LIST2:
                if re.search(need_check_string, code) != None:
                    self.log_file_error(file_name, error[10]+': '+need_check_string)
                    self.is_check_all_successfully = False
                else:
                    pass
        else:
            pass

    def check12(self, file_name):
        """检查文件权限
        """
        file_name_suffix = os.path.splitext(file_name)[1]
        if file_name_suffix == '.py':
            pass
        if not file_name_suffix in EXECUTABLE_FILE_LIST:
            file_access = oct(os.stat(os.path.join(
                self.base_path, file_name)).st_mode)[-3:]
            if file_access != '664':
                self.log_file_error(file_name, error[11])
                self.is_check_all_successfully = False
            else:
                pass
        else:
            file_execute_access = oct(
                os.stat(os.path.join(self.base_path, file_name)).st_mode)[-1:]
            if not file_execute_access in ['1', '3', '5', '7']:
                self.log_file_error(file_name, error[11])
                self.is_check_all_successfully = False
            else:
                pass

    def check13(self, git_file_list):
        """检查首层文件夹/文件命名
        """
        print('checking: ' + error[12] + ' ...')
        file_list = os.listdir(self.base_path)
        # print(file_list)
        # print(git_file_list)
        for file_name in file_list:
            if (file_name in git_file_list or self.is_folder_in_git(file_name)) and not file_name in FIRST_LAY_LIST:
                print('checking: ' + file_name + '...')
                self.log_file_error(file_name, error[12])
                self.is_check_all_successfully = False
            else:
                pass
    
    def log_file_error(self, file_name, error):
        # check_fail_list.write(file_name + ', 违反' + error + '\n')
        print(colorama.Fore.RED + '违反： ' + error)

    def log_error(self, error):
        print(colorama.Fore.RED + 'error： ' + error)

    def log_warning(self, msg):
        print(colorama.Fore.YELLOW + 'Warning: ' + msg)


if __name__ == '__main__':
    start_time = time.time()
    colorama.init(autoreset=True)
    robosense_check = RoboSenseCheck()
    is_all_rules_file_latest = robosense_check.check_files_latest()
    if len(sys.argv) != 1:
        if (sys.argv[1] == '1'):  # check format
            robosense_check.check_format(robosense_check.git_ls_file)
        elif (sys.argv[1] == '2'):  # check identifier naming
            robosense_check.check_identifier_naming(
                robosense_check.git_ls_file)
        elif (sys.argv[1] == '3'):  # check clang-tidy
            robosense_check.check_clang_tidy(robosense_check.git_ls_file)
        elif (sys.argv[1] == '4'):  # check code spell
            robosense_check.check_code_spell(robosense_check.git_ls_file)
        elif (sys.argv[1] == '5'): # check if rules file is latest
            if not is_all_rules_file_latest:
                robosense_check.is_check_all_successfully = False
        elif (sys.argv[1] == 'in_place_format'):  # clang-format in place
            robosense_check.check_format(robosense_check.git_ls_file, True)
        elif (sys.argv[1] == 'in_place_latest_file'):  # replace latest rules file in place
            robosense_check.check_files_latest(in_place=True)
        else:
            print('not support parameter: ' + sys.argv[1])
        end_time = time.time()
        print('--cost time: ' + str(end_time - start_time) + 's')
        if not is_all_rules_file_latest and sys.argv[1] != '5':
            robosense_check.log_warning('存在警告事项，请注意')
        if not robosense_check.is_check_all_successfully:
            robosense_check.log_error("检查存在错误，请修改")
            os._exit(1)
    else:
        error = ['check1--文件命名风格(小写加下划线)',
                 'check2--文件保存格式(C++及文本相关为utf-8 with bom)',
                 'check3--文件大小上限' + str(FILE_SIZE_MAX) + 'MB',
                 'check4--代码行数上限' + str(CODE_LINE_MAX) + 'MB',
                 'check5--必要文件和路径(不存在则报错)',
                 'check6--必要字符串(不存在则报错)',
                 'check7--desktop未sleep 5',
                 'check8--版权声明(不存在则报错)',
                 'check9--.gitignore文件(存在相关文件则报错)',
                 'check10--绝对路径(文档中使用了绝对路径则报错)',
                 'check11--非法字符串(存在则报错)',
                 'check12--文件权限(非执行文件664，可执行文件执行权限)',
                 'check13--首层文件(首层文件及文件夹命名规范)']
        print("审查内容：")
        for e in error:
            print(e)
        print('--------------------------------------------------------')
        robosense_check.check_self_define_rules()
        end_time = time.time()
        print('--cost time: ' + str(end_time - start_time) + 's')
        if not is_all_rules_file_latest:
            robosense_check.log_warning('存在警告事项，请注意')
    # check_fail_list.close()
        if not robosense_check.is_check_all_successfully:
            robosense_check.log_error("存在不符合commit条件的错误，请修改后再commit")
            os._exit(1)
    # else:
        # os.remove(check_fail_list_path)
