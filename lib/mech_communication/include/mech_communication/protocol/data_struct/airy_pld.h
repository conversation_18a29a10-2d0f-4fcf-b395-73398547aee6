﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef AIRY_PLD_DATA_STRUCT_H
#define AIRY_PLD_DATA_STRUCT_H

#include "mech.h"
#include <array>
#include <cstdint>
#include <unordered_map>
#include <variant>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
namespace airy_pld
{

// "系统状态：
// 0x00：默认
// 0x03：System Init
// 0x33：System Normal Run (No config state)
// 0x3C:  System Normal Run (Config state)
// 0xC0：System Standby
// 0xC3：System Fault"
enum class SystemState : uint8_t
{
  DEFAULT                     = 0x00,
  SYSTEM_INIT                 = 0x03,
  SYSTEM_NORMAL_RUN_NO_CONFIG = 0x33,
  SYSTEM_NORMAL_RUN_CONFIG    = 0x3C,
  SYSTEM_STANDBY              = 0xC0,
  SYSTEM_FAULT                = 0xC3
};
// Fault Level"系统状态：
// 0x00：默认
// 0x03：Fault Level 1
// 0x0C：Fault Level 2
// 0x30：Fault Level 3"
enum class FaultLevel : uint8_t
{
  DEFAULT = 0x00,
  LEVEL_1 = 0x03,
  LEVEL_2 = 0x0C,
  LEVEL_3 = 0x30
};
enum class IOStatus : uint8_t
{
  LOW  = 0,
  HIGH = 0xaa
};
enum class IntrusionStatus : uint8_t
{
  NO_INTRUSION = 0,
  INTRUSION    = 0xaa
};
enum class ObstacleStatus : uint8_t
{
  DEFAULT = 0x00,  // 无遮挡
  LEVEL_1 = 0x03,  // 轻微脏污或遮挡
  LEVEL_2 = 0x0C,  // 区域更严重的脏污或雨水遮挡
  LEVEL_3 = 0x30   // 严重遮挡或脏污
};
#pragma pack(push, 1)
struct DifopPacket
{
  uint64_t pkt_head;                // 0x55aa055a
  uint16_t motor_set_speed;         // 电机设置转速 300/600/1200
  uint32_t ip_src;                  // 以太网IP源地址
  uint32_t ip_dst;                  // 以太网IP目标地址
  std::array<uint8_t, 6> mac_addr;  // 雷达MAC地址
  uint16_t msop_port;               // MSOP端口
  uint16_t pkt_count;
  uint16_t difop_port;  // DIFOP端口
  SystemState system_state;
  FaultLevel fault_level;
  uint16_t fov_start;               // FOV起始角度, 0-359, 精度0.01°
  uint16_t fov_end;                 // FOV结束角度, 0-359, 精度0.01°
  uint16_t gpi_status;              // HW->PL->PS
  uint16_t lock_phase;              // 锁相相位, 0-360, 精度1°
  uint8_t top_firmware_reserve;     // 主板固件版本
  uint32_t top_firmware_version;    // 主板固件版本
  uint8_t bot_firmware_reserve;     // 底板固件版本
  uint32_t bot_firmware_version;    // 底板固件版本
  uint8_t app_firmware_reserve;     // APP固件版本
  uint32_t app_firmware_version;    // APP固件版本
  uint8_t motor_firmware_reserve;   // 电机固件版本
  uint32_t motor_firmware_version;  // 电机固件版本
  uint8_t cgi_firmware_reserve;     // cgi固件版本
  uint32_t cgi_firmware_version;    // cgi固件版本
  IOStatus ps_ossd_out_1a_ctl;      // 对应PS的AB2引脚PS_MIO12
  IOStatus ps_dig_io_01_ctl;        // 对应PS的AB3引脚PS_MIO16
  IOStatus pl_ossd_out_1b_ctl;      // 对应PL的A8引脚IO_L10P_AD2P_44
  IOStatus pl_dig_io_02_ctl;        // 对应PL的C7引脚IO_L8N_AD4N_44
  uint8_t zone_set_id;
  IOStatus ps_ossd_out_1a_det;        // PS_OSSD_OUT_1A_DET
  IOStatus ps_dig_io_01_det;          // PS_DIG_IO_01_DET
  IOStatus pl_ossd_out_1b_det;        // PL_OSSD_OUT_1B_DET
  IOStatus pl_dig_io_02_det;          // PL_DIG_IO_02_DET
  IOStatus pl_dig_io_03_ctl;          // PL_DIG_IO_03_CTL
  IOStatus pl_dig_io_03_det;          // PL_DIG_IO_03_DET
  IOStatus pl_dig_io_04_ctl;          // PL_DIG_IO_04_CTL
  IOStatus pl_dig_io_04_det;          // PL_DIG_IO_04_DET
  IOStatus pl_dig_i_01_det;           // PL_DIG_I_01_DET
  IOStatus pl_dig_i_02_det;           // PL_DIG_I_02_DET
  IOStatus pl_dig_i_03_det;           // PL_DIG_I_03_DET
  IOStatus pl_dig_i_04_det;           // PL_DIG_I_04_DET
  IOStatus pl_dig_i_05_det;           // PL_DIG_I_05_DET
  IOStatus pl_dig_i_06_det;           // PL_DIG_I_06_DET
  IOStatus pl_dig_i_07_det;           // PL_DIG_I_07_DET
  IOStatus pl_dig_i_08_det;           // PL_DIG_I_08_DET
  std::array<uint8_t, 202> reserve3;  // 预留
  mech::GpsBaudRate gps_baud_rate;    // GPS同步模式下的GPRMC波特率
  mech::MountType mount_type;         // 安装方式, 0:正装, 1:侧装, 2:测绘
  std::array<uint8_t, 2> reserve4;
  std::array<uint8_t, 6> sn;          // 产品序列号
  uint16_t zero_angle;                // 零度角标定值, 0-359.99, 单位0.01度
  mech::EchoMode echo_mode;           // 回波模式
  mech::TimeSyncMode time_sync_mode;  // 时间同步方式设置
  uint8_t time_sync_status;           // 时间同步状态, 0x00:未同步, 0x01:同步成功
  // std::array<uint8_t, 10> time;  // 时间, UTC时间格式, 前6个byte为秒时间戳, 后4个byte为微秒时间戳
  std::array<uint8_t, 6> time_sec;  // 时间, UTC时间格式, 前6个byte为秒时间戳, 后4个byte为微秒时间戳
  uint32_t time_nano;  // 时间, UTC时间格式, 前6个byte为秒时间戳, 后4个byte为微秒时间戳
  IntrusionStatus intrusion_status;  // 入侵检测状态
  std::array<uint8_t, 19> reserve6;
  uint32_t safe_area;       // 安全区域 单位mm
  uint8_t motor_dir;        // 电机正转反转标志, 0x00:正转, 0x01:反转
  uint32_t total_run_time;  // 设备运行总时间, 单位为分钟, 溢出后重新统计
  std::array<uint8_t, 9> reserve8;
  uint16_t reboot_count;  // 设备启动次数, 1-65535循环计数
  std::array<uint8_t, 4> reserve9;
  mech::GpsStatus gps_status;  // GPS状态
  std::array<uint8_t, 8> reserve10;
  uint8_t r_core_load;
  uint8_t a0_core_load;
  uint8_t a1_core_load;
  uint8_t a2_core_load;
  uint8_t a3_core_load;
  uint16_t realtime_phase;  // 实时相位, 单位度
  uint16_t realtime_speed;  // 电机实时转速, 单位RPM
  uint32_t start_time;      // 电机启动时间, 单位ms
  ObstacleStatus obstacle_status;
  std::array<uint8_t, 2> reserve12;
  std::array<uint8_t, 86> gprmc;              // GPRMC
  std::array<uint8_t, 288> vertical_calib;    // 垂直角校准
  std::array<uint8_t, 288> horizontal_calib;  // 水平角校准
  int16_t bot_vbus;                           // BB, 9*(1-20%)V~32*(1+20%)V
  int16_t bot_ibus;                           // BB, 0V~1.67A
  int16_t bot_sys_5v;                         // BB, 5*(1±15%)V
  int16_t bot_sys_12v;                        // BB, 12*(1±15%)V
  int16_t bot_vcco_psio0;                     // BB, 3.3*(1±8%)V, 片内监控电压3.3V
  uint16_t reserve13;                         // 预留
  int16_t bot_sys_1v2;                        // BB, 1.2*(1±8%)V
  int16_t bot_vcco_psddr;                     // BB, 1.1*(1±8%)V, 片内监控电压1.1V
  int16_t reserve14;                          // 预留
  int16_t top_vbus;                           // MainBoard, 11~14V
  int16_t top_sys_3v8;                        // MainBoard, 3.6~4.0V
  int16_t top_sys_3v3;                        // MainBoard, 3.1~3.5V
  int16_t top_sys_2v5;                        // MainBoard, A样暂不支持，B样增加检测电路
  int16_t top_sys_1v1;                        // MainBoard, 1.0~1.2V
  int16_t top_rx_vbd;                         // MainBoard, -25V~-11V
  int16_t top_tx_charge;                      // MainBoard, 3.1~3.5V
  int16_t top_sys_1v8;                        // MainBoard, 1.62~1.98V
  int16_t top_sys_1v0;                        // MainBoard, 0.9~1.1V
  int16_t bot_psintlp;                        // 片内电压，0.85V
  int16_t bot_psintfp;                        // 片内电压，0.85V
  int16_t bot_ps_aux;                         // 片内电压，1.8V
  int16_t bot_pl_vccint;                      // 片内电压，0.85V
  uint16_t reserve15;                         // 预留
  uint16_t total_power;                       // 整机功率, DIFOP: Data/100, 整机输入电压*总输入电流
  uint32_t imu_q_x;                           // imu标定数据
  uint32_t imu_q_y;                           // imu标定数据
  uint32_t imu_q_z;                           // imu标定数据
  uint32_t imu_q_w;                           // imu标定数据
  uint32_t imu_x;                             // imu标定数据
  uint32_t imu_y;                             // imu标定数据
  uint32_t imu_z;                             // imu标定数据
  int16_t bot_soc_temp;                       // 底板SOC温度, -40℃~120℃
  int16_t bot_fpd_temp;                       // 底板FPD温度, -40℃~120℃
  int16_t bot_lpd_temp;                       // 底板LPD温度, -40℃~120℃
  int16_t bot_imu_temp;                       // 底板IMU温度, -40℃~120℃
  int16_t top_fpga_temp;                      // 主板FPGA温度, -40℃~120℃
  int16_t top_tx_temp;                        // 主板TX温度, -40℃~120℃
  int16_t top_rx_n_temp;                      // 主板RX-N温度, -40℃~120℃
  int16_t top_rx_p_temp;                      // 主板RX-P温度, -40℃~120℃
  int16_t reserve_temp1;                      // 预留温度
  int16_t reserve_temp2;                      // 预留温度
  std::array<uint8_t, 94> reserve18;          // 预留
  uint16_t data_length;                       // 固定长度，0x4E0
  uint16_t fault_indicator;                   // 故障点标志
  uint32_t data_id;                           // 预留
  uint32_t crc32;                             // CRC32校验值
  uint16_t tail;                              // 帧尾, 0x0F 0xF0

  void toBigEndian();
  [[nodiscard]] bool isValid();

  // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
  [[nodiscard]] const char* data() const { return reinterpret_cast<const char*>(this); }

  [[nodiscard]] std::unordered_map<std::string, std::variant<float, double, int, uint32_t, std::string>> getDifopData()
    const;
};
#pragma pack(pop)
}  // namespace airy_pld
}  // namespace lidar
}  // namespace robosense

#endif  // AIRY_PLD_DATA_STRUCT_H