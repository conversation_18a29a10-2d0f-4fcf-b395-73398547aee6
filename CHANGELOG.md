﻿## v1.8.4.20250530
### Fixed
- 修正limit的命名不正确的问题

## v1.8.3.20250529
### Fixed
- 修正limit的命名不正确的问题

## v1.8.2.20250528
### Fixed
- 修正

## v1.8.1.20250527
### Added
- 增加扫码后直接一键运行
- 增加vbd校验

## v1.7.6.20250523
### Added
- 增加0360、0351项目编号

## v1.7.5.20250429
### Added
- 启用线束管控功能，支持线束使用次数统计及检测
- 在界面中显示线束SN输入控件
- 修复恢复同步信号的问题

## v1.7.4.20250407
### Feat
- 修改app版本卡控的阈值 `0x250327ff`

## v1.7.3.20250327
### Feat
- 电机信息修改为10次获取，求均值

## v1.7.2.20250220
### Fixed
- 修正复测模式也写sn
- 修正脉冲宽度设定值

## v1.7.1.20250220
### Fixed
- 修正编码器状态卡控，编码器不卡控测速、无感测速差距过大
- 修正转速阈值放宽到`590~610`

## v1.7.0.20240208
### Feat
- 增加复测模式
- 增加电机转速、母线电流、编码器状态卡控
### Fixed
- 修正主板总输入电压11~14

## v1.6.0.20240120
### Fixed
- 修正阈值参数与老化保持一致
- 修正配置文件版本卡控
- 删除配置写入
- 修正连通性测试流程一次性过，不进行重启

## v1.5.1.20240113
### Fixed
- `SN`由`MES`管控

## v1.5.0.20241223
### Added
- 增加配置文件写入可选
- 修正恢复`GPS`模式时候，直接恢复为`GPTP`模式
- 增加光通误码测试

## v1.4.3.20241210
### Fixed
- 修正固件版本名字显示
- 去除掉启动电流检测
- 修正固件版本不通过的时候中断测试
- 增加功率检测时间
- 增加打开数据文件夹
- 收窄温度阈值
- 调整盒子检测功率、盒子检测电流、盒子检测电压、实时转速、底板电压、输入电流、发射温度阈值范围
- 删除底板fpga内核温度

## v1.4.2.20241209
### Added
- 修正功率检测在顶板启动完成再检测

## v1.4.1.20241209
### Added
- 增加写入配置后的延时操作

## v1.4.0.20241206
### Added
- 添加版本检测
- 增加写入SN延时1s后再设置GPS模式
- 增加日志放入到测试里边

## v1.3.0.20241205
### Added
- 添加盒子检测电压、检测功率

## v1.2.2.20241204
### Fixed
- 添加获取同步time sync 次数

## v1.2.1.20241204
### Added
- 增加修改sn
- 增加配置文件写入
- 增加配置文件版本读取
- 增加负压延迟时间
### Fixed
- 修正当前接收问题超限问题

## v1.1.0.20241202
### Added
- 增加实时显示

## v0.3.0.20241129
### Added
- 放宽阈值

## v0.2.0.20241122
### Added
- 添加设置同步状态模式

## v0.1.0.20241121
### Added
- 添加msop包超时管控
- 添加获取sync管控

## v0.0.1.20241115
### Added
- 添加初版软件
