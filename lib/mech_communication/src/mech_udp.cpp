﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "mech_udp.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <boost/asio.hpp>
#include <boost/asio/error.hpp>
#include <boost/bind/bind.hpp>
#include <memory>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

using boost::asio::deadline_timer;
using boost::asio::ip::udp;
using boost::system::error_code;

MechUdp::MechUdp(std::size_t _len) : port_(0), pkt_length_(_len), msg_header_("MechUdp::"), flag_thread_run_(false)
{
  ptr_io_context_ = std::make_unique<boost::asio::io_context>();
}

MechUdp::~MechUdp()
{
  LOG_INDEX_DEBUG("{} ~MechUdp", msg_header_);
  stop();
}

bool MechUdp::start(const std::string& _ip, const uint16_t _port, const std::string& _group_ip)
{
  ip_         = _ip;
  port_       = _port;
  msg_header_ = fmt::format("MechUdp [{}:{}({})] -> ", _ip, _port, pkt_length_);

  //TODO: this fuction can restart
  if (ptr_socket_ && ptr_socket_->is_open())
  {
    flag_thread_run_.store(true);
    if (ptr_thread_ != nullptr)
    {
      ptr_thread_.reset();
    }
    ptr_thread_ = std::make_shared<std::thread>(&MechUdp::dataProcess, this);
    return true;
  }

  try
  {
    ptr_socket_ = std::make_unique<udp::socket>(*ptr_io_context_);
    ptr_socket_->open(udp::v4());
    ptr_socket_->set_option(udp::socket::reuse_address(true));
    ptr_socket_->bind(udp::endpoint(udp::v4(), port_));
    recv_buffer_.resize(pkt_length_);
    startReceive();

    if (!_group_ip.empty())
    {
      ptr_socket_->set_option(udp::socket::reuse_address(true));
      ptr_socket_->set_option(boost::asio::ip::multicast::join_group(boost::asio::ip::make_address(_group_ip)));
    }
    flag_thread_run_.store(true);
    ptr_thread_ = std::make_shared<std::thread>(&MechUdp::dataProcess, this);
  }
  catch (const std::exception& e)
  {
    LOG_INDEX_ERROR("start udp failed: " + std::string(e.what()));
    return false;
  }

  LOG_INDEX_DEBUG(msg_header_ + "bind success");
  return true;
}

void MechUdp::startReceive()
{
  ptr_socket_->async_receive(boost::asio::buffer(recv_buffer_), [this](const error_code& _ec, std::size_t _length) {
    if (!_ec)
    {
      handleReceive(_length);
    }
    else if (_ec == boost::asio::error::operation_aborted)
    {
      LOG_INDEX_DEBUG(msg_header_ + "startReceive -> async_receive operation_aborted");
    }
    else if (_ec == boost::asio::error::connection_reset)
    {
      LOG_INDEX_DEBUG(msg_header_ + "startReceive -> async_receive connection_reset");
    }
    else if (_ec == boost::asio::error::connection_refused)
    {
      LOG_INDEX_DEBUG(msg_header_ + "startReceive -> async_receive connection_refused");
    }

    if (flag_thread_run_.load())
    {
      startReceive();
    }
  });
}

bool MechUdp::stop()
{
  bool result = true;
  try
  {
    if (flag_thread_run_.load())
    {
      flag_thread_run_.store(false);
    }
    if (ptr_socket_ != nullptr)
    {
      error_code error_code;
      auto error = ptr_socket_->cancel(error_code);
      error      = ptr_socket_->close(error_code);
    }

    if (ptr_io_context_ != nullptr)
    {
      ptr_io_context_->stop();
    }

    // 检查是否在工作线程中调用stop()，避免死锁
    if (ptr_thread_ != nullptr && ptr_thread_->joinable())
    {
      if (ptr_thread_->get_id() == std::this_thread::get_id())
      {
        // 在工作线程中调用stop()，不能join自己，使用detach
        LOG_INDEX_DEBUG(msg_header_ + "stop -> called from worker thread, detaching");
        ptr_thread_->detach();
      }
      else
      {
        // 在其他线程中调用stop()，可以安全join
        LOG_INDEX_DEBUG(msg_header_ + "stop -> udp thread start joining");
        ptr_thread_->join();
        LOG_INDEX_DEBUG(msg_header_ + "stop");
      }
    }
  }
  catch (const std::exception& exception)
  {
    LOG_INDEX_ERROR(msg_header_ + std::string("stop -> stop udp failed: ") + exception.what());
    result = false;
  }

  if (nullptr != ptr_socket_)
  {
    ptr_socket_.reset();
  }
  if (nullptr != ptr_thread_)
  {
    ptr_thread_.reset();
  }

  // 注意：不在这里清理 vec_cb_，让它在对象析构时自然清理
  // 这样可以避免与正在执行的 handleReceive 产生竞态条件

  return result;
}

void MechUdp::regRecvCallback(const std::function<void(const char*)>& _callback)
{
  std::lock_guard<std::mutex> lock(callbacks_mutex_);
  vec_cb_.emplace_back(_callback);
}

std::optional<mech::DifopPacket> MechUdp::getOneDifopPacket(const std::string& _ip,
                                                            const uint16_t _port,
                                                            const uint32_t _timeout_s)
{
  if (pkt_length_ < sizeof(mech::DifopPacket))
  {
    LOG_INDEX_ERROR("pkt_length{}不能小于difop{}大小", pkt_length_, sizeof(mech::DifopPacket));
    return {};
  }
  mech::DifopPacket difop_packet {};
  std::mutex mutex;
  boost::asio::steady_timer timer(*ptr_io_context_, boost::asio::chrono::seconds(_timeout_s));
  regRecvCallback([&](const char* _data) {
    if (recv_buffer_.size() < sizeof(mech::DifopPacket))
    {
      return;
    }
    std::lock_guard<std::mutex> lock(mutex);
    memcpy(&difop_packet, _data, sizeof(mech::DifopPacket));
    if (!difop_packet.isValid())
    {
      return;
    }
    timer.cancel();
  });
  if (!start(_ip, _port))
  {
    return {};
  }
  std::promise<bool> promise;
  auto future = promise.get_future();
  timer.async_wait([&](const error_code& _ec) {
    if (_ec)
    {
      promise.set_value(false);
      return;
    }
    promise.set_value(true);
  });
  if (future.get())
  {
    LOG_INDEX_ERROR("获取difop包超时10s");
    stop();
    return {};
  }
  stop();
  if (!difop_packet.isValid())
  {
    LOG_INDEX_ERROR("获取数据无效");
    return {};
  }
  return difop_packet;
}

void MechUdp::handleReceive(std::size_t _length)
{
  // 检查线程运行标志，避免在停止过程中调用回调函数
  if (!flag_thread_run_.load())
  {
    return;
  }

  if (_length == pkt_length_)
  {
    // 创建回调函数的副本，使用互斥锁保护
    std::vector<std::function<void(const char*)>> callbacks_copy;

    {
      std::lock_guard<std::mutex> lock(callbacks_mutex_);

      // 在锁保护下再次检查运行状态
      if (!flag_thread_run_.load())
      {
        return;
      }

      // 安全地复制回调函数容器
      callbacks_copy = vec_cb_;
    }

    // 在锁外执行回调函数，避免长时间持有锁
    for (const auto& callback_func : callbacks_copy)
    {
      // 在每次调用前检查运行状态
      if (!flag_thread_run_.load())
      {
        break;  // 如果停止标志被设置，立即退出
      }

      try
      {
        if (callback_func)  // 检查函数对象是否有效
        {
          callback_func(recv_buffer_.data());
        }
      }
      catch (const std::bad_function_call& bad_func_exception)
      {
        LOG_INDEX_ERROR(msg_header_ + "bad function call in callback: " + bad_func_exception.what());
        break;  // 遇到无效函数调用，立即退出
      }
      catch (const std::exception& exception)
      {
        LOG_INDEX_ERROR(msg_header_ + "callback function error: " + exception.what());
      }
    }
  }
}

void MechUdp::dataProcess()
{
  LOG_INDEX_DEBUG(msg_header_ + "dataProcess start");

  try
  {
    // 创建 work_guard 确保 io_context 不会因为没有工作而退出
    // 注意：当 io_context::stop() 被调用时，run() 仍然会退出，即使有 work_guard
    auto work_guard = boost::asio::make_work_guard(*ptr_io_context_);

    // run() 会阻塞直到所有工作完成或 stop() 被调用
    // stop() 调用会导致 run() 退出，不管是否有 work_guard
    std::size_t handlers_run = ptr_io_context_->run();
    LOG_INDEX_DEBUG(msg_header_ + "dataProcess completed, handlers run: " + std::to_string(handlers_run));

    // work_guard 在作用域结束时自动释放
  }
  catch (const std::exception& exception)
  {
    LOG_INDEX_ERROR(msg_header_ + "dataProcess exception: " + exception.what());
  }

  LOG_INDEX_DEBUG(msg_header_ + "dataProcess end");
}

}  // namespace lidar
}  // namespace robosense