﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "serial_port_driver.h"
#include "rsfsc_log/rsfsc_log.h"

#include <boost/asio/read.hpp>
#include <boost/asio/write.hpp>
#include <boost/bind.hpp>
#include <boost/lambda/lambda.hpp>
#include <iostream>

#include <stdio.h>
#include <sys/file.h>
#include <unistd.h>

#define RS_SERIAL_PORT_DRIVER_LOW_LATENCY 1

namespace robosense
{
namespace lidar
{
SerialPortDriver::SerialPortDriver() :
  ptr_serial_port_(nullptr),
  ptr_thread_(nullptr),
  max_buffer_save_(1024),
  max_buffer_read_(1024),
  flag_thread_run_(false)
{
  ptr_io_service_.reset(new boost::asio::io_service);
}

SerialPortDriver::~SerialPortDriver()
{
  if (ptr_serial_port_)
  {
    closeSerial();
  }
  ptr_io_service_.reset();
}

bool SerialPortDriver::openSerial(const std::string& _port_name,
                                  const BaudRate _baud_rate,
                                  const FlowControl _flow_control,
                                  const Parity _parity,
                                  const StopBits _stop_bits,
                                  const CharacterSize _character_size)
{
  file_id_ = open(_port_name.c_str(), O_RDWR | O_NOCTTY | O_NONBLOCK);
  if (file_id_ == -1)
  {
    std::cerr << "Failed to open serial port." << std::endl;
    return false;
  }

  // 尝试获取文件锁,非阻塞方式
  if (flock(file_id_, LOCK_EX | LOCK_NB) == -1)
  {
    close(file_id_);
    return false;
  }

  if (ptr_serial_port_)
  {
    RSFSCLog::getInstance()->error("SerialPortDriver::open -> please close the previous one before open another");
    return false;
  }
  try
  {
    ptr_serial_port_.reset(new boost::asio::serial_port(*ptr_io_service_, _port_name));
    if (!ptr_serial_port_->is_open())
    {
      ptr_serial_port_.reset(nullptr);
      return false;
    }
  }
  catch (const std::exception& error)
  {
    RSFSCLog::getInstance()->error(std::string("SerialPortDriver::open -> catch a throw error: ") + error.what());
    return false;
  }
  ptr_serial_port_->set_option(boost::asio::serial_port::baud_rate(_baud_rate));
  ptr_serial_port_->set_option(
    boost::asio::serial_port::flow_control(boost::asio::serial_port::flow_control::type(_flow_control)));
  ptr_serial_port_->set_option(boost::asio::serial_port::parity(boost::asio::serial_port::parity::type(_parity)));
  ptr_serial_port_->set_option(
    boost::asio::serial_port::stop_bits(boost::asio::serial_port::stop_bits::type(_stop_bits)));
  ptr_serial_port_->set_option(boost::asio::serial_port::character_size(_character_size));

#if RS_SERIAL_PORT_DRIVER_LOW_LATENCY
#  include <linux/serial.h>
#  include <sys/ioctl.h>
  auto native = ptr_serial_port_->native_handle();  // serial_port_ is the boost's serial port class.
  struct serial_struct serial;
  ioctl(native, TIOCGSERIAL, &serial);
  serial.flags |= ASYNC_LOW_LATENCY;  // (0x2000)
  ioctl(native, TIOCSSERIAL, &serial);
#endif  // RS_SERIAL_PORT_DRIVER_LOW_LATENCY

  // start to listen data from serial port
  read_buffer_.clear();
  flag_thread_run_ = true;
  ptr_thread_.reset(new std::thread([this]() { threadReadMonitor(); }));

  return true;
}

bool SerialPortDriver::closeSerial()
{
  if (!ptr_serial_port_)
  {
    return true;
  }
  if (!isConnect())
  {
    RSFSCLog::getInstance()->warn("SerialPortDriver::close -> serial port is not open, close anyway");
  }
  // shut down thread
  flag_thread_run_ = false;
  if (ptr_serial_port_)
  {
    ptr_serial_port_->cancel();
  }
  if (ptr_serial_port_)
  {
    ptr_serial_port_->close();
  }
  if (ptr_thread_ && ptr_thread_->joinable())
  {
    ptr_thread_->join();
  }

  RSFSCLog::getInstance()->debug("ptr_thread_ is over");

  // 释放锁
  if (flock(file_id_, LOCK_UN) == -1)
  {
    RSFSCLog::getInstance()->warn("Failed to unlock file");
    close(file_id_);
    return false;
  }
  close(file_id_);

  ptr_serial_port_.reset(nullptr);
  ptr_thread_.reset(nullptr);

  return true;
}

bool SerialPortDriver::isConnect() const
{
  if (!ptr_serial_port_)
  {
    return false;
  }
  return ptr_serial_port_->is_open();
}

bool SerialPortDriver::write(const std::vector<uint8_t>& _data, const std::size_t _msec)
{
  if (!isConnect())
  {
    RSFSCLog::getInstance()->error("SerialPortDriver::write -> please open serial port before write data");
    return false;
  }
  std::stringstream stream;
  stream << "write cmd -> ";
  for (auto it : _data)
  {
    stream << std::hex << static_cast<uint16_t>(it) << " ";
  }
  robosense::lidar::RSFSCLog::getInstance()->debug(stream.str());

  std::deque<char>().swap(read_buffer_);

  // 设置超时定时器
  boost::asio::deadline_timer deadline(*ptr_io_service_);
  deadline.expires_from_now(boost::posix_time::milliseconds(_msec));
  boost::system::error_code ec = boost::asio::error::would_block;

  try
  {
    // 写操作
    boost::asio::write(*ptr_serial_port_, boost::asio::buffer(_data), boost::asio::transfer_all(), ec);
    if (ec)
    {
      RSFSCLog::getInstance()->error("SerialPortDriver::write -> 写操作失败: " + ec.message());
      return false;
    }

    // 检查超时
    deadline.wait(ec);
    if (ec && ec != boost::asio::error::operation_aborted)
    {
      RSFSCLog::getInstance()->error("SerialPortDriver::write -> 超时错误: " + ec.message());
      return false;
    }
  }
  catch (const boost::system::system_error& e)
  {
    RSFSCLog::getInstance()->error("SerialPortDriver::write -> 异常: " + std::string(e.what()));
    return false;
  }

  return true;
}

bool SerialPortDriver::readAll(std::deque<char>& _data)
{
  if (!isConnect())
  {
    RSFSCLog::getInstance()->error("SerialPortDriver::read -> please open serial port before read data");
    return false;
  }
  {
    std::lock_guard<std::mutex> lg(mutex_read_buffer_);
    // RSFSCLog::getInstance()->debug("lock guard readAll");
    _data.swap(read_buffer_);
    read_buffer_.clear();
  }
  return true;
}

// static void handleReceive(const boost::system::error_code& _ec,
//                           std::size_t _length,
//                           boost::system::error_code* _out_ec,
//                           std::size_t* _out_length)
// {
//   *_out_ec     = _ec;
//   *_out_length = _length;
// }

void SerialPortDriver::run(std::chrono::steady_clock::duration _timeout)
{
  // Restart the io_context, as it may have been left in the "stopped" state
  // by a previous operation.
  ptr_io_service_->restart();

  // Block until the asynchronous operation has completed, or timed out. If
  // the pending asynchronous operation is a composed operation, the deadline
  // applies to the entire operation, rather than individual operations on
  // the socket.
  ptr_io_service_->run_for(_timeout);

  // If the asynchronous operation completed successfully then the io_context
  // would have been stopped due to running out of work. If it was not
  // stopped, then the io_context::run_for call must have timed out.
  if (!ptr_io_service_->stopped())
  {
    // Close the serial port to cancel the outstanding asynchronous operation.
    ptr_serial_port_->cancel();

    // Run the io_context again until the operation completes.
    ptr_io_service_->run();
  }
}

void SerialPortDriver::threadReadMonitor()
{
  std::vector<char> recv_buffer(max_buffer_read_, 0x0);
  while (flag_thread_run_.load())
  {
    boost::system::error_code ec = boost::asio::error::would_block;
    // 同步读取数据
    std::size_t read_size = 0;
    try
    {
      ptr_serial_port_->async_read_some(boost::asio::buffer(recv_buffer),
                                        [&](const boost::system::error_code& _result_error, std::size_t _result_n) {
                                          ec        = _result_error;
                                          read_size = _result_n;
                                        });
      run(std::chrono::seconds(10));
      if (ec == boost::asio::error::operation_aborted)
      {
        // RSFSCLog::getInstance()->error("SerialPortDriver::read -> fail by error: timeout");
        continue;
      }

      if (ec && ec != boost::asio::error::eof)
      {
        RSFSCLog::getInstance()->error("SerialPortDriver::read -> fail by error: " + ec.message());
        continue;
      }
    }
    catch (const boost::system::system_error& e)
    {
      RSFSCLog::getInstance()->error("SerialPortDriver::read -> exception: " + std::string(e.what()));
      continue;
    }

    {
      std::lock_guard<std::mutex> lg(mutex_read_buffer_);
      // RSFSCLog::getInstance()->debug("lock guard monitor");
      if ((read_buffer_.size() + read_size) <= max_buffer_save_)
      {
        read_buffer_.insert(read_buffer_.end(), recv_buffer.begin(), recv_buffer.begin() + read_size);
      }
      else if (read_size < max_buffer_save_)
      {
        std::size_t cut_size = read_size + read_buffer_.size() - max_buffer_save_;
        read_buffer_.erase(read_buffer_.begin(), read_buffer_.begin() + cut_size);
        read_buffer_.insert(read_buffer_.end(), recv_buffer.begin(), recv_buffer.begin() + read_size);
        RSFSCLog::getInstance()->warn("SerialPortDriver::threadReadMonitor -> read buffer is overflow with size: " +
                                      std::to_string(read_buffer_.size()));
      }
      else
      {
        std::size_t cut_size = read_size - max_buffer_save_;
        read_buffer_.clear();
        read_buffer_.insert(read_buffer_.end(), recv_buffer.begin() + cut_size, recv_buffer.begin() + read_size);
        RSFSCLog::getInstance()->warn("SerialPortDriver::threadReadMonitor -> read buffer is overflow with size: " +
                                      std::to_string(read_buffer_.size()));
      }
      for (const auto& data_cb : vec_deal_data_cb_)
      {
        data_cb(read_buffer_);
      }
    }
  }
}

void SerialPortDriver::setMaxBufferSave(const std::size_t _max_size) { max_buffer_save_ = _max_size; }

void SerialPortDriver::setMaxBufferReadOnce(const std::size_t _max_size) { max_buffer_read_ = _max_size; }

void SerialPortDriver::regDealDataCallback(const std::function<void(std::deque<char>&)>& _callback)
{
  if (flag_thread_run_)
  {
    RSFSCLog::getInstance()->warn(
      "SerialPortDriver::regDealDataCallback -> you must regist callback before open serial port");
    return;
  }
  vec_deal_data_cb_.emplace_back(_callback);
}

}  // namespace lidar
}  // namespace robosense
