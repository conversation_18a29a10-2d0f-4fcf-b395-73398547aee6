﻿import os
import csv
from pathlib import Path
import math
import argparse # Import argparse

# LIMIT_SUFFIX is a constant, keep it here
LIMIT_SUFFIX = "_limit.csv"

def sanitize_identifier(name):
    return name.strip().lower()

def parse_limit_csv(file_path):
    limits = []
    with open(file_path, encoding="utf-8-sig") as f:
        reader = csv.reader(f)
        for row in reader:
            if not row or all(cell.strip() == "" for cell in row):
                continue
            if row[0].startswith("#") or row[0] == "参数名称":
                continue
            if len(row) < 5:
                continue
            try:
                name = sanitize_identifier(row[0])
                min_val = row[1].strip()
                max_val = row[2].strip()
                unit = row[3].strip().lower()
                is_text = unit in ("text", "string")
                if not is_text:
                    try:
                        min_val = float(min_val)
                        max_val = float(max_val)
                    except ValueError as e:
                        print(f"⚠️ 期望数字但转换失败 {row} in {file_path}：{e}")
                limits.append((name, min_val, max_val, is_text))
            except Exception as e:
                print(f"⚠️ 错误解析数据行 {row} in {file_path}：{e}")
    return limits

# Modify generate_header_file to take output_dir as an argument
def generate_header_file(csv_file, limits, output_dir_path: Path):
    output_file = output_dir_path / (csv_file.stem + ".h") # Use passed output_dir_path
    lines = []
    lines.append("#pragma once")
    # lines.append('#include "limit_types.h"') # Consider if this needs to be relative to output_dir_path too
    lines.append("#include <limits>")
    lines.append(f"// Auto-generated from {csv_file.name}")

    base_name = csv_file.stem
    ns_parts = base_name.split("_")
    if len(ns_parts) < 2:
        # Instead of raising an error that stops CMake, print and skip.
        print(f"❌ CSV 文件名格式错误 '{csv_file.name}'，必须包含一个 '_' 分隔符，例如 ruby_limit.csv. Skipping.")
        return # Skip this file

    product_ns = ns_parts[0]

    lines.append("namespace robosense {")
    lines.append("namespace lidar {")
    lines.append("namespace limit {")
    lines.append(f"namespace {product_ns} {{")
    lines.append("")

    def cpp_float(val):
        if isinstance(val, float) and math.isnan(val):
            return "std::numeric_limits<double>::quiet_NaN()"
        return str(val)

    for name, min_val, max_val, is_text in limits:
        lines.append(f'constexpr const char* {name} = "{name}";')

    lines.append(f"}}  // namespace {product_ns}")
    lines.append("}  // namespace limit")
    lines.append("}  // namespace lidar")
    lines.append("}  // namespace robosense")

    output_file.parent.mkdir(parents=True, exist_ok=True)
    with open(output_file, "w", encoding="utf-8") as f:
        f.write("\n".join(lines))
    print(f"✅ Generated header: {output_file}")


# Modify ensure_common_limit_types to take output_dir as an argument
def ensure_common_limit_types(output_dir_path: Path):
    common_header = output_dir_path / "limit_types.h" # Use passed output_dir_path
    common_header.parent.mkdir(parents=True, exist_ok=True)

    header_content = '''#pragma once

#include <string_view>

namespace robosense {
namespace lidar {
namespace limit {

struct RangeDouble {
    double min;
    double max;
};

struct RangeString {
    std::string_view min;
    std::string_view max;
};

template<typename T>
struct NamedRange {
    const char* name;
    T value;
    operator const char*() const { return name; }
    operator std::string_view() const { return name; }
};

}  // namespace limit
}  // namespace lidar
}  // namespace robosense
'''
    with open(common_header, "w", encoding="utf-8") as f:
        f.write(header_content)
    print(f"✅ 已生成公共头文件: {common_header}")

def main():
    parser = argparse.ArgumentParser(description="Generate C++ header files from limit CSVs.")
    parser.add_argument("--config-dir", required=True, help="Directory containing the _limit.csv files.")
    parser.add_argument("--output-dir", required=True, help="Directory to write the generated .h files.")
    args = parser.parse_args()

    config_path = Path(args.config_dir)
    output_path = Path(args.output_dir) # This is the base output directory for all generated files

    if not config_path.exists():
        print(f"❌ Config directory '{config_path}' 不存在")
        return
    if not config_path.is_dir():
        print(f"❌ Config path '{config_path}' is not a directory.")
        return

    # If you want to use ensure_common_limit_types, pass the output_path
    # ensure_common_limit_types(output_path)

    processed_any = False
    for csv_file in config_path.glob(f"*{LIMIT_SUFFIX}"):
        print(f"Processing {csv_file}...")
        limits = parse_limit_csv(csv_file)
        if limits:
            generate_header_file(csv_file, limits, output_path) # Pass output_path
            processed_any = True
        else:
            print(f"⚠️ 没有有效数据: {csv_file}")
    
    if not processed_any:
        print(f"⚠️ No CSV files ending with '{LIMIT_SUFFIX}' found or processed in '{config_path}'.")


if __name__ == "__main__":
    main()