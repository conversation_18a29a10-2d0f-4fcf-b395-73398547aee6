#include "rsfsc_comm_client/rsfsc_comm_client.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <boost/asio/executor_work_guard.hpp>
#include <boost/asio/io_context.hpp>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

RsfscCommClient::RsfscCommClient() : io_running_(false), io_context_(std::make_unique<boost::asio::io_context>()) {}

RsfscCommClient::~RsfscCommClient() { stopIoContext(); }

void RsfscCommClient::setConfig(const CommClientConfig& _config) { config_ = _config; }

CommClientConfig RsfscCommClient::getConfig() const { return config_; }

bool RsfscCommClient::connect(const TcpClientConfig& _tcp_config)
{
  CommClientConfig config;
  config.tcp_config = _tcp_config;
  setConfig(config);
  msg_header_ = fmt::format("RsfscCommClient [{}:{}] :: ", _tcp_config.host, _tcp_config.port);
  return connect();
}
bool RsfscCommClient::connect(const SerialClientConfig& _serial_config)
{
  CommClientConfig config;
  config.serial_config = _serial_config;
  setConfig(config);
  msg_header_ = fmt::format("RsfscCommClient [{}] :: ", _serial_config.port_name);
  return connect();
}

void RsfscCommClient::startIoContext()
{
  if (io_thread_.joinable())
  {
    return;
  }
  work_guard_ = std::make_unique<boost::asio::executor_work_guard<boost::asio::io_context::executor_type>>(
    io_context_->get_executor());
  io_thread_ = std::thread([this]() {
    LOG_INDEX_DEBUG(msg_header_ + "start io_context");
    io_context_->restart();
    io_context_->run();
    LOG_INDEX_DEBUG(msg_header_ + "stop io_context");
  });
}

void RsfscCommClient::stopIoContext()
{
  work_guard_.reset();
  if (io_thread_.joinable())
  {
    io_thread_.join();
  }
}

}  // namespace lidar
}  // namespace robosense