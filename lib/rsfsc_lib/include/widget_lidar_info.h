﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   widget_lidar_info.h
 * <AUTHOR> Chen (<EMAIL>), Lennox Wang(<EMAIL>)
 * @brief  a widget to show info for MEMS lidar 
 * @version v1.1.9
 * @date 2024-11-04
 **/
#ifndef RSFSCLIB_WIDGET_LIDAR_INFO_H
#define RSFSCLIB_WIDGET_LIDAR_INFO_H
#include "rsfsc_msg.h"
#include <string>
#include <utility>  // for pair

#include <QtCore/QString>
#include <QtWidgets/QWidget>

class QLineEdit;
class QSpinBox;
class QComboBox;
class QGridLayout;
class QPushButton;

namespace robosense
{
namespace lidar
{
namespace rsfsc_lib
{
class LineEditLimitKeyboard;
class WidgetLogSetting;

class WidgetLidarInfo final : public QWidget
{
  Q_OBJECT
public:
  /**
   * @brief     Construct a new Widget Lidar Info object
   * 
   * @param     _program_name      your program name, this should be same as what you 
   *                               put in WidgetLogSetting's constructor
   * @param     _parent            usually is your WidgetLidarInfo
   */
  explicit WidgetLidarInfo(const QString& _program_name, quint32 _lidar_index, QWidget* _parent);
  explicit WidgetLidarInfo(WidgetLidarInfo&&)      = delete;
  explicit WidgetLidarInfo(const WidgetLidarInfo&) = delete;
  WidgetLidarInfo& operator=(WidgetLidarInfo&&) = delete;
  WidgetLidarInfo& operator=(const WidgetLidarInfo&) = delete;
  ~WidgetLidarInfo() final;
  friend WidgetLogSetting;

  /**
   * @brief     Set which project will not check sn regex, if not check, return project code receive anyway. 
   *            If check and SN not satisfy project's regex, a PROJECT_NOT_FIND will return
   *
   * @param     _not_check_sn_project      split with ;
   */
  static void setNotCheckSNProject(const QString& _not_check_sn_project);
  /**
   * @brief     Set a Fixed Project Code, if set, project code after SN will be ignore, 
   *            and default project code will be set to _pc
   * 
   * @param     _pc                fixed project code, if set to PROJECT_NOT_FIND, fixed project code will be disable
   */
  void setFixedProjectCode(ProjectCode _pc);
  /**
   * @brief     Set a Fixed Lidar Install Position, if set, lidar install position will be fixed, 
   *            and default lidar install position will be set to _lip
   * 
   * @param     _lip               fixed lidar install position, if set to LIDAR_INSTALL_POSITION_NOT_FIND
   *                               fixed lidar install position will be disable
   */
  void setFixedLidarInstallPosition(const LidarInstallPosition _lip);
  /**
   * @brief     reset ip widget to *************, msop port widget to 6699 and difop port widget to 7788 
   */
  void resetIPandPort();
  /**
   * @brief     set ip widget to ************* + index, msop port widget to 6699 + index and difop port widget to 7788 + index
   */
  void setIPandPortAccording2Index();

  quint32 getLidarIndex() const;
  void setLidarSN(const QString& _lidar_sn);
  void setIP(const QString& _ip);
  void setMSOP(quint16 _msop);
  void setDIFOP(quint16 _difop);
  void setProjectCode(ProjectCode _pc);
  void setLidarInstallPosition(LidarInstallPosition _lip);
  void setCarType(const QString& _car_type);
  QString getLidarSN() const;
  QString getCableSN() const;
  quint32 getCableUseTimes() const;
  quint32 getCableUseTimesLimit() const;
  void setCableUseTimes(quint32 _use_times);
  void setCableUseTimesLimit(quint32 _use_time_limit);
  static void setCableUseTimesDefaultLimit(quint32 _use_time_default_limit);
  QString getProjectCodeStr() const;
  ProjectCode getProjectCodeIndex() const;
  QString getCarType() const;
  LidarInstallPosition getLidarInstallPosition() const;
  QString getIP() const;
  quint16 getMSOPPort() const;
  quint16 getDIFOPPort() const;
  std::pair<std::string, uint16_t> getNetworkInfo() const;
  QString getPcapPath() const;
  QString getParamPath() const;
  QString getAdditionalInfo() const;

  void setLidarSNPos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setCableSNPos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setCableUseTimesInfoPos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setCableUseTimesLimitInfoPos(int _row,
                                    int _column,
                                    int _row_span    = 1,
                                    int _column_span = 1,
                                    bool _show_title = true);
  void setProjectCodePos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setProjectInfoPos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setCarTypePos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setLidarPositionPos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setIPPos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setMSOPPos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setDIFOPPos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setLidarParamPos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setPcapPathPos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setAdditionalInfoPos(int _row, int _column, int _row_span = 1, int _column_span = 1, bool _show_title = true);
  void setLidarSNWidgetFocus();
  void setCableSNWidgetFocus();

Q_SIGNALS:
  /**
   * @brief     this signal will be emitted when a Return or Enter was pressed in lineedit_sn
   */
  void signalLidarNameInputFinished();
  void signalCableSNInputFinished();
  void signalProjectCodeChanged(int);

private Q_SLOTS:
  void slotCheckIP();
  void slotPcapPath();
  void slotLidarParam();
  void slotSpliceSNAndProjectCode();
  void slotReplaceLidarSNErrorSymbol();

private:
  void readSetting();
  void setWidgetPos(QWidget* _widget,
                    int _row,
                    int _column,
                    int _row_span              = 1,
                    int _column_span           = 1,
                    const QString& _show_title = QString(""));
  static QString spliceSN(const QString& _org_text);
  static bool snRegex(const QString& _sn, ProjectCode _pc);
  // NOTE change getProjectCode from public to protected, so user will not use incorrectly
  // terminal user will change QComboBox of project code from ui, which LineEditLimitKeyboard was not informed
  // so call getProjectCode will lead to an incorrect result
  ProjectCode getProjectCodePrivate(const QString& _sn_cache) const;

  void setLidarSNPrivate(const QString& _sn);
  void setProjectCodeStr(const QString& _str);
  void setLidarPosition(LidarInstallPosition _lip);
  void setLidarPositionStr(const QString& _str);
  void clearLidarSN();
  void clearCableSN();
  void enableAllInfoChanged(bool _enable);
  void enableTechnicianInfoChanged(bool _enable);
  void enableManagerInfoChanged(bool _enable);
  QString getLidarSNSuffix() const;
  void setCurProcessLidarSN(const QString& _lidar_sn);
  bool isLidarCheckLogStateFinish() const;
  static void exitAfterSomeTime(const size_t _second);
  static Qt::ConnectionType getSyncType();

private:
  QGridLayout* layout_lidar_info_;
  LineEditLimitKeyboard* lineedit_lidar_serial_number_;
  LineEditLimitKeyboard* lineedit_cable_serial_number_;
  QSpinBox* spinbox_cable_use_times_;
  QSpinBox* spinbox_cable_use_times_limit_;
  QComboBox* combobox_project_;
  QLineEdit* lineedit_car_type_;
  QComboBox* combobox_lidar_position_;
  QLineEdit* lineedit_ip_;
  QSpinBox* spinbox_msop_;
  QSpinBox* spinbox_difop_;
  QPushButton* pushbutton_lidar_param_;
  QPushButton* pushbutton_pcap_path_;
  QLineEdit* lineedit_lidar_param_;
  QLineEdit* lineedit_pcap_path_;
  QLineEdit* lineedit_additional_info_;

  QString program_name_;
  quint32 lidar_index_;
  std::string log_prefix_;
  static QString not_check_sn_project_;  // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)
  ProjectCode fixed_project_code_;
  LidarInstallPosition lidar_install_position_ { LIDAR_INSTALL_POSITION_NOT_FIND };
  static quint32 use_times_default_limit_;  // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)
  QString lidar_sn_suffix_;
  QString cur_process_lidar_sn_;
};
}  // namespace rsfsc_lib
}  // namespace lidar
}  // namespace robosense

#endif  // RSFSCLIB_WIDGET_LIDAR_INFO_H
