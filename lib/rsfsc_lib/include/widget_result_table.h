﻿/******************************************************************************
* Copyright 2020 RoboSense All rights reserved.
* Suteng Innovation Technology Co., Ltd. www.robosense.ai

* This software is provided to you directly by RoboSense and might
* only be used to access RoboSense LiDAR. Any compilation,
* modification, exploration, reproduction and redistribution are
* restricted without RoboSense's prior consent.

* THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
* WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
* DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.
*****************************************************************************/

#ifndef RSFSCLIB_WIDGET_RESULT_TABLE_H
#define RSFSCLIB_WIDGET_RESULT_TABLE_H

#include "csv_parser.h"
#include "widget_log_setting.h"
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableWidget>
#include <qobject.h>
#include <qwidget.h>
#include <vector>

namespace robosense
{
namespace lidar
{
namespace rsfsc_lib
{

class WidgetResultTable : public QTableWidget
{
  Q_OBJECT
public:
  /**
   * @brief     Construct a new Widget Result Table object
   * 
   * @param     _lidar_index       the corresponding index you register by registerWidgetResultTable, start from 1
   * @param     _parent            usually is your WidgetResultTable
   */
  explicit WidgetResultTable(quint32 _lidar_index, QWidget* _parent = Q_NULLPTR);
  explicit WidgetResultTable(WidgetResultTable&&)      = delete;
  explicit WidgetResultTable(const WidgetResultTable&) = delete;
  WidgetResultTable& operator=(WidgetResultTable&&) = delete;
  WidgetResultTable& operator=(const WidgetResultTable&) = delete;
  ~WidgetResultTable() override                          = default;

public:
  /**
   * @brief add measure message for LimitInfo, from WidgetLogSetting::addMeasureMessage
   * 
   * @param _limit_info LimitInfo from CsvParser
   * @param _data       data from current test
   * @return true 
   * @return false 
   */
  bool addMeasureMessage(const LimitInfo& _limit_info, const double _data, const MeasureDataType _data_type);

  /**
   * @brief add measure message for LimitInfo, from WidgetLogSetting::addMeasureMessage
   * 
   * @param _limit_info LimitInfo from CsvParser
   * @param _data       data from current test
   * @return true 
   * @return false 
   */
  bool addMeasureMessage(const LimitInfo& _limit_info, const std::string& _data);

  /**
   * @brief show from WidgetLogSetting::finishProcess
   * 
   */
  void showResult();

  /**
   * @brief show from WidgetLogSetting::checkAllState
   * 
   */
  void clearResult();

  /**
   * @brief get current lidar index
   * 
   * @return quint32 
   */
  quint32 getLidarIndex() const;

private:
  static void exitAfterSomeTime(const size_t _second);
  static Qt::ConnectionType getSyncType();

private Q_SLOTS:
  void showResultPrivate();
  void resetRowCount();

private:
  struct MeasureMessageString
  {
    // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
    explicit MeasureMessageString(const std::string& _describe,
                                  const std::string& _data,
                                  const std::string& _min_th,
                                  const std::string& _max_th,
                                  const std::string& _detail) :
      describe(_describe), data(_data), min_th(_min_th), max_th(_max_th), detail(_detail)
    {}
    MeasureMessageString(MeasureMessageString&& _md) = default;

    MeasureMessageString(const MeasureMessageString& _md) = default;

    MeasureMessageString& operator=(const MeasureMessageString&) = default;

    MeasureMessageString& operator=(MeasureMessageString&&) = default;

    ~MeasureMessageString() = default;
    std::string describe;
    std::string data;
    std::string min_th;
    std::string max_th;
    std::string detail;
  };

  std::vector<LimitInfo> limit_vec_;
  std::map<std::string, double> value_map_;
  std::map<std::string, MeasureDataType> value_type_map_;
  std::vector<MeasureMessageString> measure_msg_string_vec_;
  quint32 lidar_index_;
};
}  // namespace rsfsc_lib
}  // namespace lidar
}  // namespace robosense

#endif  // RSFSCLIB_WIDGET_RESULT_TABLE_H
