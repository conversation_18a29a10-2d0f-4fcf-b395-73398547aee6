﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   mech_udp.h
 * <AUTHOR> Chen (<EMAIL>), Sloan Xi (<EMAIL>), Vance Huang (<EMAIL>)
 * @brief     you can use MECHUDP to capture UDP data upload from lidar which defined by RoboSense
 * @version 1.0.1
 * @date 2022-10-20
 * 
 * Copyright (c) 2021, RoboSense Co.,Ltd. - www.robosense.ai
 * All Rights Reserved.
 *  
 * You can not use, copy or spread without official authorization.
 * 
 * If you find any BUG or improvement in MECHUDP, please contact the authors, so we can share your idea  
 * 
 */

#ifndef MECH_UDP_H
#define MECH_UDP_H

#include "mech_communication/protocol/data_struct/mech.h"
#include <atomic>
#include <boost/asio/deadline_timer.hpp>
#include <boost/asio/ip/udp.hpp>
#include <cstdint>
#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#include <vector>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
class MechUdp
{
public:
  /**
   * @brief     Construct a new MechUdp object
   * 
   * @param     _len               captured udp's length is not equal with _len will be delete
   */
  explicit MechUdp(std::size_t _len);
  explicit MechUdp(MechUdp&&)      = delete;
  explicit MechUdp(const MechUdp&) = delete;
  MechUdp& operator=(MechUdp&&) = delete;
  MechUdp& operator=(const MechUdp&) = delete;
  ~MechUdp();

  [[nodiscard]] int getLogIndex() const { return log_index_; }
  void setLogIndex(const int _index) { log_index_ = _index; }

  /**
   * @brief     reset socket and start a thread to capture data
   * 
   * @param     _ip                not used in here
   * @param     _port              udp port of local computer
   * @param     _group_ip          set the join group ip
   * @retval    true               start successfully
   * @retval    false              start failed
   */
  bool start(const std::string& _ip, const uint16_t _port, const std::string& _group_ip = "");

  void startReceive();

  /**
   * @brief     close socket and delete thread   
   */
  bool stop();

  void handleReceive(std::size_t _length);

  /**
   * @brief     _callback will be called if capture a udp that satisfy the rule
   * 
   * @param     _callback          callback function
   */
  void regRecvCallback(const std::function<void(const char*)>& _callback);

  /**
   * @brief     Get the One Difop Packet
   * 
   * @return    std::optional<mech::DifopPacket> 
  **/
  std::optional<mech::DifopPacket> getOneDifopPacket(const std::string& _ip,
                                                     const uint16_t _port,
                                                     const uint32_t _timeout_s = 10);

private:
  void dataProcess();
  void checkDeadline();

private:
  int log_index_ = -1;
  std::string ip_;
  uint16_t port_;
  std::size_t pkt_length_;
  std::string msg_header_;

  std::unique_ptr<boost::asio::ip::udp::socket> ptr_socket_;
  std::unique_ptr<boost::asio::io_context> ptr_io_context_;
  std::vector<std::function<void(const char*)>> vec_cb_;
  mutable std::mutex callbacks_mutex_;  // 保护回调函数容器的互斥锁

  std::atomic<bool> flag_thread_run_;
  std::shared_ptr<std::thread> ptr_thread_;

  std::vector<char> recv_buffer_;
};
}  // namespace lidar
}  // namespace robosense

#endif  // MECH_UDP_H