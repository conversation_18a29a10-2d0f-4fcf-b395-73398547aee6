﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "app_controller.h"
#include "app_event.h"
#include "rsfsc_lib/include/widget_result_table.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "ui_mainwindow.h"
#include "widgets/widget_para_setting.h"
#include "work_model/work_model_factory.h"
#include <QDesktopServices>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

AppController::AppController(MainWindow* _main_window, QObject* _parent) : QObject(_parent), main_window_(_main_window)
{
  initFsm();
  initSignalSlot();
  checkAndInitBox();
}
AppController::~AppController()
{
  for (auto& [lidar_index, fsm] : fsm_map_)
  {
    fsm->quit();
  }
  lidar_communication_box_.reset();
  checkAndInitBox();
}

void AppController::initFsm()
{
  auto para_info_ptr = main_window_->widget_para_setting_->getCurrParaInfo();
  for (int i = 1; i <= main_window_->widget_para_setting_->getLidarNum(); ++i)
  {
    work_model_map_[i] = WorkModelFactory::createWorkModel(main_window_->lidar_info_map_[i].widget_lidar_info);
    fsm_map_[i]        = work_model_map_[i]->createFsm();
    auto& work_model   = work_model_map_[i];
    auto& fsm          = fsm_map_[i];

    fsm->setWorkModelVec({ work_model });
    work_model->setParaInfo(*para_info_ptr);
    fsm->setStateEndCallback([this, i, work_model]() {
      main_window_->lidar_info_map_[i].label_test_state->setState(work_model->getTestState());
    });
    work_model->setUpdateTestStateCb([this, i](const TestState _test_state) {
      main_window_->lidar_info_map_[i].label_test_state->setState(_test_state);
    });

    connect(main_window_->lidar_info_map_[i].button_open_data_folder, &QPushButton::clicked, [this, i]() {
      QDir path = work_model_map_[i]->getPath().result_dir;
      if (path.exists())
      {
        QDesktopServices::openUrl(QUrl::fromLocalFile(path.absolutePath()));
      }
    });
  }
}

void AppController::initSignalSlot()
{
  connect(main_window_->ui_->button_one_key_run, &QPushButton::clicked, this, &AppController::slotOneKeyRunClicked,
          Qt::QueuedConnection);
  connect(work_model_map_[1]->getLidarManager()->getLidarInfo(),
          &rsfsc_lib::WidgetLidarInfo::signalLidarNameInputFinished, this, &AppController::slotOneKeyRunClicked,
          Qt::QueuedConnection);
  connect(work_model_map_[1]->getLidarManager()->getLidarInfo(), &rsfsc_lib::WidgetLidarInfo::signalProjectCodeChanged,
          this, [this]() {
            LOG_INFO("项目代码已更改, 固定为: {}",
                     work_model_map_[1]->getLidarManager()->getLidarInfo()->getProjectCodeStr());
            work_model_map_[1]->getLidarManager()->getLidarInfo()->setFixedProjectCode(
              work_model_map_[1]->getLidarManager()->getLidarInfo()->getProjectCodeIndex());
          });
  connect(main_window_->ui_->button_get_current, &QPushButton::clicked, this,
          &AppController::slotButtonGetCurrentClicked, Qt::QueuedConnection);
  connect(main_window_->ui_->button_get_sync, &QPushButton::clicked, this, &AppController::slotButtonGetSyncClicked,
          Qt::QueuedConnection);
  connect(main_window_->ui_->button_get_firmware, &QPushButton::clicked, this,
          &AppController::slotButtonGetFirmwareClicked, Qt::QueuedConnection);
  connect(main_window_->ui_->checkbox_switch_power, &QCheckBox::stateChanged, this,
          &AppController::slotCheckboxSwitchPowerStateChanged, Qt::QueuedConnection);
  connect(main_window_->ui_->combobox_box_port_name, &QComboBox::currentTextChanged, this, [this]() {
    if (lidar_communication_box_ != nullptr)
    {
      lidar_communication_box_.reset();
    }
  });

  for (int i = 1; i <= main_window_->widget_para_setting_->getLidarNum(); ++i)
  {
    connect(main_window_->lidar_info_map_[i].button_connect, &QPushButton::clicked, [this, i]() {
      if (main_window_->lidar_info_map_[i].button_connect->text() == "连接")
      {
        auto handler = fsm_map_[i]->getStateHandler(ActionState::STATE_CONNECT_LIDAR);
        handler->handleState();
      }
      else if (main_window_->lidar_info_map_[i].button_connect->text() == "断开连接")
      {
        work_model_map_[i]->getLidarManager()->disconnect();
      }
    });
  }
}

bool AppController::checkAndInitBox()
{
  if (lidar_communication_box_ == nullptr || !lidar_communication_box_->isConnect())
  {
    lidar_communication_box_ = std::make_shared<LidarCommunicationBox>();
    if (!lidar_communication_box_->connect(main_window_->ui_->combobox_box_port_name->currentText().toStdString()))
    {
      LOG_ERROR("盒子打开串口{}失败", main_window_->ui_->combobox_box_port_name->currentText());
      return false;
    }

    for (auto& [lidar_index, model] : work_model_map_)
    {
      lidar_communication_box_->switchLidarPower(model->getLidarIndex(), false);
      model->setLidarCommunicationBoxPtr(lidar_communication_box_);
    }
  }
  return true;
}
bool AppController::checkBeforeRun()
{
  bool res = true;
  for (auto& [index, model] : work_model_map_)
  {
    auto lidar_sn = model->getLidarManager()->getLidarInfoSn();
    if (lidar_sn.isEmpty())
    {
      app()->signalShowErrorMessageBox("输入框雷达SN不能为空");
      res = false;
    }
  }
  if (!checkAndInitBox())
  {
    LOG_ERROR("盒子初始化失败");
    res = false;
  }

  return res;
}

void AppController::updatePara()
{
  auto para_info_ptr = main_window_->widget_para_setting_->getCurrParaInfo();
  for (auto& model : work_model_map_)
  {
    model.second->setParaInfo(para_info_ptr);
  }
}

void AppController::slotOneKeyRunClicked()
{
  LOG_INFO("One key run clicked");
  if (main_window_->ui_->button_one_key_run->text() == "一键运行")
  {
    updatePara();
    if (!checkBeforeRun())
    {
      LOG_ERROR("请检查外设连接状态");
      return;
    }
    for (auto& [lidar_index, fsm] : fsm_map_)
    {
      fsm->startup(ActionState::STATE_CHECK_ALL_STATE);
    }
  }
  else if (main_window_->ui_->button_one_key_run->text() == "停止运行")
  {
    // for (auto& [lidar_index, fsm] : fsm_map_)
    // {
    //   fsm->quit();
    // }
    LOG_INFO("暂不支持停止运行, 请等待测试完成");
  }
}

void AppController::slotButtonGetCurrentClicked()
{
  if (!checkAndInitBox())
  {
    return;
  }

  uint32_t curr   = 0;
  int lidar_index = main_window_->ui_->combobox_box_lidar_index->currentText().toInt();

  if (!lidar_communication_box_->getElectricity(lidar_index, curr))
  {
    LOG_ERROR("{}获取电流失败", lidar_index);
  }
  LOG_INFO("{}电流为{}", lidar_index, curr);
}
void AppController::slotButtonGetSyncClicked()
{
  if (!checkAndInitBox())
  {
    return;
  }

  uint32_t sync   = 0;
  int lidar_index = main_window_->ui_->combobox_box_lidar_index->currentText().toInt();

  if (!lidar_communication_box_->getSync(lidar_index, sync))
  {
    LOG_ERROR("获取sync失败");
  }
  LOG_INFO("sync为{}", sync);
}
void AppController::slotButtonGetFirmwareClicked()
{
  if (!checkAndInitBox())
  {
    return;
  }

  std::string firmware;

  if (!lidar_communication_box_->getFirmwareVersion(firmware))
  {
    LOG_ERROR("获取盒子固件版本失败");
  }
  LOG_INFO("盒子固件版本为{}", firmware);
}
void AppController::slotCheckboxSwitchPowerStateChanged()
{
  if (!checkAndInitBox())
  {
    return;
  }
  int lidar_index = main_window_->ui_->combobox_box_lidar_index->currentText().toInt();
  if (!lidar_communication_box_->switchLidarPower(lidar_index, main_window_->ui_->checkbox_switch_power->isChecked()))
  {
    LOG_ERROR("{}雷达切换电源失败", lidar_index);
  }
  LOG_INFO("{}雷达切换电源{}成功", lidar_index,
           main_window_->ui_->checkbox_switch_power->isChecked() ? "上电" : "下电");
}

void AppController::slotButtonTestClicked()
{
  auto lidar_sn = work_model_map_.begin()->second->getLidarManager()->getLidarInfo()->getLidarSN();
}

}  // namespace lidar
}  // namespace robosense