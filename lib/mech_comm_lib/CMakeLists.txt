﻿cmake_minimum_required(VERSION 3.16)

project(
  mech_comm_lib
  VERSION 1.0.0
  LANGUAGES CXX)

# 添加子目录
add_subdirectory(rsfsc_utils EXCLUDE_FROM_ALL)
add_subdirectory(rsfsc_fsm EXCLUDE_FROM_ALL)
add_subdirectory(rsfsc_comm_client EXCLUDE_FROM_ALL)
add_subdirectory(mech_third_party EXCLUDE_FROM_ALL)
add_subdirectory(mech_comm_func EXCLUDE_FROM_ALL)

# 支持find
# include(CMakePackageConfigHelpers)

# # 导出目标到build tree
# export(TARGETS rsfsc_utils rsfsc_fsm rsfsc_comm_client mech_comm_third_party
#   rsfsc_tcp_client rsfsc_udp_client rsfsc_serial_client
#   FILE ${CMAKE_CURRENT_BINARY_DIR}/mech_comm_libTargets.cmake
#   NAMESPACE rsfsc::
#   )

# # 根据cmake.in文件生成配置文件到build tree
# configure_package_config_file(
#   ${CMAKE_CURRENT_LIST_DIR}/cmake/MechCommConfig.cmake.in
#   ${CMAKE_CURRENT_BINARY_DIR}/mech_comm_libConfig.cmake
#   INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/mech_comm_lib
#   )

# # 安装配置文件
# write_basic_package_version_file(
#   ${CMAKE_CURRENT_BINARY_DIR}/mech_comm_libConfig-version.cmake
#   VERSION ${PROJECT_VERSION}
#   COMPATIBILITY AnyNewerVersion
#   )
