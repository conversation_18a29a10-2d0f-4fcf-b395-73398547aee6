﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mech_communication/protocol/codec/airy_lite_codec.h"
#include "mech_communication/protocol/data_struct/airy_lite.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <boost/endian/conversion.hpp>

using boost::endian::big_to_native;

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

using CheckSumType = airy::lite::CheckSumType;
using FrameHead    = airy::lite::FrameHead;
using Frame        = airy::lite::Frame;

namespace server
{
using FrameHead = airy::lite::server::FrameHead;
using Frame     = airy::lite::server::Frame;
}  // namespace server

bool airy::lite::Frame::isValid()
{
  if (head.flag == big_to_native(airy::lite::FRAME_FLAG))
  {
    swapEndian();
    return true;
  }
  if (head.flag == airy::lite::FRAME_FLAG)
  {
    return true;
  }
  return false;
}
bool airy::lite::server::Frame::isValid()
{
  if (head.flag == big_to_native(airy::lite::FRAME_FLAG))
  {
    swapEndian();  // Call swapEndian() after updating length
    return true;
  }
  if (head.flag == airy::lite::FRAME_FLAG)
  {
    return true;
  }
  return false;
}
void airy::lite::server::FrameHead::swapEndian() { flag = big_to_native(flag); }

void airy::lite::server::Frame::swapEndian()
{
  head.swapEndian();
  length = big_to_native(length);
}
void airy::lite::FrameHead::swapEndian() { flag = big_to_native(flag); }
void airy::lite::Frame::swapEndian()
{
  head.swapEndian();
  length = big_to_native(length);
}
std::optional<std::vector<uint8_t>> airy::lite::Frame::isValid(std::vector<uint8_t>& _data)
{
  if (_data.size() < sizeof(Frame))
  {
    LOG_ERROR("payload的data size: {}小于Frame的size: {}", _data.size(), sizeof(Frame));
    return {};
  }
  // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
  auto* frame   = reinterpret_cast<Frame*>(_data.data());
  bool reverse  = false;
  bool is_valid = false;

  if (frame->head.flag == big_to_native(airy::lite::FRAME_FLAG))
  {
    reverse  = true;
    is_valid = true;
  }
  if (frame->head.flag == airy::lite::FRAME_FLAG)
  {
    reverse  = false;
    is_valid = true;
  }
  if (!is_valid)
  {
    LOG_ERROR("解析出错，当前头与期望的头不符合:{}, 期望头: {}", frame->head.flag, Frame::frameFlag());
    return {};
  }
  if (reverse)
  {
    frame->length = big_to_native(frame->length);
  }
  size_t expected_data_size = frame->length + Frame::wrapSize();
  if (_data.size() < expected_data_size)
  {
    LOG_ERROR("解析出错，当前数据长度与期望的数据长度不符合:{}, 期望数据长度: {}", _data.size(), expected_data_size);
    return {};
  }
  // #ifndef DISABLE_CHECKSUM
  // auto check_sum_data_size = expected_data_size - sizeof(CheckSumType) - 1;
  // CheckSumType cal_check_sum =
  //   AiryLiteParser::staticCheckSum(&_data.at(sizeof(mech::FRAME_FLAG)), static_cast<uint16_t>(check_sum_data_size));

  // CheckSumType check_sum = 0;
  // std::memcpy(&check_sum, &_data.at(expected_data_size - sizeof(CheckSumType) - sizeof(FRAME_FLAG)),
  //             sizeof(CheckSumType));

  // check_sum = reverse ? boost::endian::big_to_native(check_sum) : check_sum;
  // std::memcpy(&_data.at(expected_data_size - sizeof(CheckSumType) - sizeof(FRAME_FLAG)), &check_sum,
  //             sizeof(CheckSumType));
  // if (cal_check_sum != check_sum)
  // {
  //   LOG_ERROR("解析出错，当前校验和与期望的校验和不符合:{}, 期望校验和: {}", cal_check_sum, check_sum);
  //   return {};
  // }
  // #endif  // DISABLE_CHECKSUM

  // std::vector<uint8_t> payload
  return {};
}
uint16_t AiryLiteCodec::staticCheckSum(const uint8_t* _data, uint16_t _length)
{
  uint32_t check_sum = 0;
  while (_length != 0)
  {
    check_sum += _data[--_length];  // NOLINT
  }

  check_sum = check_sum & 0xffffU;

  return static_cast<uint16_t>(check_sum);
}
std::string AiryLiteCodec::getCurrentProtocolType() { return "AiryLite"; }
std::vector<uint8_t> AiryLiteCodec::frameTailPack(std::vector<uint8_t>& _frame_array)
{  // notice that it is necessary to include the tail byte and frame type of
  // the data frame for calculating checksum
  // _frame_array.emplace_back(airy::lite::FRAME_TAIL);

  // // cppcoreguidelines-pro-bounds-pointer-arithmetic
  // const uint16_t CHECK_SUM = checkSum(&_frame_array.at(sizeof(airy::lite::FRAME_FLAG)),
  //                                     static_cast<uint16_t>(_frame_array.size() - sizeof(airy::lite::FRAME_FLAG)));

  // _frame_array.emplace_back((CHECK_SUM >> 8U) & 0xFFU);  // NOLINT
  // _frame_array.emplace_back(CHECK_SUM & 0xFFU);
  return _frame_array;
}

AiryLiteCodec::AiryLiteCodec() = default;
bool AiryLiteCodec::parseClientPacket(std::vector<uint8_t>& _packet_buffer)
{
  while (!_packet_buffer.empty())
  {
    if (_packet_buffer.size() < sizeof(Frame))
    {
      error_str = "payload size error, receive payload size:" + std::to_string(_packet_buffer.size());
      _packet_buffer.clear();
      return false;
    }
    // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
    auto* frame_head  = reinterpret_cast<Frame*>(_packet_buffer.data());
    auto payload_size = boost::endian::big_to_native(frame_head->length);

    size_t expected_data_size = payload_size + Frame::wrapSize();
    if (_packet_buffer.size() < expected_data_size)
    {
      error_str = "data length error, expect not less than: " + std::to_string(expected_data_size) +
                  ", actual: " + std::to_string(_packet_buffer.size());
      return false;
    }

    auto check_sum_data_size = expected_data_size - sizeof(CheckSumType) - sizeof(Frame::frameFlag());
    const CheckSumType CAL_CHECK_SUM =
      checkSum(&_packet_buffer.at(sizeof(mech::FRAME_FLAG)), static_cast<uint16_t>(check_sum_data_size));

    CheckSumType check_sum = 0;
    std::memcpy(&check_sum, &_packet_buffer.at(expected_data_size - sizeof(CheckSumType) - sizeof(Frame::frameFlag())),
                sizeof(CheckSumType));
    check_sum = boost::endian::big_to_native(check_sum);

    if (CAL_CHECK_SUM != check_sum)
    {
      error_str = fmt::format("crc32 error，expect: {:#x}, actual: {:#x}", CAL_CHECK_SUM, check_sum);
      _packet_buffer.clear();
      return false;
    }

    // auto packet_end_iter =
    //   _packet_buffer.begin() + expected_data_size - sizeof(CheckSumType) - sizeof(Frame::frameTail());
    auto packet_end_iter = _packet_buffer.begin() + expected_data_size;
    auto packet          = std::vector<uint8_t>(_packet_buffer.begin() + sizeof(Frame), packet_end_iter);
    _packet_buffer.erase(_packet_buffer.begin(), packet_end_iter);

    if (!extractData(packet))
    {
      return false;
    }
  }
  return true;
}
bool AiryLiteCodec::parseServerPacket(std::vector<uint8_t>& _packet_buffer)
{
  while (true)
  {
    if (_packet_buffer.size() < sizeof(server::Frame))
    {
      error_str = "packet too small to contain full frame, size: " + std::to_string(_packet_buffer.size());
      return false;
    }

    // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
    auto* frame = reinterpret_cast<server::Frame*>(_packet_buffer.data());

    if (!frame->isValid())
    {
      error_str =
        "invalid frame header: 0x" + hex(frame->head.flag) + ", expected: 0x" + hex(server::Frame::frameFlag());
      return false;
    }

    auto payload_size         = frame->length;
    size_t expected_data_size = payload_size + server::Frame::wrapSize();

    if (_packet_buffer.size() < expected_data_size)
    {
      error_str = "incomplete frame, expected size: " + std::to_string(expected_data_size) +
                  ", actual: " + std::to_string(_packet_buffer.size());
      return false;
    }

    // const auto check_sum_data_size = expected_data_size - sizeof(CheckSumType);
    // const CheckSumType CAL_CHECK_SUM =
    //   checkSum(&_packet_buffer.at(sizeof(server::Frame::frameFlag())), static_cast<uint16_t>(check_sum_data_size));

    // CheckSumType check_sum = 0;
    // std::memcpy(&check_sum, &_packet_buffer.at(expected_data_size - sizeof(CheckSumType)), sizeof(CheckSumType));
    // check_sum = boost::endian::big_to_native(check_sum);

    // if (CAL_CHECK_SUM != check_sum)
    // {
    //   error_str = fmt::format("checksum error，expect: {:#x}, actual: {:#x}", CAL_CHECK_SUM, check_sum);
    //   _packet_buffer.clear();
    //   return false;
    // }

    // 提取载荷
    // const std::vector<uint8_t> PAYLOAD(
    //   _packet_buffer.begin() + sizeof(server::FrameHead),
    //   _packet_buffer.begin() + expected_data_size - sizeof(server::Frame::frameTail()) - sizeof(CheckSumType));
    const std::vector<uint8_t> PAYLOAD(_packet_buffer.begin() + sizeof(server::FrameHead),
                                       _packet_buffer.begin() + expected_data_size);

    {
      std::lock_guard<std::mutex> lock(queue_mutex);
      payload_queue.emplace(static_cast<uint32_t>(frame->head.data_type), PAYLOAD);
    }

    _packet_buffer.erase(_packet_buffer.begin(), _packet_buffer.begin() + expected_data_size);

    if (_packet_buffer.size() < sizeof(server::Frame))
    {
      break;
    }
  }

  return true;
}

bool AiryLiteCodec::extractData(const std::vector<uint8_t>& _packet)
{
  auto service_id = static_cast<mech::ServiceID>(_packet.front());
  switch (service_id)
  {
  case mech::ServiceID::READ_DID:
  case mech::ServiceID::WRITE_DID:
  {
    return extractDID(_packet);
  }
  case mech::ServiceID::RID_REQ:
  {
    return extractRID(_packet);
  }
  default:
  {
    LOG_INDEX_ERROR("未知协议包: {}", service_id);
    return false;
  }
  }
  return false;
}

bool AiryLiteCodec::extractDID(const std::vector<uint8_t>& _packet)
{
  if (_packet.size() < sizeof(mech::DIDFrame))
  {
    LOG_INDEX_ERROR("提取did返回数据失败，err: 长度不足，当前包长度{}", _packet.size());
    return false;
  }
  // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
  const auto* did_frame = reinterpret_cast<const mech::DIDFrame*>(_packet.data());
  switch (did_frame->cmd)
  {
  case mech::DIDCmd::PS_VER:
  {
    if (did_frame->id == mech::ServiceID::WRITE_DID)
    {
      return true;
    }
    if (did_frame->id == mech::ServiceID::READ_DID)
    {
      if (_packet.size() < sizeof(mech::DIDFrame) + sizeof(uint32_t))
      {
        LOG_INDEX_ERROR("提取did返回数据失败，err: 长度不足，当前包长度{}", _packet.size());
        return false;
      }
      uint32_t ps_ver = 0;
      size_t index    = sizeof(mech::DIDFrame);
      MechBaseCodec::copyReverseFromPayload(_packet, ps_ver, index);
      register_value_queue.emplace(ps_ver);
      return true;
    }
    break;
  }
  default:
  {
  }
  }
  LOG_INDEX_ERROR("提取did返回数据失败，err: 未知服务id: {}", did_frame->id);
  return false;
}
bool AiryLiteCodec::extractRID(const std::vector<uint8_t>& _packet)
{
  if (_packet.size() < sizeof(mech::RIDFrame))
  {
    LOG_INDEX_ERROR("提取rid返回数据失败，err: 长度不足，当前包长度{}", _packet.size());
    return false;
  }
  // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
  const auto* rid_frame = reinterpret_cast<const mech::RIDFrame*>(_packet.data());
  if (rid_frame->id != mech::ServiceID::RID_RESP)
  {
    LOG_INDEX_ERROR("提取rid返回数据失败，err: 未知服务id: {}", rid_frame->id);
    return false;
  }
  switch (rid_frame->cmd)
  {
  case mech::RIDCmd::READ_REG:
  case mech::RIDCmd::WRITE_REG:
  {
    if (_packet.size() < (sizeof(mech::RIDFrame) + (2 * sizeof(uint32_t))))
    {
      LOG_INDEX_ERROR("提取rid返回数据大小不正确，packet size: {}, expected more than: {}", _packet.size(),
                      (sizeof(mech::RIDFrame) + (2 * sizeof(uint32_t))));
      return false;
    }
    if (((_packet.size() - sizeof(mech::RIDFrame)) % 4) != 0)
    {
      LOG_INDEX_ERROR("提取的寄存器数据不能被4整除");
      return false;
    }
    size_t index = sizeof(mech::RIDFrame);
    while (index < _packet.size())
    {
      uint32_t reg_addr = 0;
      MechBaseCodec::copyReverseFromPayload(_packet, reg_addr, index);
      register_addr_queue.emplace(reg_addr);

      uint32_t reg_value = 0;
      MechBaseCodec::copyReverseFromPayload(_packet, reg_value, index);
      register_value_queue.emplace(reg_value);
    }
    return true;
  }
  default:
  {
    LOG_INDEX_ERROR("提取rid返回数据失败，err: 未知命令: {:#x}", static_cast<uint16_t>(rid_frame->cmd));
    return false;
  }
  }
  return false;
}
}  // namespace lidar
}  // namespace robosense
