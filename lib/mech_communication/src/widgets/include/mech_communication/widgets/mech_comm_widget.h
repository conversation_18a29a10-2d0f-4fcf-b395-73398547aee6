﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef MECH_COMM_WIDGET_H
#define MECH_COMM_WIDGET_H

#include "mech_communication/mech_communication.h"
#include <QWidget>
#include <memory>

QT_BEGIN_NAMESPACE
class QComboBox;
class QLineEdit;
class QSpinBox;
class QPushButton;
class QPlainTextEdit;
class QVBoxLayout;
class QHBoxLayout;
class QScrollArea;
QT_END_NAMESPACE

namespace robosense
{
namespace lidar
{

class MechCommWidget : public QWidget
{
  Q_OBJECT

public:
  explicit MechCommWidget(QWidget* _parent = nullptr);
  MechCommWidget(const MechCommWidget&) = delete;
  MechCommWidget(MechCommWidget&&)      = delete;
  MechCommWidget& operator=(const MechCommWidget&) = delete;
  MechCommWidget& operator=(MechCommWidget&&) = delete;
  ~MechCommWidget() override                  = default;

  std::unique_ptr<MechCommunication>& getMechComm() { return ptr_mech_comm_; }

private Q_SLOTS:
  void onLidarTypeChanged(int _index);
  void onConnectButtonClicked();
  void onPingButtonClicked();
  void onSetEyeSafeButtonClicked();
  void onSingleRegReadClicked();
  void onSingleRegWriteClicked();
  void onMultiRegReadClicked();
  void onMultiRegWriteClicked();

  void slotConnected();
  void slotDisconnected();

private:
  void setupUi();
  void createConnections();
  void updateRegisterInputs(int _count);
  QWidget* createRegisterWidget(int _count);

  // 新增UI初始化相关的私有函数
  void setupLidarTypeSection(QVBoxLayout* _main_layout);
  void setupConnectionSection(QVBoxLayout* _main_layout);
  void setupOperationSection(QVBoxLayout* _main_layout);
  void setupRegisterControls(QVBoxLayout* _operation_layout);
  void setupRegisterList(QVBoxLayout* _operation_layout);

private:
  // UI组件
  QComboBox* combo_box_lidar_type_ = nullptr;
  QComboBox* combo_project_code_   = nullptr;
  QLineEdit* line_edit_ip_         = nullptr;
  QSpinBox* spin_box_msop_port_    = nullptr;
  QLineEdit* line_edit_port_name_  = nullptr;
  QSpinBox* spin_box_baudrate_     = nullptr;
  QPushButton* button_connect_     = nullptr;
  QPushButton* button_ping_        = nullptr;
  QPushButton* button_eye_safe_    = nullptr;

  QWidget* widget_operation_ = nullptr;

  // 多寄存器操作相关
  QSpinBox* spin_box_reg_count_    = nullptr;
  QWidget* widget_reg_values_      = nullptr;
  QVBoxLayout* reg_values_layout_  = nullptr;
  QPushButton* button_multi_read_  = nullptr;
  QPushButton* button_multi_write_ = nullptr;
  std::vector<QLineEdit*> reg_addr_inputs_;
  std::vector<QLineEdit*> reg_value_inputs_;

  std::unique_ptr<MechCommunication> ptr_mech_comm_;

  struct RegRowWidgets
  {
    QLineEdit* addr_edit      = nullptr;  // 地址输入框
    QLineEdit* write_value    = nullptr;  // 写入值输入框
    QLineEdit* read_value     = nullptr;  // 读取值显示框
    QPushButton* read_button  = nullptr;  // 单个读取按钮
    QPushButton* write_button = nullptr;  // 单个写入按钮
  };

  std::vector<RegRowWidgets> reg_rows_;  // 存储每行的控件

  QScrollArea* scroll_area_ = nullptr;  // 添加这个成员变量
};

}  // namespace lidar
}  // namespace robosense

#endif  // MECH_COMM_WIDGET_H