// cSpell Settings
{
  // Version of the setting file.  Always 0.2
  "version": "0.2",
  // language - current active spelling language
  "language": "en",
  // words - list of words to be always considered correct
  "words": [
    "<PERSON><PERSON>",
    "AUTOSAR",
    "AVCC",
    "BPEARL",
    "BRAM",
    "bugprone",
    "CA<PERSON>IB",
    "cbus",
    "centralwidget",
    "chardet",
    "CHNL",
    "CICD",
    "combobox",
    "Daqin",
    "DIFOP",
    "dspinbox",
    "dtags",
    "Eigen",
    "ENCOD",
    "fusa",
    "gbit",
    "gedit",
    "gitsubmodule",
    "gmock",
    "googletest",
    "GPRMC",
    "GPTP",
    "gtest",
    "hhmmss",
    "hicpp",
    "intrin",
    "Liang",
    "librsfsc",
    "lineedit",
    "loguru",
    "LPTOP",
    "mainwindow",
    "MECHTCP",
    "MECHUDP",
    "MEMS",
    "MEMSTCP",
    "MEMSUDP",
    "<PERSON><PERSON>",
    "munubar",
    "NOLINT",
    "NOLINTNEXTLINE",
    "opencv",
    "OPENMP",
    "ossd",
    "pcap",
    "Pixmap",
    "psddr",
    "psintfp",
    "psintlp",
    "QMESSAGE",
    "qobject",
    "qsetting",
    "qsettings",
    "REGRST",
    "robosense",
    "rsdata",
    "rsfsc",
    "RSFSCLIB",
    "RSFSCLOG",
    "RSFSCQSettings",
    "rsfsg's",
    "rslidar",
    "SATU",
    "SECL",
    "Shen",
    "SHIYAN",
    "spdlog",
    "suteng",
    "tablewidget",
    "tabwidget",
    "THRE",
    "TMPC",
    "TMPCOE",
    "TMPDIF",
    "tparam",
    "txchn",
    "utest",
    "vbus",
    "VCCAUX",
    "VCCBRAM",
    "VCCINT",
    "vcco",
    "VCCODDR",
    "VCCPAUX",
    "VCCPINT",
    "wdata",
    "widgetaction",
    "WORKCOE",
    "YAMLCPP"
  ],
  // flagWords - list of words to be always considered incorrect
  // This is useful for offensive words and common spelling errors.
  // For example "hte" should be "the"
  "flagWords": [],
  "dictionaries": [
    "cpp",
    "python",
    "bash",
    "en_us",
    "latex",
    "public-licenses",
    "softwareTerms"
  ]
}
