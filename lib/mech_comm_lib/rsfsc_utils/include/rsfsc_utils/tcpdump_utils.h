﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef TCPDUMP_UTILS_H
#define TCPDUMP_UTILS_H

#include <QProcess>
#include <memory>
#include <optional>
#include <string>
#include <vector>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

class TcpdumpUtils
{
public:
  TcpdumpUtils() = default;
  ~TcpdumpUtils();

  // 根据 Rule of Five 添加特殊成员函数
  TcpdumpUtils(const TcpdumpUtils&) = delete;
  TcpdumpUtils& operator=(const TcpdumpUtils&) = delete;
  TcpdumpUtils(TcpdumpUtils&&)                 = delete;
  TcpdumpUtils& operator=(TcpdumpUtils&&) = delete;

  struct NetInfo
  {
    std::string ip;
    uint16_t msop_port;
    uint16_t difop_port;
    std::string mac_addr;
  };
  struct Filter
  {
    std::string ip_mask;  // 限制dump的IP掩码
    std::string network_interface;
    std::string src_ip;
    std::string dst_ip;
    std::string host_ip;
    std::string protocol;
    std::string file_size;                          // 限制dump的文件大小，单位MB, 会循环生成2个文件
    int timeout_s {};                               // 限制dump的超时时间，仅在captureOneNetInfo中使用
    std::string custom_filter;                      // 限制dump的过滤，添加自定义规则
    std::vector<std::string> exclude_mac_addr_vec;  // 排除的mac地址
    Filter() : ip_mask("***********/16"), network_interface("any"), timeout_s(20) {}
  };

public:
  std::optional<NetInfo> captureOneNetInfo(const Filter& _filter = Filter());
  std::optional<NetInfo> captureOneAiryNetInfo(Filter _filter = Filter());
  void abort();

  // 添加一个方法来显式释放 process_ 资源
  void resetProcess();

  bool startTcpdump(const std::string& _dump_file_path, const Filter& _filter = Filter());
  void stopTcpdump();

  /**
   * @brief 根据IP获取网络接口名
   * @param _ip IP地址
   * @return 网络接口
   */
  static std::string getNetworkInterFaceByIP(const std::string& _ip);

  /**
   * @brief kill所有tcpdump进程, 软件启动时可调用，保证之前
   */
  static void killAllTcpdump();

  void setLogIndex(const int _log_index) { log_index_ = _log_index; }
  [[nodiscard]] int getLogIndex() const { return log_index_; }

private:
  std::unique_ptr<QProcess> process_;
  int log_index_ = -1;
  bool is_abort_ = false;
};

}  // namespace lidar
}  // namespace robosense

#endif