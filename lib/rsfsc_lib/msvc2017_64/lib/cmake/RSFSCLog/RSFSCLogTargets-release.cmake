#----------------------------------------------------------------
# Generated CMake target import file for configuration "Release".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "RSFSCLog::RSFSCLog" for configuration "Release"
set_property(TARGET RSFSCLog::RSFSCLog APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(RSFSCLog::RSFSCLog PROPERTIES
  IMPORTED_IMPLIB_RELEASE "${_IMPORT_PREFIX}/lib/rsfsc_log.lib"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/bin/rsfsc_log.dll"
  )

list(APPEND _cmake_import_check_targets RSFSCLog::RSFSCLog )
list(APPEND _cmake_import_check_files_for_RSFSCLog::RSFSCLog "${_IMPORT_PREFIX}/lib/rsfsc_log.lib" "${_IMPORT_PREFIX}/bin/rsfsc_log.dll" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
