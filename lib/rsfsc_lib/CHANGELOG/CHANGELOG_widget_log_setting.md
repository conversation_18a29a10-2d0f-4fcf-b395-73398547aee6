﻿### v1.12.9 - 20250427

### Added
- 增加崩溃后的日志拷贝

### Changed
- 修改`csv_parser`寄存器获取连续地址的数量的逻辑,不改变`name的文件名称`

### v1.12.8 - 20250417

### Fixed
- 修正`check all state`时候修改日志路径的顺序，避免出现不可预测的风险
- 修正增加`mes`的`SubType`可配置

### v1.12.7 - 20250416

### Added
- 增加`MES`获取数据功能，通过整机`SN`查询指定子件的指定测试信息​

### v1.12.6 - 20250411

### Changed
- 修改`rsfsc_log`在`CheckAllstate`之后日志进行分离，让日志显示更清晰

### v1.12.5 - 20250325

### Added
- 增加支持修改`program unique code`的功能，解决同一套软件，无法动态根据工位修改`unique code`的问题
- 增加`保存参数`按钮，解决因闪退引起的参数未及时保存的问题
- 细化MES检查参数为空时候的日志提示，可具体到哪一项为空

### Changed
- 修改开放`lidar info`的设置`IP, MSOP, DIFOP`的功能
- 增加项目编号`0352`,`0360`的支持

### Fixed
- 修复设置日志等级与实际不一致的问题

### v1.12.4 - 20250319

### Fixed
- 修改在`本地保存的result.csv文件`，按照`UTF-8 with BOM`编码保存，解决跨平台乱码问题

### v1.12.3 - 20250314

### Changed
- 修改在`本地保存的result.csv文件`，按照`GBK`编码保存，方便统计

### Changed
- 增加项目编号`1010`的支持
- 更新`rsfsc_log`至版本`v2.2.3`

### Fixed
- 修正IP池确认绑定失败，却返回true的问题

### v1.12.2 - 20250221

### Added
- 增加`limit`字符串类型允许上下限比较

### Changed
- 修改请求数据支持获取`回复的不为空的数据`
- 更新`rsfsc_log`至版本`v2.2.2`

### Fixed
- 修正多项处理支持`log` `index`
- 修正在`finishProcess`时候`resetFileSavePath`

### v1.12.1 - 20250113
### Added
- 适配最新的`RSFSCLog`对`index`的支持
- 自动机台协议适配`v1.0.2`,增加心跳包
  
### Changed
- `MES`日志增加对`index`的打印
- 修改`WidgetResultTable`结果控件列可调节宽度
- IP池设置增加对域名解析连接
- 由于目前未打通mes获取量产数据，为了避免误导，暂时屏蔽量产数据显示

### Fixed
- 修复对`LimitInfo的extra_str_info`进行有效性检测，避免导致使用崩溃
- 修复`loadRegisterCsvInfo`在`windows`下，大于`0x80000000`的值无法失败的情况

### v1.11.15 - 20250110
### Added
- 增加`mes的秘钥`相关查询及上传函数相关功能
  - 查询类型`ASK_TYPE_KMS_AES_EFUSE_KEY/ASK_TYPE_KMS_ENABLE_SECURE_TAG/ASK_TYPE_KMS_SIGN_CERT`
  - 上传函数`uploadKmsEnableResult`

### v1.11.14 - 20241226

### Changed
- 增加项目编号`0621`的支持

### v1.11.13 - 20241225

### Changed
- 暂时修改`开放LimitInfo的构造private改public`
- 增加项目编号`0270_na`的支持
  
### Fixed
- 修正挂载失败不提示的问题
- 修正`addMeasureMessage`中`text`类型失败情况返回`true`的问题

### v1.11.12 - 20241122

### Changed
- 合并`v1.10.6`的修改

### Fixed
- 修正`WidgetResultTable`控件显示结果与`addMeasureMessage`不一致的问题
- 修正`Fixed Install Pos`与`Fixed Project Code`回车
- 增加`rsfsc_log`在`focal`路径下的软链接

### v1.11.11 - 20241104
### Added
- `WidgetLidarInfo`控件更新至`v1.1.9`,输入雷达SN完成后先将Shift+数字键对应的符号替换为数字，再触发输入完成信号

### v1.11.10 - 20241104
### Added
+ 更新MES通讯协议，数据查询接口`bool WidgetLogSetting::requireData`新增通过子件SN查询指定测试信息(未装配的子件SN)功能，使用该功能时需在构造`AskData`时传入子件SN
```
  AskData(const std::string& _station,
                   std::initializer_list<std::string> _items_name,
                   const std::string& _unassembled_sub_item_sn = "")


    /**
   * @brief    `ASK_TYPE_CALIB_ASK_CALIB_DATA`：根据SN查询对应工序过站信息(包含整机段、振镜段、模具段)
               `ASK_TYPE_CALIB_ASK_MODULE_DATA`：整机段查模组线工站的数据
               `ASK_TYPE_CALIB_ASK_MIRROR_DATA`：整机段查振镜工站的数据
               `ASK_TYPE_CALIB_ASK_RECEIVE_MODULE_DATA`：整机段查接收模组段数据
               `ASK_TYPE_CALIB_ASK_EMITTING_MODULE_DATA`：整机查发射模组段数据
               `ASK_TYPE_CALIB_ASK_MATERIAL_DATA`：根据SN查询料号信息
               `ASK_TYPE_CALIB_ASK_CUSTOMER_SN`：整机段查客户SN信息
               `ASK_TYPE_CALIB_ASK_SUB_SN_TO_DATA`: 通过子件SN查询指定测试信息(未装配的子件SN)
               `ASK_TYPE_CALIB_ASK_SN_TO_SUB_SN_DATA`: 通过整机SN查询指定子件的指定测试信息​
   * 
   * @param _lidar_index 当前lidar info控件索引,索引从1开始
   * @param _ask_data 请求数据集
   * @return true 
   * @return false 
   */
  bool WidgetLogSetting::requireData(const quint32 _lidar_index,
                   std::map<AskData::AskType, std::vector<AskData>>& _ask_data = g_ask_data_init);
```

### v1.11.9 - 20241010

### Changed
+ 修改在`debug模式`下，不启动登录10分钟后自动退出账号机制

### v1.11.8 - 20240924

### Changed
+ 添加用户管理类型兼容支持`超级管理员`和`管理员`

### Fixed
+ 修复`LimitInfo`增加设置后缀方式，采用独立`setNameSuffix`
+ 修复操作部分UI使能导致mes相关使能异常bug

### v1.11.7 - 20240904
### Added
+ 添加适配`EDI`系统上传
+ 添加适配`SY`系统上传
+ `widget_log_setting`增加参数选择`mes`类型
+ 修正`limit`上线限为`nan`时候不做卡控
+ 修复`limit`上下限为`nan`时候的显示问题

### Fixed
+ 修复`RSFSCLog`的`windows`错误，修复崩溃日志上传错误
+ 修复`cTest`错误
+ 修复上传的`Fail Message`存在空格问题

### v1.11.6 - 20240814
### Fixed
+ 修正`limit`输出结果与显示的`limit`范围不符的问题
+ 添加`limit`输出结果的多项单元测试

### v1.11.5 - 20240812
### Added
+ 增加`RSFSCLog v2.1.0`版本
+ 增加`RSFSCLog v2.1.0`使用手册

### v1.11.4 - 202408012
### Fixed
+ 修正链接`RSFSCLog`的目标名

### v1.11.3 - 20240802
### Added
+ 增加项目编号`0350`、`0600`、`0603`、`0604`、`0605`

### v1.11.2 - 20240802
### Added
+ 增加`LidarAbnormalMonitor`类为`LimitInfo`类的友元类

### Changed
+ 更新`RSFSC_LOG`模块，支持多参数日志打印
  
### Fixed
+ 修复`libgit2.so`生成路径未与`librsfsc_lib.so`保持一致的问题

### v1.11.1 - 20240801
### Changed
+ 修改`LimitInfo`类构造函数为私有，禁止用户代码调用`LimitInfo`类构造函数，所有`LimitInfo`类实例化必须通过`CsvParser`类加载配置文件实现
+ 修改`LimitInfo`类的`name_`和`unit_`属性为私有，禁止用户代码对`LimitInfo`类的`name_`和`unit_`进行修改
+ 修改配置文件解析规则，`LimitInfo`新增文本类型支持，须将`unit`定义为`text`，且`LSL`与`USL`必须一致，否则配置文件解析失败将退出进程，文本类型阈值属性为`limit_text`
+ 修改配置文件校验规则，加载配置文件时对`LimitInfo`的`name`和`unit`属性进行非法字符校验，仅支持a-z A-Z 0-9 -#_. 等，存在非法字符将退出进程，备注信息必须不少于4个英文字符（对应中文字符则为不少于2个字符），建议在不明确备注信息时写TODO便于后期完善
+ 修改测试项添加接口，仅保留数值类和文本类`LimitInfo`添加接口
```cpp
  bool addMeasureMessage(const quint32 _lidar_index,
                         const LimitInfo& _limit_info,
                         const double _data,
                         const MeasureDataType _data_type = MEASURE_DATA_TYPE_FLOAT);

  bool addMeasureMessage(const quint32 _lidar_index, const LimitInfo& _limit_info, const std::string& _data);
```
+ 修改过站防呆逻辑，所有测试节点必须存在对应的测试项`LimitInfo`进行说明，若测试结果无任何FAIL测试项但过站结果为FAIL会通过飞书推送异常信息，并将对应的测试result文件及log复制至公共盘`\\10.10.0.52\production_software_workspace\`对应软件版本文件夹中，命名规则为`测试开始时间_SN_result.csv`和`测试开始时间_SN.log`，方便排查问题


### Added
+ `RSFSCLog`通过fmt实现多参数日志打印
+ 增加项目编号0270-0276的支持
  
### v1.11.0 - 202400801
### Added
+ 增加`GitManager`类实现git部分功能，包括clone、pull、diff等
+ 增加`WidgetGitManager`控件类配合`GitManager`类的使用

### v1.10.6 - 20241126
### Changed
+ 修改线束管理使用方式，在`WidgetLogSetting::init`函数中传入线束管理是否启用线束管理

### Fixed
+ 修复操作部分UI使能导致mes相关使能异常bug

### v1.10.5 - 20241031
### Fixed
+ 增加0277 - 027F项目编号支持

### v1.10.4 - 20240826
### Fixed
+ `WidgetLidarInfo`更新至1.1.8,修复线束SN输入完成后未触发输入完成信号的BUG

### v1.10.3 - 20240826
### Added
+ `WidgetLidarInfo`更新至1.1.7， 增加`WidgetLidarInfo::setLidarSNWidgetFocus()`和`WidgetLidarInfo::setCableSNWidgetFocus()`接口用于设置光标焦点

### v1.10.2 - 20240819
### Added
+ 增加0600、0604、0605项目编号支持

### v1.10.1 - 20240805
### Added
+ 增加0270 - 0276项目编号支持

### v1.10.0 - 20240515
### Added
+ 增加`WidgetResultTable`类用于展示测试项结果
+ 适配`0217 B2`车型窗口片折射因子

### v1.9.4 - 20240428
### Fixed
+ 修复`安灯设置`下的`继电器端口名`获取为空的问题,及`安灯设置`只能在登录`技术员`以上才能修改

### v1.9.3 - 20240424
### Fixed
+ 修复`安灯设置`下的`继电器蜂鸣器Pin`无法保存本地配置问题

### v1.9.2 - 20240415
### Changed
+ 修改`equipment_url`默认值端口由`17001`修改为`9999`

### v1.9.1 - 20240411
### Added
+ 新增加安灯继电器端口名参数

### v1.9.0 - 20240326
### Changed
+ 新增加设备状态报告功能

### v1.8.3 - 20240314
### Changed
+ 窗口片计算函数增加`0217 VA`车型适配

### v1.8.2 - 20240308
### Changed
+ `WidgetLogSetting::init`函数新增加`_is_debug_mode`, 用于设置开发者mes权限及调试版本窗体颜色警告提示

### v1.8.1 - 20240204
### Changed
+ 在`finishProcess`调用后清空`线束SN`
+ 为了确保上传mes的串行操作，在`checkAllState`,`requireData`,`finishProcess`增加串行操作锁

### v1.8.0 - 20240112
### Added
+ 过在检测增加对多个SN唯一性检测，检测到非唯一会报`CHECK_STATE_SN_IS_NOT_UNIQUE_ERROR`
+ 解决mes上传第一次测试`fail`, 但是未设置`fail message`，导致第二次`pass`误报'未设置项目: `Fail Message`
+ 将`mes`过站和数据请求权限提升至开发者

### Changed
+ 将`SN的字符`限制由原来的`10`修改为`6`,兼容机械式雷达SN

### v1.7.1 - 20231117
### Fixed
+ 修复MessageBrowser多线程快速打印导致的卡死问题, 更新rsfsc_log至v1.2.3

### v1.7.0 - 202310205
### Added
+ 增加`windows系统的dmp`文件生成
+ 在测试结果增加`项目平台(Platform)`且上传mes

### Fixed
+ 修复内存泄漏
+ 修复在子线程使用过站时, 当警告磁盘不足弹出会崩溃问题
+ 修改在`checkAllState`和`finishProcess`记录产量支持多线程
+ 修改自动通信使用`Abort`命令报错问题

### v1.6.5 - 20230914
### Added
+ 增加dmp文件自动上传至网盘功能，网盘地址：`\\10.10.0.52\production_software_workspace`

### Changed
- 适配MES线束SN规则

### v1.6.4 - 20230914
### Fixed
+ 修复v1.6.3线束SN检查规则BUG

### v1.6.3 - 20230913
### Changed
+ 由于线束编码规则中包含`#`,控件`LineEditLimitKeyboard`增加`#`支持
  
### v1.6.2 - 20230905
### Added
+ 增加`WidgetLidarInfo::setLidarSN()`接口用于外部扫码器进行雷达SN传递
+ 增加寄存器备份功能:用户可调用`backUpOperateRegInfo`进行寄存器备份，该函数会自动在用户指定路径生成备份文件。需要恢复时调用`getBackUpOperateRegInfo`来获取之前的备份内容。

### Changed
+ 修改`WidgetLidarInfo`控件界面修改方式，支持多线程

### v1.6.1 - 20230823
### Fixed
+ 修复`高级设置`残留原本地线束寿命管理控件问题

### v1.6.0 - 20230822
### Added
+ 使用MES进行线束管理校验，取代原本地记录线束寿命方式
+ 过站时上传软件版本信息至MES进行校验，版本信息格式为"vX.Y.Z"
  
### v1.5.5 - 20230825
### Added
+ 增加寄存器备份功能:用户可调用`backUpOperateRegInfo`进行寄存器备份，该函数会自动在用户指定路径生成备份文件。需要恢复时调用`getBackUpOperateRegInfo`来获取之前的备份内容。
  
## v1.5.4 - 20230810
### Changed
+ 修改数据请求区分不同工厂, LT工厂支持`技术员`权限修改`数据请求选项`, `HHL工厂`不支持, 默认是`HHL工厂`

## v1.5.3 - 20230731
### Fixed
+ 修复上传MES系统时字符串类型测试值结果未拼接limit内容问题

## v1.5.2 - 20230717
### Added
+ 窗口片计算函数增加0226项目支持，DX车型窗口片与0218一致(即默认)，E1车型与0219一致

### Changed
+ Robosense MES系统上传测试值结果字符串无论是否合格均拼接上下限及结果内容

## v1.5.1 - 20230710
### Changed
+ 修改数据请求函数`requireData`, 支持整机段查询振镜数据
+ 过站时生成文件夹新增temp文件用于标定测试过程存放过渡中转数据
+ 增加机械式相关项目编号

## v1.5.0 - 20230420
### Added
+ 增加`Breakpad`捕捉异常机制，原捕捉机制存在冲突故弃用(log中不会再进行crash打印)

### Fixed
+ 增加打印`addMeasureMessage`字符异常的log信息


## v1.4.1 - 20230419
### Added
+ 增加0228窗口片折射因子计算

## v1.4.0 - 20230328
### Added
+ 增加检查配置文件MD5值功能，过站前需通过```WidgetLogSetting::addCheckSumDir(const std::string& _dir)```设置文件夹路径，如果出现变更需使用```菜单栏->高级设置->重置配置文件校验值```进行重置，否则会过站会返回错误```CheckState::CHECK_STATE_CHECK_CONFIG_SUM_ERROR```
+ 增加寄存器表文件及阈值文件格式校验，需要确保CSV文件行尾序列为CRLF
+ `addMeasureMessage`增加非法字符校验，仅支持a-z A-Z 0-9 -#_. 等
+ 过站检查时进行项目编号及安装位置防呆，若为NOT_FOUND将无法过站，注意在`WidgetLidarInfo`进行项目编号及安装位置的显示
+ 添加EOS、0800相关项目编号
+ 增加022A窗口片折射因子计算
  
### Changed
+ rs_user_manager模块变更为v1.1.0版本，管理员用户模式变更为超级管理员单一账户模式,增加非开发者/操作员账号10分钟退出登录机制
+ 分离过站接口和数据获取接口
+ IP池绑定与解绑分离独立接口
+ 雷达最大支持数量从55增加至200
+ 取消管理员以下用户的MES开关权限
+ `WidgetLogSetting::setSoftwareVersion`增加`tweak`参数

### Fixed
+ 修正`setContactEmail`函数拼写
+ 修复线程池通讯中断问题
+ 修复lineedit_limit_keyboard输入%^&*等符号的问题
+ 修复寄存器表读取模块的```std::stoi```问题
+ 优化`自动设备通讯`，处理由于延迟导致的粘包问题，以及可跨线程调用通讯命令
+ 优化适配windows平台

## v1.3.6 - 20221212
### Added
+ 在界面中增加操作文档菜单栏，点击后直接弹出系统中的文件夹，方便大家找到帮助文档

### Changed
+ 更新rsfsc_log至v1.2.0，修复代码拼写检查和clang-tidy的一些报错

### Fixed
+ 修复代码中的拼写检查错误
+ 修复0255拼写错误，所有用到0255这个项目号的软件都需要更新，否则无法识别0255项目编号

## v1.3.5 - 20220927
### Added
+ WidgetLidarInfo从v1.1.2升级到v1.1.4，具体变更参考WidgetLidarInfo的CHANGELOG
+ `菜单栏->高级设置`中启用线束寿命管理后，超过使用次数将返回LOG_CHECK_STATE_CABLE_MANAGER_ERROR
+ 增加串口绑定功能
+ 增加操作员认证功能，勾选后操作员也需要登录才可以操作
+ 帮助菜单栏中增加操作文档弹出
+ 增加0252窗口片折射因子计算

### Changed
+ 优化登录操作，登录后无需再输入操作员ID

### Fixed
+ 修复连接项目过站时没有连接车型和安装位置的BUG
+ 修复无法通过RS MES获取MAC地址的BUG
+ RSFSCLog从v1.1.0更新到v1.1.1，changeFileSavePath时会将之前产生的log也拷贝到新的目录，修复当前过站前log无法保存到一起的BUG

## v1.3.4 - 20220916
### Added
+ RoboSense MES增加硬件版本号获取接口
+ 将USB-KEY检查功能加到init函数中，不存在则强制退出，后续程序中统一用init接口，不需再在自己的代码中写相关函数和拷贝动态库

### Fixed
+ 修复`不校验SN项目`不能保存下次，重启软件时自动加载的BUG
+ 修复自动化通信协议中，回复确认时把所有S都替换成C的BUG，修复为只替换第一个S

## v1.3.3 - 20220908
### Added
+ 增加请求数据勾选，将请求数据和过站检测分开

### Changed
+ WidgetLidarInfo升级到v1.1.2
+ 修改编译debug等级，由默认-g2修改为-g1，减少二进制库大小
+ 将021A的默认车型由AY修改为00

## v1.3.2 - 20220829
### Added
+ 为Jabil统计数据功能增加勾选框
+ 增加WidgetAbout，统一版权声明模板
+ 增加`LOG_TEST_STATUS_NOT_READY`状态，以适应类似转台连不上、导轨连不上、工装治具连不上的情况，当状态设置为LOG_TEST_STATUS_NOT_READY，调用finishProcess时，仅通知自动化设备，并清空WidgetLidarInfo，不进行MES上传

### Changed
+ WidgetLidarInfo从v1.1.0升级到v1.1.1

### Fixed
+ 过站不过时，没通知自动化设备的BUG
+ 未勾选自动化设备时，还进行通讯的BUG
+ 修复Windows终端中文乱码问题
+ 修复v1.3.1中引入的浮点数保留后3位的问题，v1.3.1中写错了单位，导致所有浮点数测试值输出到csv表格中都增加了0.5，这个版本修复了该问题。目前采取的做法是，阈值截取后3位，测试数据四舍五入保留后3位进行判断，保存数据也做同样处理
+ RoboSense的MES如果通信不成功，会自动再进行通信，如果总共3次都不成功，才最终判断为通信失败

## v1.3.1 - 20220819
### Added
+ 增加自动化通讯协议，当自动化设备准备好后，自动触发WidgetLidarInfo；当测试完成调用finishProcess时，自动通知自动化设备
+ 增加捷普需要的测试数据统计

### Changed
+ 增加checkWithinLimit接口，同时将addMessageMessage接口的返回改为bool，统一测试数据是否满足阈值的方法
+ WidgetLidarInfo从v1.0.0升级到v1.1.0

### Fixed
+ 统一判断测试数据是否满足阈值方法后，修复之前出现的，由于浮点数写到文件只保存3位小数点，而内部判断却用所有位数，导致文件看起来pass，但判为fail的BUG
+ 如果软件从1.2.x升级到v1.3.x之后，再降级，再升级，robosense_data_url可能被修改为其他值，自动判别并修改

## v1.3.0 - 20220816
### Added
+ 在原有LineeditLimitKeyboard控件基础上扩展到WidgetLidarInfo控件，支持SN、项目编号、车型、雷达安装位置、IP、端口等显示及设置
+ 增加IP池功能，勾选后可自动更改WidgetLidarInfo中的IP及端口号
+ 增加统一SN功能，勾上后SN无需带项目编码后缀，直接通过MES获取，同时增加从MES中获取客户SN的接口
+ 增加用户权限管理系统，分四级用户管理
+ 增加MES相关URL界面设置，避免部分工程人员不懂设置
+ 增加rsfsc_qsettings，使用该类替代QSetting，配合用户管理系统，可监控谁在什么时候将哪个参数从什么修改为什么，解决质量反馈被修改无法管控问题
+ 增加Jabil数据收集路径设置，仅在Jabil MES选项下生效，用于Jabil收集测试数据

### Changed
+ 将原有widget_log_setting.h相关类及结构体变量等移到rsfsc_msg.h中，避免rsfsc_lib内部重复包含，可以适当以rsfsc_msg.h替代前者
+ 增加rsfsc_lib的namespace，以避免部分变量及函数名跟其工程冲突，需注意相关代码
+ WidgetLogSetting相关函数增加lidar_index参数，让一拖多时更方便使用
+ 增加了部分防呆，传给WidgetLogSetting的构造参数不能带log_setting等，同时约束必须先init再构造。如果原有构造参数带了log_setting，需要提醒用户更新软件后重新设置mes相关参数

### Fixed
+ 增加超时设置接口，解决Jabil和LT爆出来的偶发超时问题
+ 修复部分乱码问题

## v1.2.5 - 20220823
### Changed
+ RoboSense的MES如果通信不成功，会自动进行3次通信

## v1.2.4 - 20220715
### Changed
+ LineEditLimitKeyboard从v0.2.3升级到v0.2.4

## v1.2.3 - 20220708
### Added
+ 增加0222窗口片折射计算，与0216保持一致
+ 增加获取MES界面设置的接口

### Changed
+ 将MES界面设置由英文变更为中文

### Fixed
+ 增加校验，如果在构造前没有init则退出程序并报错
+ 增加校验，如果_program_nam中有log_setting字眼则退出程序并报错

## v1.2.2 - 20220630
### Add
+ 增加模组数据查询接口，并修改了原有查询接口函数，需要进行相应适配

## v1.2.1 - 20220615
### Added
+ 增加良率统计的状态栏
+ 增加将不良项目上传MES的功能
+ 增加不勾选过站和数据上传时界面变红的功能

### Changed
+ 变更窗口片折射因子计算的接口，增加车型、寄存器值等形参以适应0217以及将来M1P的情况

## v1.2.0 - 20220507
### Added
+ 增加基于spdlog的RSFSCLog支持
+ 增加从RoboSense MES获取BOM料号的功能
+ 增加SN连接项目编号选项，当勾上这个的时候，过站查询及数据上传都会使用SN-项目编号的完整形式，以支持产线只打印一个二维码的需求
+ 增加fixture_slot，即一拖多时雷达对应工位编号的输出到标准文件中
+ 增加窗口片折射因子计算

### Changed
+ WidgetLogSetting，接口变更，开发者请注意WidgetLogSetting、checkLogState、finishProcess、LogCheckState等参数修改，及保存路径相关修改
+ 由WidgetLogSetting统一提供数据保存路径，总保存路径下分为log、项目编号；项目编号再底下一层是mes、rsdata、summary；rsdata再底下一层是雷达数据
+ 增加SN为空、项目编号not_find时过站不过的判断
+ 原来保存数据才检测硬盘空间（包含数据保存硬盘及系统盘）大小改为过站请求时判断，内存不够将不能进行标定
+ csv_parser由`v1.0.0`升级为`v1.1.0`；lineedit_limit_keyboard由`v0.2.2`升级为`v0.2.3`，均涉及不兼容更改

### Fixed
+ 修复在英文Ubuntu下中文乱码的问题

## v1.1.0 - 20220208

### Added

+ 增加菜单栏设置，可选择是否进行SN规则检查、是否检测软件多开
+ 增加软件多开检测，检测到已经有进程在运行时，弹窗报错
+ 增加关于，显示版本号及commit号
+ 增加软件崩溃时log输出

## v1.0.2 - 20220113

### Added

+ 增加硬盘空间大小检测，目前默认为小于30G时进行警告
+ 测试fail时，将测试fail的前n项的序号发到MES的`Fail Message`中，目前默认前10项

## v1.0.1 - 20211231

### Changed

+ 将Jabil的MES由IP修改为HOST
+ 将RoboSense的默认MES默认IP修改为正式系统
+ 禁用了石岩的操作员键盘输入，所有均只支持扫码枪输入

### Fixed

+ 大于int32时固件版本输出错误的的BUG
+ 修复Windows下全局变量的BUG

## v0.2.0 - 20211010
### Added

+ 增加RoboSense自己的MES系统
+ 增加标准化的测试数据输出
+ 增加自动数据上传功能
+ 增加Lot、fixture slot、fixture等指标

### Changed

+ 重构了Jabil以及WidgetLog的部分接口
+ 输出动态库，不再提供源码
+ 删除Secondary的log保存
+ 修改了addMeasureMessage接口

## v0.1.2 - 20210422
### Fixed

+ 小键盘按enter键也可以触发完成输入了

## v0.1.1 - 20210331

### Added

+ 增加从setting中获取FTP账号密码等功能
+ 时间限制从50ms改到100ms

## v0.1.0 - 20210315

### Added

+ 扫描完成后1.5秒关闭窗口
+ 修改了某些LOG相关的enum，需要在宏前面增加`LOG_`

关于启动的时候把焦点放在Operator的问题，如果在MainWindow的构造函数里面show，会导致MainWindow在log_setting之后显示，无法满足。需要重载MainWindow的`showEvent`，在MainWindow显示后的50毫秒再进行显示：

```cpp
void MainWindow::showEvent(QShowEvent* event)
{
  QTimer::singleShot(50, this, SLOT(slotShowLogSetting()));
  QMainWindow::showEvent(event);
}
```

log_setting的默认焦点在Operator ID这里，至于SN输入框的焦点，则需要自行设置了

### Fixed

+ LineEditLimitKeyboard修复了中文输入法能输入一串字符的BUG（直接禁止了中文输入法）
+ LineEditLimitKeyboard修复了Ubuntu下会出现乱码的BUG（目前是限制了只能输入英文字符和数字）

