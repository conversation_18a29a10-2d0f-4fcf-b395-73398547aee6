#ifndef RSFSC_SERIAL_CLIENT_H
#define RSFSC_SERIAL_CLIENT_H

#include "rsfsc_comm_client/rsfsc_comm_client.h"
#include <boost/asio/serial_port.hpp>

namespace robosense
{
namespace lidar
{

class RsfscSerialClient : public RsfscCommClient
{
public:
  RsfscSerialClient();
  explicit RsfscSerialClient(const std::string& _port, uint32_t _baud_rate);
  explicit RsfscSerialClient(const SerialClientConfig& _config);
  ~RsfscSerialClient() override;

  bool connect() override;
  bool disconnect() override;
  [[nodiscard]] bool isConnected() const override;

  std::size_t write(const std::vector<uint8_t>& _data) override;
  std::size_t readSome(std::vector<uint8_t>& _data) override;
  std::vector<uint8_t> readSomeTimeout(uint32_t _timeout_ms) override;

  void asyncWrite(const std::vector<uint8_t>& _data, WriteHandler _handler) override;
  void asyncRead(std::vector<uint8_t>& _data, ReadHandler _handler) override;

  std::vector<uint8_t> request(const std::vector<uint8_t>& _request, uint32_t _timeout_ms) override;

  void abort() override;
  [[nodiscard]] bool isAborted() const override;
  void resetAbort() override;

private:
  std::string msg_header_;
  std::unique_ptr<boost::asio::serial_port> serial_port_ = nullptr;
  bool is_connected_                                     = false;
};

}  // namespace lidar
}  // namespace robosense

#endif  // RSFSC_SERIAL_CLIENT_H
