﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mech_tcp.h"
#include <cstdint>
#include <iomanip>
#include <iostream>
#include <sstream>

struct RegisterInfo
{
  std::string name;
  std::string formula;
  uint8_t low;
  uint8_t high;
};

double getBitsAsDouble(uint32_t _value, uint32_t _start_bit, uint32_t _end_bit)
{
  uint32_t mask            = ((1ULL << (_end_bit - _start_bit + 1)) - 1) << _start_bit;
  uint32_t extracted_value = (_value & mask) >> _start_bit;

  return static_cast<double>(extracted_value);
}

int main(int /*_argc*/, const char** /*_argv*/)
{
  std::unordered_map<uint32_t, RegisterInfo> register_map;

  register_map[0x83c18024] = RegisterInfo({ "apd_temp", "200*(data/4096)-50", 0, 0xf });
  register_map[0x83c18014] = RegisterInfo({ "top_under_temp", "data/256", 0, 0xf });
  register_map[0x83c18018] = RegisterInfo({ "top_above_temp", "data/256", 0x10, 0x1f });
  register_map[0x83c18028] = RegisterInfo({ "top_fpga_temp", "503.975*(data/4096)-273.15", 0x10, 0x1f });
  register_map[0x83c00110] = RegisterInfo({ "bot_fpga_temp", "503.975*(data/4096)-273.15", 0, 0x1f });
  register_map[0x83c18008] = RegisterInfo({ "top_2V5", "10*(data/4096)", 0, 0xf });
  register_map[0x83c18004] = RegisterInfo({ "top_vbus", "42*(data/4096)", 0, 0xf });
  register_map[0x83c1800c] = RegisterInfo({ "top_tx5V", "10*(data/4096)", 0, 0xf });
  register_map[0x83c1800c] = RegisterInfo({ "top_A5V", "10*(data/4096)", 0x10, 0x1f });
  register_map[0x83c18010] = RegisterInfo({ "top_HV", "(1.661-data/4096)/0.00658", 0, 0xf });
  register_map[0x83c18010] = RegisterInfo({ "top_N5V", "-(0.9087-data/4096)/0.0725", 0x10, 0x1f });
  register_map[0x83c00134] = RegisterInfo({ "machine_vbus", "data/4096/0.020833", 0, 0x1f });
  register_map[0x83c00130] = RegisterInfo({ "bot_5V", "data/4096/0.1", 0, 0x1f });
  register_map[0x83c0012C] = RegisterInfo({ "bot_28V", "data/4096/0.020833", 0, 0x1f });
  register_map[0x83c00138] = RegisterInfo({ "cbus_curr", "5.5*(data/4096)", 0, 0x1f });
  register_map[0x83c00214] = RegisterInfo({ "real_speed", "data/6", 0, 0x1f });

  robosense::lidar::MechTcp tcp_connect(robosense::lidar::ProtocolType::MECH_RUBY_OLD);

  // connect
  std::cout << "try connect " << std::endl;
  if (!tcp_connect.connect("192.168.1.200", 6699, 10000))
  {
    std::cerr << "failed to connect." << std::endl;
    return -1;
  }

  uint32_t val  = 0x01000000 + 0x1208 * 256 + 1;
  uint32_t addr = 0x83c20030;
  if (!tcp_connect.writeRegData(addr, val))
  {
    std::cerr << "failed to write." << std::endl;
    return -1;
  }

  std::vector<uint32_t> reg_addr;
  reg_addr.reserve(register_map.size());

  for (const auto& entry : register_map)
  {
    reg_addr.push_back(entry.first);
  }

  if (!tcp_connect.waitForStartupComplete(10000))
  {
    std::cerr << "failed to startup." << std::endl;
    return -1;
  }

  std::vector<uint32_t> reg_val;
  if (!tcp_connect.readMixRegData(reg_addr, reg_val))
  {
    std::cerr << "failed to read." << std::endl;
    return -1;
  }

  ExpCalculator exp_calculator;
  for (size_t i = 0; i < reg_addr.size(); i++)
  {
    std::cout << register_map[reg_addr[i]].name << " 0x";
    std::cout << std::hex << reg_addr[i] << " :";
    std::string exp = register_map[reg_addr[i]].formula;
    exp.replace(
      exp.find("data"), 4,
      std::to_string(getBitsAsDouble(reg_val[i], register_map[reg_addr[i]].low, register_map[reg_addr[i]].high)));
    exp_calculator.evaluate(exp);
    std::cout << exp_calculator.getResult() << std::endl;
  }

  return 0;
}