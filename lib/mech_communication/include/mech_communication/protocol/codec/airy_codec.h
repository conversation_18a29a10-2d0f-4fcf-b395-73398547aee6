﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef AIRY_CODEC_H
#define AIRY_CODEC_H

#include "mech_base_codec.h"
#include <cstdint>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
class AiryCodec : public MechBaseCodec
{
public:
  AiryCodec();
  AiryCodec(AiryCodec&&) noexcept = delete;
  AiryCodec(const AiryCodec&)     = delete;
  AiryCodec& operator=(AiryCodec&&) = delete;
  AiryCodec& operator=(const AiryCodec&) = delete;
  ~AiryCodec() override                  = default;

  std::string getCurrentProtocolType() override;
  bool packCtrlTxChannelExclusively(const int _channel_num,
                                    const bool _is_open,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet) override;
  bool packReadTxChannelAll(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet) override;
  bool packCtrlTxChannelAll(const bool _open,
                            uint32_t& _expected_packet_response_code,
                            std::vector<uint8_t>& _packet) override;
  bool packCtrlTxChannel(const int _channel_num,
                         const bool _open,
                         const uint32_t _curr_value,
                         uint32_t& _expected_packet_response_code,
                         std::vector<uint8_t>& _packet) override;
  bool getTxChannelRegAddr(const int _channel_num, uint32_t& _tx_channel_reg_addr) override;

  bool packWriteDigitalRegister(const RegisterData _register_data,
                                uint32_t& _expected_packet_response_code,
                                std::vector<uint8_t>& _packet) override;

private:
};

}  // namespace lidar
}  // namespace robosense

#endif  //AIRY_CODEC_H
