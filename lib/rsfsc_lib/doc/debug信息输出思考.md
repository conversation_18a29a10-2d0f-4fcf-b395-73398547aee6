﻿rsfsc_lib在`3292756d`这个commit，加-g参数编译出来是27M，不加的话编译出来是1.2M，差了有20倍，不加-O3是1.2M。-g1是4.8M，默认-g2

-g允许和-O参数一起使用，这时候行数和参数名等信息可能都是不准确的，只能知道个大概

https://gcc.gnu.org/onlinedocs/gcc-12.2.0/gcc/Debugging-Options.html#Debugging-Options

```

Request debugging information and also use level to specify how much information. The default level is 2.

Level 0 produces no debug information at all. Thus, -g0 negates -g.

Level 1 produces minimal information, enough for making backtraces in parts of the program that you don’t plan to debug. This includes descriptions of functions and external variables, and line number tables, but no information about local variables.

Level 3 includes extra information, such as all the macro definitions present in the program. Some debuggers support macro expansion when you use -g3.

If you use multiple -g options, with or without level numbers, the last such option is the one that is effective.

```

-g1有崩溃的文件名和行数，以及这一行对应的代码
-没找到g2的定义，但从描述上推测，应该是临时变量的值