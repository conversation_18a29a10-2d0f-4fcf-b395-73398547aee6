﻿﻿# mech_communication
mech_communication is a utility that allows depend to communicate mech lidar.Additional line of information text about what the project does. 
## 1 Prerequisites
Before you begin, ensure you have met the following requirements:
- Boost 1.71
- Qt 5.12
- Cmake 3.14 or latest
- C++ 14 or latest
- Linux machine

## 2 Compiling
To compile mech_communication, follow these steps:  
shell commands
```sh
mkdir build && cd build
cmake ..
make
```
## 3 Usage
To depend mech_communication, follow these steps:  
git commands
```git
git submodule add git.path lib/mech_communication
cd lib/mech_communication
git checkout branch(you use)
```
cmake commands
```cmake
add_subdirectory(lib/mech_communication)
...
add_executable(${PROJECT_NAME})
...
target_link_libraries(${PROJECT_NAME} PRIVATE mech_communication)
```
cpp commands
```cpp
#include "mech_tcp.h"
#include "mech_udp.h"
```
## 4 Test
To test mech_communication, follow these steps:  
change 
```cmake 
option(BUILD_EXAMPLE "build example or not" OFF)
```
to 
```cmake 
option(BUILD_EXAMPLE "build example or not" ON)
```
  in `CMakeLists.txt`  

shell commands
```sh
mkdir build && cd build
cmake ..
make
```