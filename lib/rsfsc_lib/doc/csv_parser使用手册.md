﻿## CSV解析库使用说明

本库主要实现功能如下：
- 从CSV解析寄存器和参数阈值信息的功能
- 读入寄存器信息后，直接通过寄存器名称、类型索引等来索引其对应的地址，模式，数值等信息的功能
- 读入参数信息后，直接通过参数名称来索引其对应的信息的功能


### 1 使用说明

1. 按照格式定义好对应的register和limit文件，这些文件应该随软件安装到系统目录防止篡改，格式可参考template中的示例
2. 声明一个对象`robosense::lidar::CsvParser csv_info;"`
3. 调用`loadRegisterCsvInfo`和`loadLimitCsvInfo`去加载对应文件，注意这两个函数`return false`时代表加载错误，不能继续进行
4. 调用对应`get...`来获取参数

#### 1.1 读寄存器使用说明

寄存器读取得到的是`std::multimap<std::string, RegisterInfo>`格式的，其中first索引是`string`类型的寄存器名称，`RegisterInfo`可参考头文件定义。使用`multimap`的意义在于允许同名不同寄存器模式的寄存器存在，以满足程序运行到不同阶段对同一寄存器的不同操作。

示例代码：

```cpp
robosense::lidar::CsvParser register_info;
if (!register_info.loadRegisterCsvInfo("/usr/local/share/test/test_register.csv"))
{
  return false; // 加载失败时退出并报错
}
auto single_register_info = register_info.getRegisterInfo("phase_align"); // 获取单个寄存器信息
if (!single_register_info.is_ok)
{
  return false; // 获取失败
}
auto init_mode_register_info = register_info.getSelectIndexPropertyRegisterInfo("init"); // 获取所有模式为init的寄存器，注意，此函数返回可以与MEMSTCP的一个接口配套使用
// 。。。还有其他的一些不常用函数，可仔细查阅头文件
```

#### 1.2 读参数阈值使用说明

limit读取到的是`std::map<std::string, LimitInfo>`格式的，不允许存在同名的limit，其中first索引是`string`类型的limit名称，`LimitInfo`可参考头文件定义

为了防止添加或者减少额外的字段带来出错风险，最新的版本`LimitInfo`类已经私有，禁止用户代码调用`LimitInfo`类构造函数，所有`LimitInfo`类实例化必须通过`CsvParser`类加载配置文件实现

最新的`LimitInfo`类如下：

```cpp
/**
 * @brief 阈值基础信息
 */
struct LimitInfo
{
  friend class CsvParser;
  friend class LidarAbnormalMonitor;

  bool is_ok    = false;  //当前参数是否存在，每次搜索参数后，可先读取这个状态值，来判断当前寄存器是否可用
  double min_th = -9999.0;                  //阈值下限
  double max_th = 9999.0;                   //阈值上限
  std::string limit_text;                   //字符串文本
  std::vector<std::string> extra_str_info;  //额外的输入信息(字符串格式)

  std::string getName() const { return name_; }  //获取参数名称
  std::string getUnit() const { return unit_; }  //获取参数单位
  void setNameSuffix(const std::string& _suffix);  //设置名称后缀，一般不可使用，通过CsvParser::setNameSuffix进行设置
  std::string getNameSuffix() const { return name_suffix_; }  //获取名称后缀

private:
  // 私有构造函数
  LimitInfo() = default;

  std::string name_;         //参数名称
  std::string unit_;         //参数单位
  std::string name_suffix_;  //名称后缀
};
```

示例代码：

- 从limit文件获取使用`LimitInfo`

```cpp
robosense::lidar::CsvParser limit_info;
if (!limit_info.loadRegisterCsvInfo("/usr/local/share/test/test_limit.csv"))
{
  return false; // 加载失败时退出并报错
}
const auto& single_limit_info = limit_info.getLimitInfo("phase_align"); // 获取单个limit
if (!single_limit_info.is_ok)
{
  return false; // 获取失败
}
single_limit_info.getName();
single_limit_info.getUnit();
auto all_limit_info = limit_info.getAllLimitInfo(); // 获取所有LimitInfo信息
if (limit_info.checkWithinLimit("phase_align", 10.5))
{
  // 测试值在阈值范围内
}
else
{
  // 测试值不满足阈值要求
}
```

- 批量处理LimitInfo阈值相同，但最终结果测试结果不同的情况

```cpp
robosense::lidar::CsvParser limit_info;
if (!limit_info.loadRegisterCsvInfo("/usr/local/share/test/test_limit.csv"))
{
  return false; // 加载失败时退出并报错
}
const auto& single_limit_info = limit_info.getLimitInfo("gpio_state"); // 获取单个limit
if (!single_limit_info.is_ok)
{
  return false; // 获取失败
}
for (int i = 0; i < 10; ++i)
{
  auto suffix     = "_" + std::to_string(i);
  auto temp_limit = csv_parser_.setNameSuffix("gpio_state", suffix);
  widget_log_setting_->addMeasureMessage(1, temp_limit, "PASS");
}
```

![](img/limit_info_test.png)

#### 2.3 注意事项

- 通过参数名字搜索特定的参数信息后，首先判断当前参数状态 `re_1.is_ok`，搜索成功则状态为 true，此时可对该参数做正常的读写操作，若未搜索到，首先检查软件的参数信息csv文件是否存在格式或者内容的错误；其次要确保搜索的参数名称与csv文件中的名称是完全一致的，才可正常使用。
- 建议将软件对应的csv文件存储在 config 目录中，封装时直接安装到系统中，降低暴漏和被误修改的风险。
- 可以关注RSFSCLog输出的一些错误信息
- limit参数名称使用小写加下划线形式，参数后面的一些`threshold`字眼可以去掉，limit文件本身已有相关含义
  ![](img/limit_example.png)
- 由于这些文件会有其他同事查看和编辑，所以需要将文件格式保存为`UTF-8 with BOM`格式，同时换行使用`CRLF`格式

### 3 常用的单位

尽量使用[国际单位制](https://zh.wikipedia.org/wiki/%E5%9B%BD%E9%99%85%E5%8D%95%E4%BD%8D%E5%88%B6)，且注意不要使用中文和特殊字符，以下是一些常见的单位：

+ 长度: m, 米
+ 电阻: Ohm, 欧姆; kOhm, 千欧; MOhm, 兆欧；
+ 电压: V, 伏特
+ 时间: s, 秒
+ 温度: Celsius, 摄氏度
+ 角度: degree, 度
+ 光强: lx, 勒克斯
+ 光功率: mw, 毫瓦
+ 频率: Hz, 赫兹
+ 压强: kPa, 千帕
+ 弧度: rad，弧度
