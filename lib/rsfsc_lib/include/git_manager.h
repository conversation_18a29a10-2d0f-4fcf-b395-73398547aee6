﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   git_manager.h
 * <AUTHOR> Wang (<EMAIL>)
 * @brief
 * @version 1.0.0
 * @date 2024-06-12
 *
 * Copyright (c) 2021, RoboSense Co.,Ltd. - www.robosense.ai
 * All Rights Reserved.
 *
 * You can not use, copy or spread without official authorization.
 *
**/
#ifndef RSFSCLIB_GIT_MANAGER_H
#define RSFSCLIB_GIT_MANAGER_H

#include "widget_git_manager.h"
#include "widget_lidar_info.h"
#include <qobject.h>
#include <qobjectdefs.h>
#include <qwidget.h>
#include <string>

class git_time;
class git_credential;
class git_indexer_progress;
class git_repository;
class git_reference;
class git_oid;
namespace robosense
{
namespace lidar
{

/**
 * @brief Git管理器类
 */
class GitManager : public QObject
{
  Q_OBJECT
public:
  GitManager();
  explicit GitManager(GitManager&&)      = delete;
  explicit GitManager(const GitManager&) = delete;
  GitManager& operator=(GitManager&&) = delete;
  GitManager& operator=(const GitManager&) = delete;
  ~GitManager() override;

  void setWidget(rsfsc_lib::WidgetGitManager* _widget);
  bool clone(const std::string& _repo_url, const std::string& _local_path);
  bool open(const std::string& _local_path);

  int checkoutBranch(const std::string& _branch_name);
  bool compareLocalAndRemoteCommits();
  bool isExistUncommittedChanged();
  bool pull();

public Q_SLOTS:
  bool slotOpen();
  bool slotClone();
  bool slotCheckoutBranch(const std::string& _branch_name);
  void slotPull();

private Q_SLOTS:
  void slotRemoteCompare();
  void slotLocalCompare();

private:
  void branchListing();
  void revWalking();
  static std::string gitTimeToStr(const git_time* _time);
  static int statusSingleCb(const char* _p, unsigned int _s, void* _payload);
  static int credAcquireCb(git_credential** _out,
                           const char* _url,
                           const char* _username_from_url,
                           unsigned int _allowed_types,
                           void* _payload);
  static void checkoutProgress(const char* _path, size_t _cur, size_t _tot, void* _payload);
  static int sidebandProgress(const char* _str, int _len, void* _payload);
  static int fetchProgress(const git_indexer_progress* _stats, void* _payload);

  static size_t instance_count_;  // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)

  bool open_repo_finished_ { false };
  rsfsc_lib::WidgetGitManager* widget_git_manager_ { nullptr };
  git_repository* repo_ { nullptr };
  // git_strarray* ref_list_;
  git_reference* head_ref_ { nullptr };
  const git_oid* remote_id_ { nullptr };
};

}  // namespace lidar
}  // namespace robosense

#endif
