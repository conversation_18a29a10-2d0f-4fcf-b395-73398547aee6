﻿if(WIN32)
  set(<PERSON><PERSON>_CODENAME "msvc2017_64")
else()
  # Check for the presence of lsb_release and provide installation instructions if not found
  find_program(LSB_EXEC lsb_release)
  if(NOT LSB_EXEC)
    message(WARNING "lsb_release not found, please install it using: sudo apt install lsb-release")
  else()
    execute_process(
      COMMAND ${LSB_EXEC} -cs
      OUTPUT_VARIABLE LSB_CODENAME
      OUTPUT_STRIP_TRAILING_WHITESPACE)
  endif()
endif()
