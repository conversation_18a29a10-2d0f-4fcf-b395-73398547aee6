﻿cmake_minimum_required(VERSION 3.16)
string(TIMESTAMP PROJECT_COMPILE_DATE %Y%m%d)
string(TIMESTAMP PROJECT_COMPILE_TIME %H%M%S)
project(
  mech_connectivity_test
  VERSION 1.8.4.${PROJECT_COMPILE_DATE}
  DESCRIPTION "Mech Connectivity Test Project"
  LANGUAGES C CXX)

set(PROJECT_CODE "350")
set(PROJECT_NAME_EN "Mech Connectivity Test")
set(PROJECT_NAME_ZH "MECH连通性测试")

set(USE_QT_SERIALPORT ON)
set(USE_QT_CONCURRENT OFF)
set(USE_QT_CHARTS OFF)

# =========================
# Option
# =========================
option(BUILD_PROJECT_UTEST "build gtest or not" ON)

set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} "${PROJECT_SOURCE_DIR}/cmake")

include(cmake/base.cmake)
include(cmake/system_name.cmake)
include_directories(lib)

# set(INSTALL_PREFIX_LIB ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}/)
# set(INSTALL_PREFIX_BIN ${CMAKE_INSTALL_PREFIX}/bin/${PROJECT_NAME}/)
set(INSTALL_PREFIX_SHARE ${CMAKE_INSTALL_PREFIX}/share/${PROJECT_NAME}/)
message(STATUS "CMAKE_INSTALL_PREFIX: ${CMAKE_INSTALL_PREFIX}")
set(CMAKE_INSTALL_RPATH ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME} ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}/lib)

# include(cmake/qt5_auto.cmake)

# =========================
# Set C++ Standard
# =========================
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(FACTORY_LOCATION "HHL")

# ========================= 如果去除这句话会出现找不到公共库的libhasp.so问题
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU")
  set(CMAKE_EXE_LINKER_FLAGS "-Wl,--disable-new-dtags")
endif()

# for static analysis
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
find_package(Git QUIET)
execute_process(COMMAND ${GIT_EXECUTABLE} config core.hooksPath .githooks
                WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}")
execute_process(
  COMMAND ${GIT_EXECUTABLE} rev-parse --short HEAD
  WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
  OUTPUT_VARIABLE GIT_OUTPUT
  OUTPUT_VARIABLE PROJECT_COMPILE_COMMIT
  ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)
configure_file("${PROJECT_SOURCE_DIR}/include/config.h.in" "${PROJECT_SOURCE_DIR}/src/config.h")

find_package(Threads REQUIRED)
find_package(
  Qt5
  COMPONENTS Widgets Concurrent SerialPort Network
  REQUIRED)
find_package(PCAP MODULE REQUIRED)

# 添加子目录构建
add_definitions(-DRSFSCLOG_USE_SPDLOG -DRSFSCLOG_USE_QT -DRSFSCLOG_SHOW_FUNC)
add_subdirectory(lib/rsfsc_lib)
find_package(RSFSCLog CONFIG REQUIRED)

add_subdirectory(lib/mech_comm_lib)
add_subdirectory(lib/mech_communication)
add_subdirectory(lib/lidar_communication_box)

# 设置头文件和源文件
file(GLOB_RECURSE HEADER_FILES CONFIGURE_DEPENDS include/*.h include/*.hpp)
file(GLOB_RECURSE SOURCE_FILES CONFIGURE_DEPENDS src/*.cpp src/*.h)
file(GLOB_RECURSE QT_UI_FILES CONFIGURE_DEPENDS ui/*.cpp ui/*.ui ui/*.h)
set(QT_RESOURCES resource/resource.qrc resource/appicon.rc)
# file append rsfsc_lib/
list(APPEND QT_UI_FILES lib/rsfsc_lib/ui/widget_about.h lib/rsfsc_lib/ui/widget_about.cpp
     lib/rsfsc_lib/include/widget_result_table.h)

add_executable(${PROJECT_NAME} ${HEADER_FILES} ${SOURCE_FILES} ${QT_UI_FILES} ${QT_RESOURCES})
set_target_properties(
  ${PROJECT_NAME}
  PROPERTIES AUTOMOC ON
             AUTORCC ON
             AUTOUIC ON)
target_include_directories(${PROJECT_NAME} SYSTEM PRIVATE "${CMAKE_BINARY_DIR}/${PROJECT_NAME}_autogen/include")
target_include_directories(${PROJECT_NAME} SYSTEM PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/lib/protobuf")
# 设置 AUTOUIC 相关属性
set(AUTOUIC_SEARCH_PATHS ${CMAKE_CURRENT_SOURCE_DIR}/ui)
set_target_properties(${PROJECT_NAME} PROPERTIES AUTOUIC ON AUTOUIC_SEARCH_PATHS "${AUTOUIC_SEARCH_PATHS}")

target_include_directories(${PROJECT_NAME} PRIVATE include src lib ui)
target_compile_definitions(${PROJECT_NAME} PRIVATE RSFSCLOG_FORMAT_ENUM)

# 链接库和其他设置
target_link_libraries(
  ${PROJECT_NAME}
  PRIVATE rsfsc_lib
          mech_communication
          lidar_communication_box
          mech_comm_func
          rsfsc_fsm
          Threads::Threads
          Qt5::Widgets
          Qt5::Network
          Qt5::SerialPort)

if(BUILD_PROJECT_UTEST)
  enable_testing()
  add_subdirectory(test)
endif()

include(cmake/cpack.cmake)
