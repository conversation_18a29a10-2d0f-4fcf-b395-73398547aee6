# 寄存器名称：在程序中生成 '寄存器名称 - 数值结构体'的 std::map　直接通过名称来索引对应的数值结构体 从而在程序中进行寄存器的相关读写操作,,,,,,,,
# 寄存器地址：如果是多个连续地址的 写首地址 加0x前缀 寄存器地址小写,,,,,,,,
# 寄存器个数（最小值为1）：以当前寄存器地址为起始地址 连续读取的寄存器数量 (个数为包含首地址的10进制数) 连续寄存器地址默认间隔为4,,,,,,,,
# 寄存器模式: 可以是任意自定义的字符串，例如init，abs_test等，用于标识不同过程
# 标定时设定值：  标定时需要设定的寄存器值(加0x前缀的16进制数),,,,,,,,
# 标定后恢复值：  标定完成需要恢复的寄存器值(加0x前缀的16进制数),,,,,,,,
# 寄存器最小值：  寄存器允许写入的最小值(加0x前缀的16进制数),,,,,,,,
# 寄存器最大值：  寄存器允许写入的最大值(加0x前缀的16进制数),,,,,,,,
# 备注：          寄存器含义以及其参数含义,,,,,,,,
# 特殊说明1：csv读取是以　'逗号'　来划分格子，注意　'备注'　中，不要添加逗号、换行等特殊字符，否则程序无法完全读取备注信息,,,,,,,,
# 特殊说明2：'寄存器名称'和备注均设置为与PL给出的寄存器表格内容统一，并注意适当变通,,,,,,,,
# 特殊说明3：本文件从第17行开始解析，请不要随意增删前面的行数               
#                
#                
,,,,,,,,
寄存器名称, 寄存器地址, 寄存器个数,寄存器模式, 标定时设定值, 标定后恢复值, 寄存器最小值, 寄存器最大值, 备注
mirror_func_safe_close,0x83ca0138,1,fusa,0x5500,0x0,,,振镜功能安全 0x5555：全关 0：全开,
function_safety_close,0x83C60024,1,fusa,0x55,0x0,,,人眼安全功能开关 0x55：关 0：开,
rx_func_close,0x83c00764,1,fusa,0x55,0x0,,,接收功能安全开关 0x55：关 0：开,
mems_ctrl_en,0x83ca0000,1,init,0,0x55,,,关闭振镜：0关闭，0x55打开,
wave_en,0x83c00074,1,init,0,1,,,点云数据上传开关：0关闭，1打开,回波使能信号
charge_fix_en   ,0x83c60264,1,init,1,0,,,档位固定设置： 0X0不固定档位，0x1固定档位,
charge_fix_value,0x83c60268,1,init,3,0,,,档位调节开关： 0X0动态调节，0x1 一档，0x2 二挡，0x3 三挡,
apc_close,0x83c6000c,1,init,1,0,,,APC开关功能：1关闭，0打开,