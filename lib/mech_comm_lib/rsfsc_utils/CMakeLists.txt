﻿cmake_minimum_required(VERSION 3.10)

project(
  rsfsc_utils
  VERSION 1.0.0
  LANGUAGES CXX)

# 创建库
add_library(rsfsc_utils STATIC)
add_library(rsfsc::utils ALIAS rsfsc_utils)

add_library(tcpdump_utils STATIC)
add_library(rsfsc::tcpdump_utils ALIAS tcpdump_utils)

# 获取除tcpdump_utils.cpp外的所有源文件
file(GLOB_RECURSE UTILS_SOURCE_FILES CONFIGURE_DEPENDS src/*.cpp)
# list(REMOVE_ITEM UTILS_SOURCE_FILES "${CMAKE_CURRENT_SOURCE_DIR}/src/tcpdump_utils.cpp")

# 分别设置源文件
target_sources(rsfsc_utils PRIVATE ${UTILS_SOURCE_FILES})
target_sources(tcpdump_utils PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/src/tcpdump_utils.cpp")

# 设置包含目录
target_include_directories(rsfsc_utils PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
                                              $<INSTALL_INTERFACE:include>)

target_include_directories(tcpdump_utils PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
                                                $<INSTALL_INTERFACE:include>)

# 设置依赖
target_link_libraries(rsfsc_utils PUBLIC Qt5::Core)
target_link_libraries(tcpdump_utils PUBLIC Qt5::Core)
