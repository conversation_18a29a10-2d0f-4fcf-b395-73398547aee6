﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

/****************************************************************
 * @file      config.h
 * <AUTHOR>
 * @brief     this file was generated automatically, dont try to change it
****************************************************************/

#define MECH_COMMUNICATION_VERSION_MAJOR @PROJECT_VERSION_MAJOR@
#define MECH_COMMUNICATION_VERSION_MINOR @PROJECT_VERSION_MINOR@
#define MECH_COMMUNICATION_VERSION_PATCH @PROJECT_VERSION_PATCH@
#define MECH_COMMUNICATION_VERSION_TWEAK @PROJECT_VERSION_TWEAK@
#define MECH_COMMUNICATION_VERSION_STR   "@PROJECT_VERSION@"
#define PROJECT_NAME                     "@PROJECT_NAME@"
#define PROJECT_COMPILE_COMMIT           "@PROJECT_COMPILE_COMMIT@"
#define PROJECT_COMPILE_TIME             "@PROJECT_COMPILE_TIME@"
