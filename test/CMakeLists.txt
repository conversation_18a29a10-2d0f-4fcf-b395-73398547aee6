﻿project(test_main LANGUAGES CXX)
find_package(Threads REQUIRED)

# If GoogleTest is not found, fetch it using FetchContent
find_package(GTest QUIET)
if(NOT GTest_FOUND)
  include(FetchContent)

  execute_process(
    COMMAND git config --get remote.origin.url
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE CURRENT_GIT_URL
    OUTPUT_STRIP_TRAILING_WHITESPACE)

  if(CURRENT_GIT_URL MATCHES "^git@")
    set(GOOGLE_TEST_GIT_URL "***********************:system_codebase/factory_tool/common_lib/googletest.git")
  elseif(CURRENT_GIT_URL MATCHES "^http://")
    set(GOOGLE_TEST_GIT_URL "http://gitlab.robosense.cn/system_codebase/factory_tool/common_lib/googletest.git")
  elseif(CURRENT_GIT_URL MATCHES "^https://")
    set(GOOGLE_TEST_GIT_URL "https://gitlab.robosense.cn/system_codebase/factory_tool/common_lib/googletest.git")
  else()
    message(FATAL_ERROR "Unsupported Git URL format: ${CURRENT_GIT_URL}")
  endif()

  FetchContent_Declare(
    googletest
    GIT_REPOSITORY ${GOOGLE_TEST_GIT_URL}
    GIT_TAG release-1.12.1
    GIT_SHALLOW TRUE
    GIT_DEPTH 1
    GIT_CONFIG advice.detachedHead=false)

  # Download and configure GoogleTest
  # FetchContent_MakeAvailable(googletest)
  FetchContent_GetProperties(googletest)
  if(NOT googletest_POPULATED)
    FetchContent_Populate(googletest)
    add_subdirectory(${googletest_SOURCE_DIR} ${googletest_BINARY_DIR} EXCLUDE_FROM_ALL)
  endif()
endif()

add_executable(${PROJECT_NAME} test.cpp)
target_link_libraries(${PROJECT_NAME} gtest gtest_main Threads::Threads)
