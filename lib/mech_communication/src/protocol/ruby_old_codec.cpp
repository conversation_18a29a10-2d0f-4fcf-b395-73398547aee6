﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mech_communication/protocol/codec/ruby_old_codec.h"
#include "mech_communication/protocol/data_struct/helios.h"
#include "mech_communication/protocol/data_struct/ruby.h"

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

std::string RubyOldCodec::getCurrentProtocolType() { return "Ruby4.0 old"; }

bool RubyOldCodec::extractRegister(const std::vector<uint8_t>& _payload)
{
  if ((_payload.size()) % sizeof(uint32_t) != 0)
  {
    error_str = "register data length error, payload size must divisible by 4, payload actual size:" +
                std::to_string(_payload.size());
    return false;
  }

  if (!register_value_queue.empty())
  {
    std::queue<uint32_t>().swap(register_value_queue);
  }

  uint32_t register_data {};
  for (size_t i = 0; i < _payload.size(); i += sizeof(register_data))
  {
    memcpy(&register_data, &_payload.at(i), sizeof(register_data));
    register_value_queue.emplace(register_data);
  }

  return true;
}

bool RubyOldCodec::extractMultiRegister(const std::vector<uint8_t>& _payload) { return extractRegister(_payload); }
bool RubyOldCodec::extractConRegister(const std::vector<uint8_t>& _payload) { return extractRegister(_payload); }

}  // namespace lidar
}  // namespace robosense
