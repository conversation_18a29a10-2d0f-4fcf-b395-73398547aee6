﻿cmake_minimum_required(VERSION 3.15)
set(BUILD_GMOCK
    OFF
    CACHE BOOL "disable gmock build")

# 修改 MD 为 MT
if(MSVC)
  set(CMAKE_CXX_FLAGS_RELEASE "/MT")
  set(CMAKE_CXX_FLAGS_DEBUG "/MTd")
endif(MSVC)

# If GoogleTest is not found, fetch it using FetchContent
include(Fetch<PERSON>ontent)
# If GoogleTest is not found, fetch it using FetchContent
if(NOT GTest_FOUND)
  include(FetchContent)

  # Configure FetchContent to download GoogleTest from the specified repository
  FetchContent_Declare(
    googletest
    GIT_REPOSITORY ***********************:system_codebase/factory_tool/common_lib/googletest.git
    GIT_SHALLOW TRUE # 浅克隆 只clone指定分支或tag
    GIT_TAG release-1.12.1
    GIT_CONFIG advice.detachedHead=false)

  # Download and configure GoogleTest
  FetchContent_MakeAvailable(googletest)
  set_target_properties(gtest gtest_main PROPERTIES EXCLUDE_FROM_ALL TRUE)
endif()

add_executable(test_main test.cpp)

if(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
  target_link_libraries(test_main gtest mech_communication)
else()
  target_link_libraries(test_main gtest mech_communication pthread)
endif()

target_include_directories(test_main PRIVATE ${PROJECT_SOURCE_DIR}/include ${PROJECT_SOURCE_DIR}/src)
target_include_directories(test_main SYSTEM PRIVATE ${GTEST_INCLUDE_DIRS})
set_target_properties(
  test_main
  PROPERTIES CXX_STANDARD 14
             CXX_STANDARD_REQUIRED YES
             CXX_EXTENSIONS NO)

if(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
  target_compile_options(test_main PRIVATE /utf-8)
else()
  target_compile_options(test_main PRIVATE -Wall)
endif()

include(GoogleTest)
gtest_discover_tests(test_main)
