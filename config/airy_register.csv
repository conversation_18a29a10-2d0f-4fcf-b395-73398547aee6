﻿# 寄存器名称：在程序中生成 '寄存器名称 - 数值结构体'的 std::map 直接通过名称来索引对应的数值结构体 从而在程序中进行寄存器的相关读写操作,,,,,,,,
# 寄存器地址：如果是多个连续地址的 写首地址 加0x前缀 寄存器地址小写,,,,,,,,
# 寄存器个数(最小值为1)：以当前寄存器地址为起始地址 连续读取的寄存器数量 (个数为包含首地址的10进制数) 连续寄存器地址默认间隔为4,,,,,,,,
# 寄存器模式(0 1 2): 在程序中 首先读取寄存器模式数值 再决定去读取该模式的相关数值,,,,,,,,
#                ,"寄存器模式0 表示不改变寄存器数值的""只读操作""",,,,,,,
#                ,"寄存器模式1 表示改变后恢复寄存器原始数值的""读写操作""(本模式需要读取 '标定时设定值' 和 '标定后恢复值')",,,,,,,
#                ,"寄存器模式2 表示改变后保留寄存器新数值的""读写操作""(本模式读取 '寄存器最大值'和'寄存器最小值')",,,,,,,
# 标定时设定值：  标定时需要设定的寄存器值(加0x前缀的16进制数),,,,,,,,
# 标定后恢复值：  标定完成需要恢复的寄存器值(加0x前缀的16进制数),,,,,,,,
# 寄存器最小值：  寄存器允许写入的最小值(加0x前缀的16进制数),,,,,,,,
# 寄存器最大值：  寄存器允许写入的最大值(加0x前缀的16进制数),,,,,,,,
# 备注：          寄存器含义以及其参数含义,,,,,,,,
# 特殊说明1: csv读取是以 '逗号' 来划分格子，注意 '备注' 中，不要添加逗号，否则程序无法完全读取备注信息,,,,,,,,
# 特殊说明2: '寄存器名称'和备注均设置为与 'reg_map_mems_20210622.xlsm'统一的内容 并注意适当变通,,,,,,,,
,,,,,,,,
寄存器名称, 寄存器地址, 寄存器个数,寄存器模式, 标定时设定值, 标定后恢复值, 寄存器最小值, 寄存器最大值, 备注
angle_test_en,0x83c01028,1,time_sync,0x1,0x1,,,,模拟码盘角度使能
pulse_ch_en,0x83c01058,1,time_sync,0x1,0x1,,,,三组角度同步脉冲使能bit0->pulse0
pulse_width0_cpu,0x83c01068,1,time_sync,0xe0000,0xe0000,,,,脉冲宽度0
config_version,0x83c0000c,1,normal,0,0,,,整机配置版本
light_uptst_en,0x83c03200,1,normal,0x0,0x0,,,,光通上行测试控制 0x80:关闭上行；0x01:上行光通测试；0x11:上行光通注入误码测试
light_uptst_total,0x3101,1,normal,0x0,0x0,,,,光通上行测试总数据量
light_uptst_error,0x3105,1,normal,0x0,0x0,,,,光通上行测试误码数据量
pkt_cnt,0x83c03214,1,normal,0x0,0x0,,,,顶板到底板包计数
pkt_err_cnt,0x83c03218,1,normal,0x0,0x0,,,,顶板到底板CRC错误包计数
light_dwntst_total,0x83c0321c,1,normal,0x0,0x0,,,,光通下行测试总数据量
light_dwntst_error,0x83c03220,1,normal,0x0,0x0,,,,光通下行测试误码数据量
light_dwntst_en,0x3100,1,normal,0x0,0x0,,,,光通下行测试控制 0x80:关闭下行；0x01:下行光通测试；0x11:下行光通注入误码测试