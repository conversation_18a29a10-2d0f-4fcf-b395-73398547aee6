﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#pragma once

#include <cstdint>
#include <optional>
#include <string>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
namespace rsfsc_lib
{
class WidgetLogSetting;
}  // namespace rsfsc_lib
namespace mech
{
struct CustomerSN
{
  std::string mac_addr;
  std::string firmware_version;
  std::string firmware_md5;
  uint32_t top_version;
  uint32_t bot_version;
  uint32_t app_version;
  uint32_t motor_version;
  uint32_t config_version;
};

std::optional<CustomerSN> requireCustomerSN(rsfsc_lib::WidgetLogSetting* _widget_log_setting, int _lidar_index);
std::optional<float> requireVbdData(rsfsc_lib::WidgetLogSetting* _widget_log_setting, int _lidar_index);

}  // namespace mech
}  // namespace lidar
}  // namespace robosense
