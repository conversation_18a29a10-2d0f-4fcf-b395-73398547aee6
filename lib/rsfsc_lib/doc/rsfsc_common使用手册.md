﻿## rsfsc_common使用手册

rsfsc_common.hpp主要放置一些多数人（不是所有）能用到的公用代码，主要目的是提高复用，减少出错。这个库应该是所有使用者一起维护，一起测试的。

### 1 WindowSliceRefractionTransform

众所周知，光在不同介质的速度会不一样，从一种介质进入另外一种介质时会产生折射。这个结构体用于计算窗口片对光斑的折射情况，由于窗口片具有厚度，且呈圆弧形，导致光进入到窗口片时的折射角与光出去窗口片时的折射角不一样（0216呈平面就没有这个问题），所以光经过窗口片后整体角度发生了偏转。如果你在已知进入窗口片前的角度下，希望知道出射角度，你就可以调用这个类：

无窗口片时，振镜停止时，5个通道的光斑从左到右出射角度依次为-48.5、-24.25、0、24.25、48.5

```cpp
using namespace robosense::lidar;
rsfsc_lib::WindowSliceRefractionTransform wsrt_021a(ProjectCode::PROJECT_021A);
double real_out_angle1 = wsrt_021a.calFunc(-48.5);
double real_out_angle2 = wsrt_021a.calFunc(24.25);
```