﻿## GitManager库使用说明

本库主要实现功能如下：
- 从远程Git仓库克隆到本地功能
- 对比本地文件与仓库目录是否一致
- 对比本地commit与远程仓库的commit是否一致
- 从远程仓库下载文件到本地


### 1 使用说明
1. 声明一个GitManager对象和WidgetGitManager对象，其中`WidgetGitManager`是一个`QWidget`对象，用于显示及输入GitManager的相关信息
  ```cpp
  robosense::lidar::rsfsc_lib::WidgetGitManager* widget_git_manager_ { nullptr };
  robosense::lidar::GitManager* git_manager_ { nullptr };
  ```
2. GitManager对象和WidgetGitManager对象实例化后通过`GitManager::setWidget`接口进行关联
3. 手动操作界面（相关功能分别在`GitManger`类中分别有对应的接口可在代码端调用）
   ![widget_git_manager](img/widget_git_manager.png)
  + `Clone`按钮：从远程仓库克隆到本地，克隆成功后会自动打开本地仓库(***为做好公司Gitlab仓库权限管控，当前仅支持HTTP协议方式克隆，克隆过程需要输入用户名密码***)
  + `Open`按钮: 打开本地仓库
  + `Pull`按钮：从远程仓库下载文件到本地
  + `远程比对`按钮：对比本地仓库与远程仓库的commit差异
  + `本地比对`按钮：对比本地文件与仓库目录的差异
  