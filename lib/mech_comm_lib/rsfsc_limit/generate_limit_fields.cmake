﻿find_package(Python3 REQUIRED)

# Define the paths in CMake
# CMAKE_CURRENT_SOURCE_DIR is the directory of the current CMakeLists.txt
# CMAKE_SOURCE_DIR is the top-level source directory of your project

set(PYTHON_SCRIPT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/gen_limit_fields.py)
set(INPUT_CONFIG_DIR ${CMAKE_SOURCE_DIR}/config) # Or ${CMAKE_SOURCE_DIR}/config
set(GENERATED_OUTPUT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include/rsfsc_limit
)# Or ${CMAKE_BINARY_DIR}/generated_include/limit if you prefer build dir

# Ensure the output directory exists before Python script tries to create files in it
# (Though the Python script also does mkdir, it's good practice in CMake too)
file(MAKE_DIRECTORY ${GENERATED_OUTPUT_DIR})

message(STATUS "Running Python script to generate limit headers...")
message(STATUS "  Script: ${PYTHON_SCRIPT_PATH}")
message(STATUS "  Config Dir: ${INPUT_CONFIG_DIR}")
message(STATUS "  Output Dir: ${GENERATED_OUTPUT_DIR}")

# Run Python script to generate include/limit/*.h files
execute_process(
  COMMAND ${Python3_EXECUTABLE} ${PYTHON_SCRIPT_PATH} --config-dir ${INPUT_CONFIG_DIR} --output-dir
          ${GENERATED_OUTPUT_DIR}
  # WORKING_DIRECTORY ${CMAKE_SOURCE_DIR} # Or CMAKE_CURRENT_SOURCE_DIR.
  # If all paths are absolute (as ${VAR} usually are),
  # this matters less, but can be important if the
  # script itself tries to resolve relative paths.
  # The Python script now uses Path objects with paths
  # passed as arguments, so it should be robust.
  RESULT_VARIABLE result
  OUTPUT_VARIABLE py_stdout
  ERROR_VARIABLE py_stderr)

if(NOT result EQUAL 0)
  message(FATAL_ERROR "❌ 生成 limit 头文件失败，Python 脚本返回码: ${result}\nSTDOUT:\n${py_stdout}\nSTDERR:\n${py_stderr}")
else()
  message(STATUS "✅ Python script executed successfully.\nSTDOUT:\n${py_stdout}")
  if(py_stderr)
    message(WARNING "Python script stderr:\n${py_stderr}")
  endif()
endif()

# Important: If these generated headers are needed by targets defined in this
# or sub-CMakeLists.txt files, you need to tell CMake about them.
# For example, you might need to add GENERATED_OUTPUT_DIR to include_directories.
# include_directories(${GENERATED_OUTPUT_DIR})
#
# Also, if you use add_custom_command/add_custom_target, you can make
# other targets depend on the generation. execute_process runs at configure time.
