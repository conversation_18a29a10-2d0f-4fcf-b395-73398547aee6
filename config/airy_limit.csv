# 参数名称会直接作为索引读到程序中，请注意不要随意更改,,,,
# 下限、上限不得留空，如果范围不确定或者很广，则写一个很大的数即可。默认为浮点数,,,,
# 上下限采用小于等于、大于等于的形式，即当特性等于上下限时为合格,,,,
# 单位写明方便测试和工程人员查看分析，若没有单位，则统一写 unitless,,,,
# 备注用于说明一些特殊情况，方便其他人查看,,,,
,,,,
参数名称,下限,上限,单位,备注
fsm_lidar_is_exist,1,1,unitless,lidar在位状态
fsm_box_lidar_power_on,1,1,unitless,lidar上电状态
fsm_require_vbd_data,1,1,unitless,获取VBD数据失败
fsm_vbd_voltage,-21.3,-20.4,Voltage,vbd测量值
fsm_write_sn,1,1,unitless,更改lidar序列号
fsm_write_459_config,1,1,unitless,写配置文件
fsm_ping_lidar,1,1,unitless,ping雷达状态
fsm_connect_lidar,1,1,unitless,连接lidar状态
fsm_get_config_param,1,1,unitless,获取配置参数
fsm_set_time_sync_mode,1,1,unitless,设置时间同步模式
fsm_restore_time_sync_mode,1,1,unitless,恢复时间同步模式
fsm_msop_time,0,35,s,MSOP包时间
fsm_box_lidar_current_wait_bot,0,650,mA,盒子检测电流启动中
fsm_box_lidar_voltage,0,14,V,盒子检测电压for功率
fsm_box_lidar_current_wait_top,0,650,mA,盒子检测电流顶板启动完成
fsm_box_lidar_power,0,7.8,W,盒子检测功率
fsm_connect,1,1,unitless,连接状态
fsm_msop_channel,1,1,unitless,MSOP通道状态
fsm_difop_obtain,1,1,unitless,DIFOP获取状态
fsm_msop_obtain,1,1,unitless,MSOP获取状态
fsm_msop_valid,1,1,unitless,MSOP有效状态
fsm_box_sync_status,1,1,unitless,盒子获取的同步状态
fsm_read_time_sync_mode,0,0,unitless,时间同步模式0为GPS
fsm_bot_firmware_version,0x100304ff,0x100304ff,unitless,MES前底板固件检查
fsm_app_firmware_version,0x250327ff,0x250327ff,unitless,MES前APP固件检查
fsm_total_config_version,0,0,unitless,MES前整机配置检查
fsm_up_light_test_en,1,1,unitless,光通上行测试控制
fsm_up_light_test_total,1,nan,unitless,光通上行测试总数据量
fsm_up_light_test_error,0,0,unitless,光通上行测试误码数
fsm_up_light_test_error_rate,0,0,unitless,光通上行误码率
fsm_down_light_test_en,1,1,unitless,光通上行测试控制
fsm_down_light_test_total,1,nan,unitless,光通下行测试总数
fsm_down_light_test_error,0,0,unitless,光通下行测试误码数
fsm_down_light_test_error_rate,0,0,unitless,光通下行误码率
fsm_direct_motor_speed,590,610,rpm,直接获取电机转速
fsm_motor_curr,0.1,80,mA,电机母线电流
fsm_motor_coder_status_bit0,0,0,unitless,电机编码器状态、曾发生堵转
fsm_motor_coder_status_bit1,0,1,unitless,电机编码器状态、编码器测速、无感测速差距过大
fsm_motor_coder_status_bit2,0,0,unitless,电机编码器状态、编码器信号丢失
fsm_motor_coder_status_bit3,0,0,unitless,电机编码器状态、存在多个零位齿信号
fsm_motor_coder_status_bit4,0,0,unitless,电机编码器状态、没有存在零位齿信号
fsm_motor_coder_status_bit5,0,0,unitless,电机编码器状态、编码器信号异常
motor_set_speed,600,600,RPM,电机设定转速
top_firmware_version,nan,nan,unitless,顶板固件版本,difop
bot_firmware_version, nan, nan,unitless,底板固件版本,difop
app_firmware_version,nan,nan,unitless,APP固件版本,difop
fsm_459_config_version,0,0,unitless,整机配置文件版本,difop
motor_firmware_version,nan,nan,unitless,电机固件版本,difop
realtime_speed,590,610,RPM,电机实时转速,difop
top_input_vol,11,14,V,主板总输入电压,difop
top_3v8,3.6,4.0,V,主板3.8v电压,difop
top_3v3,3.1,3.5,V,主板3.3v电压,difop
top_1v1,1.0,1.2,V,主板接收1.1V电压,difop
top_neg_vol,-25,-11,V,主板接收负压,difop
top_3v3_rx,3.1,3.5,V,主板接收3.3V电压,difop
top_charge_vol,24,26,V,主板发射充能电压,difop
total_input_vol,9,32,V,整机输入电压,difop
bot_12v,11,13,V,底板12V电压,difop
bot_mcu_0v85,0.8,0.9,V,底板MCU0.85V电压,difop
bot_fpga_1v,0.9,1.1,V,底板FPGA内核1V,difop
total_input_cur,0.01,3,A,整机输入电流,difop
top_fpga_temp,0,120,celsius,主板fpga内核温度,difop
top_tx_temp,nan,nan,celsius,主板发射温度,difop
top_rx_459_temp_n,0,120,celsius,主板RX-459温度N端,difop
top_rx_459_temp_p,0,120,celsius,主板RX-459温度P端,difop
bot_imu_temp,0,120,celsius,底板IMU温度,difop
total_power,1,36,W,整机功率,difop
time_sync_status,1,1,unitless,时间同步状态,difop
pps_lock,1,1,unitless,PPS锁相状态,difop
gprmc_lock,1,1,unitless,GPRMC锁相状态,difop
utc_lock,1,1,unitless,UTC锁相状态,difop
gprmc_input,1,1,unitless,GPRMC输入状态,difop
pps_input,1,1,unitless,PPS输入状态,difop