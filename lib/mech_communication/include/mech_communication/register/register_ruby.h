﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * Definitions of Register.
 *
 * @defgroup MOD_register
 * 
 * Copyright ((C) 2014 - 2020 RoboSense, Co., Ltd.  All rights reserved.
 *
 * <AUTHOR> Liu
 *
 * @attention 20210806 Create register definitions file.
 *
 */

#ifndef RUBY_REGISTER_ADDRESS_H
#define RUBY_REGISTER_ADDRESS_H

#include <cstdint>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
namespace ruby
{
constexpr uint32_t RUBY4_BOT_VERSION  = 0x83C00000; /* FPGA bot version information (in hex), e.g. B02R03_V01S00. */
constexpr uint32_t RUBY4_BOT_VER_DATE = 0x83C00004; /* FPGA bot build date */
constexpr uint32_t RUBY4_BOARD_ID     = 0x83C00008; /* FPGA bot board id */

constexpr uint32_t RUBY4_TOP_POWER_TEMPERATURE =
  0x83c001F0; /* High 16bit: Temperature near the power supply on the front of the top, Low 16bit: Launch temperature 0 */
constexpr uint32_t RUBY4_TOP_RECV_TEMPERATURE =
  0x83C10058; /* top_fpga_rx_temp:Top receiver board temperature. calculation formula: (200 * data/4096 - 50) */
constexpr uint32_t RUBY4_TOP_FPGA_TEMPERATURE =
  0x83c001F0; /* High 16bit: Temperature near the fpga on the front of the top, Low 16bit: top fpga core temperature*/
constexpr uint32_t RUBY4_BOT_FPGA_TEMPERATURE =
  0x83c00110; /* temperature_on_chip: Bot FPGA temperature, 2 bytes, calculation formula temp=data*503.975/4096-273.15 */

/* Lidar current and voltage status register */

constexpr uint32_t RUBY4_VOL_VCCPINT_CORE_PS = 0x83C00114; /* PS internal core voltage */
constexpr uint32_t RUBY4_VOL_VCCPAUX_PS      = 0x83C00118; /* PS auxiliary voltage */
constexpr uint32_t RUBY4_VOL_VCCODDR         = 0x83C0011C; /* DDR RAM work voltage */
constexpr uint32_t RUBY4_VOL_VCCINT_CORE_PL  = 0x83C00120; /* PL internal core voltage */
constexpr uint32_t RUBY4_VOL_VCCAUX_PL       = 0x83C00124; /* PL auxiliary voltage */
constexpr uint32_t RUBY4_VOL_VCCBRAM_PL      = 0x83C00128; /* PL BRAM work voltage */
constexpr uint32_t RUBY4_VOL_24V_BOT =
  0x83C0012C; /* BOT 24V voltage, 24V voltage calculation formula: Data/4096/0.020833 */
constexpr uint32_t RUBY4_VOL_5V_BOT = 0x83C00130; /* BOT 5V voltage, 5V voltage calculation formula: Data/4096/0.1 */
constexpr uint32_t RUBY4_VOL_BUS =
  0x83C00134; /* Whole machine voltage VBUS, voltage calculation formula: data/4096/0.020833 */
constexpr uint32_t RUBY4_CURRENT_BUS =
  0x83C00138; /* Whole machine current CBUS , current calculation formula: Data/4096/0.90909   */
constexpr uint32_t RUBY4_CURRENT_24V =
  0x83C0013C; /* 24v current, bit23 indicates current direction, bit22:0 indicates current value, unit uA */
constexpr uint32_t RUBY4_MOTOR_COIL_VOL =
  0x83C00140; /* 24v voltage, voltage values, direct representation of voltage values in mV (collected via inA219) */
constexpr uint32_t RUBY4_BOT_CURRENT_1 = 0x83c001F0; /*  */

/* Motor mode register */

constexpr uint32_t RUBY4_TOP_POWER_REG = 0x83C00100; /* TOP board power-on control register. 1: power-on */
constexpr uint32_t RUBY4_MOTOR_SPEED_SET =
  0x83C00210; /* Set the motor speed. Motor speed can be set to 300, 600, 1200rpm/min */
constexpr uint32_t RUBY4_MOTOR_SPEED_REAL   = 0x83C00214; /* Real-time motor speed */
constexpr uint32_t RUBY4_MOTOR_PHASE_SET    = 0x83C00220; /* Motor phase-locked. 0~360 can be set */
constexpr uint32_t RUBY4_MOTOR_PHASE_REAL   = 0x83C00224; /* PPS trigger moment, real-time turn to angle value */
constexpr uint32_t RUBY4_MOTOR_PHASE_STATUS = 0x83C00230;
/*
TOP_COMMUNICATION_STATUS
bit0: 1:The downlink optical communication link is normal, 
      0:The downlink optical communication link is abnormal 
bit1: 1:CDR clock phase lock success, 
      0:CDR clock phase lock failure
*/
constexpr uint32_t RUBY4_TOP_COMMUNICATION_STATUS = 0x83C00414; /* light status */
constexpr uint32_t RUBY4_MOTOR_PHASE_LOCK_STATUS  = 0x83C01090;
constexpr uint32_t RUBY4_MOTOR_REBOOT_FLAG        = 0x83C01088;

constexpr uint32_t RUBY4_SCAN_ANGLE_EN = 0x83C00300; /* Enable equal angle scan. 0x01: Enable equal angle scan */
constexpr uint32_t RUBY4_ANGLE_TEST_EN =
  0x83C00304; /* Analog code wheel angle enable. 0x01: Generate angle output by itself according to the current speed */

constexpr uint32_t RUBY4_ZERO_ANGLE_CORRECTION =
  0x83C00308; /* Zero angle calibration. Software configuration zero angle calibration. Unit 0.01 degrees, range 0-36000, if it is negative-2 degrees, so that the conversion is 358 degrees */
constexpr uint32_t RUBY4_FOV_START =
  0x83C00310; /* Fov start angle. No firing outside the angle. Data is also not uploaded */
constexpr uint32_t RUBY4_FOV_END = 0x83C00314; /* Fov end angle. Support intermediate range across zero. */

constexpr uint32_t RUBY4_CDR_KP_PARA = 0x83C00400; /* Optical Communication CDR parameters1 */
constexpr uint32_t RUBY4_CDR_KI_PARA = 0x83C00404; /* Optical Communication CDR parameters2 */
constexpr uint32_t RUBY4_LIGHT_TEST_EN =
  0x83C00408;                                      /* Optical Communication test mode enable, 0x01: enable, 0x00: off */
constexpr uint32_t RUBY4_RC_DATA_CNT = 0x83C0040C; /* Number of bytes received during optical communication test mode */
constexpr uint32_t RUBY4_RX_DATA_ERR_CNT =
  0x83C00410; /* Number of bytes with error codes during optical communication test mode */

/*
LIGHT_DEBUG
bit[15: 0] : Number of downlink exceptions
bit[23: 16] : The number of times the optical link is flipped when the downlink is abnormal
*/
constexpr uint32_t RUBY4_LIGHT_DEBUG = 0x83C00418;

constexpr uint32_t RUBY4_PULSE_CHANNEL_EN =
  0x83C00500; /* 3 angle pulse output enable, corresponding bit is 1: enable, 0: not enable */
constexpr uint32_t RUBY4_PULSE_START_WIDTH_EN =
  0x83C00504; /* Angle pulse output calculation enable, 1: enable, 0: do not enable */
constexpr uint32_t RUBY4_PULSE_START_ANGLE0_CPU = 0x83C00510; /* Angle pulse start angle 1 */
constexpr uint32_t RUBY4_PULSE_START_ANGLE1_CPU = 0x83C00514; /* Angle pulse start angle 2 */
constexpr uint32_t RUBY4_PULSE_START_ANGLE2_CPU = 0x83C00518; /* Angle pulse start angle 3 */
constexpr uint32_t RUBY4_PULSE_WIDTH0_CPU       = 0x83C00520; /* Angle pulse width 1 */
constexpr uint32_t RUBY4_PULSE_WIDTH1_CPU       = 0x83C00524; /* Angle pulse width 2 */
constexpr uint32_t RUBY4_PULSE_WIDTH2_CPU       = 0x83C00528; /* Angle pulse width 3 */
constexpr uint32_t RUBY4_PULSE_STEP0_CPU        = 0x83C00530; /* Angle pulse step 1 */
constexpr uint32_t RUBY4_PULSE_STEP1_CPU        = 0x83C00534; /* Angle pulse step 2 */
constexpr uint32_t RUBY4_PULSE_STEP2_CPU        = 0x83C00538; /* Angle pulse step 3 */

constexpr uint32_t RUBY4_TIME_SYNC_MODE_REG =
  0x83C03000; /* 100: ptp mode (default), 010: GPS mode, 000: no synchronization */
constexpr uint32_t RUBY4_SYNC_GPS_LOCKED_STATUS_REG = 0x83C03008; /* GPS sync status */
constexpr uint32_t RUBY4_PTP_SYNC_EN = 0x83C03060; /* PTP time sync enable, once a second, effective on rising edge */

constexpr uint32_t RUBY4_PTP_SYNC_STATUS_REG = 0x83C00600; /* Just set this value on APP side, read it from web CGI */

constexpr uint32_t RUBY4_GET_GPS_REQ_UTC  = 0x83C03090; /* get pl Gps time */
constexpr uint32_t RUBY4_SET_GPS_SECH_REG = 0x83C030A0; /* Set Gps time seconds high byte to pl */
constexpr uint32_t RUBY4_SET_GPS_SECL_REG = 0x83C030A4; /* Set Gps time Seconds low byte to pl */
constexpr uint32_t RUBY4_SET_GPS_NSEC_REG = 0x83C030A8; /* Set Gps time nanosecond to pl */

constexpr uint32_t RUBY4_SET_PTP_SECH_REG = 0x83C030B0; /* Set Ptp mode second time high to pl */
constexpr uint32_t RUBY4_SET_PTP_SECL_REG = 0x83C030B4; /* Set Ptp mode second time low to pl */
constexpr uint32_t RUBY4_SET_PTP_NSEC_REG = 0x83C030B8; /* Set Ptp mode nanosecond time to pl */

constexpr uint32_t RUBY4_GET_PL_GPS_TIME_SECH = 0x83C03130; /* Difop Gps mode second time high */
constexpr uint32_t RUBY4_GET_PL_GPS_TIME_SECL = 0x83C03134; /* Difop Gps mode second time low  */
constexpr uint32_t RUBY4_GET_PL_GPS_TIME_NSEC = 0x83C03138; /* Difop Gps mode nanosecond time  */

constexpr uint32_t RUBY4_GET_PL_PTP_TIME_SECH = 0x83C03140; /* Difop Ptp mode second time high */
constexpr uint32_t RUBY4_GET_PL_PTP_TIME_SECL = 0x83C03144; /* Difop Ptp mode second time low  */
constexpr uint32_t RUBY4_GET_PL_PTP_TIME_NSEC = 0x83C03148; /* Difop Ptp mode nanosecond time  */

constexpr uint32_t RUBY4_PC_SYNC_EN     = 0x83C03010; /* Jump edge from 0 to 1, logical take configuration time */
constexpr uint32_t RUBY4_PC_SYNC_YEAR   = 0x83C03020; /* Set date: Year */
constexpr uint32_t RUBY4_PC_SYNC_MONTH  = 0x83C03024; /* Set date: Month */
constexpr uint32_t RUBY4_PC_SYNC_DAY    = 0x83C03028; /* Set date: Day */
constexpr uint32_t RUBY4_PC_SYNC_HOUR   = 0x83C0302C; /* Set date: Hour */
constexpr uint32_t RUBY4_PC_SYNC_MIN    = 0x83C03030; /* Set date: Minutes */
constexpr uint32_t RUBY4_PC_SYNC_SEC    = 0x83C03034; /* Set date: Seconds */
constexpr uint32_t RUBY4_PC_SYNC_MS     = 0x83C03038; /* Set date: Milliseconds */
constexpr uint32_t RUBY4_PC_SYNC_US     = 0x83C0303C; /* Set date: Microsecond */
constexpr uint32_t RUBY4_GPS_SYNC_YEAR  = 0x83C03040; /* Time from GPS module, date: Year */
constexpr uint32_t RUBY4_GPS_SYNC_MONTH = 0x83C03044; /* Time from GPS module, date: Month */
constexpr uint32_t RUBY4_GPS_SYNC_DAY   = 0x83C03048; /* Time from GPS module, date: Day */
constexpr uint32_t RUBY4_GPS_SYNC_HOUR  = 0x83C0304C; /* Time from GPS module, date: Hour */
constexpr uint32_t RUBY4_GPS_SYNC_MIN   = 0x83C03050; /* Time from GPS module, date: Minutes */
constexpr uint32_t RUBY4_GPS_SYNC_SEC   = 0x83C03054; /* Time from GPS module, date: Seconds */

constexpr uint32_t RUBY4_PL_DATE_YEAR  = 0x83C03110;
constexpr uint32_t RUBY4_PL_DATE_MONTH = 0x83C03114;
constexpr uint32_t RUBY4_PL_DATE_DAY   = 0x83C03118;
constexpr uint32_t RUBY4_PL_DATE_HOUR  = 0x83C0311C;
constexpr uint32_t RUBY4_PL_DATE_MIN   = 0x83C03120;
constexpr uint32_t RUBY4_PL_DATE_SEC   = 0x83C03124;
constexpr uint32_t RUBY4_PL_DATE_MS    = 0x83C03128;
constexpr uint32_t RUBY4_PL_DATE_US    = 0x83C0312C;

constexpr uint32_t RUBY4_PPS_RX_NUM   = 0x83C03150;
constexpr uint32_t RUBY4_GPRMC_RX_NUM = 0x83C03154;
constexpr uint32_t RUBY4_PTP_EN_NUM   = 0x83C03158;

constexpr uint32_t RUBY4_DIFOP_PACKETS_GPRMC = 0x83C04000;

constexpr uint32_t RUBY4_PAGE_TABLE_REG = 0x83C09000;

constexpr uint32_t BOT_REG_OPTICAL_TEST               = 0x83c00408; /* optical test, error rate */
constexpr uint32_t BOT_REG_OPTICAL_TEST_UNKOWN        = 0x83c1803c; /* optical test, error rate */
constexpr uint32_t BOT_REG_OPTICAL_UPLOAD_NUM         = 0x83c0042c;
constexpr uint32_t BOT_REG_OPTICAL_UPLOAD_ERROR_NUM   = 0x83c00434;
constexpr uint32_t BOT_REG_OPTICAL_DOWNLOAD_NUM       = 0x83c0043c;
constexpr uint32_t BOT_REG_OPTICAL_DOWNLOAD_ERROR_NUM = 0x83c00438;

/* Top info to Bot*/
constexpr uint32_t RUBY4_TOP_FPGA_VERSION = 0x83C10030; /* Top FPGA version infomation */
constexpr uint32_t RUBY4_WAVE_MODE_REG    = 0x83C1000C; /* Wave mode selection. 0x01:1 wave, 0x02:2 wave */
constexpr uint32_t RUBY4_WAVE_EN = 0x83C10008; /* Wave enable signal. 0x01:enable waveform, 0x00:no enable waveform */

constexpr uint32_t RUBY4_RX_DATA_MODE = 0x83C10000; /* Bot point cloud test mode. 0x01:test mode, 0x00: normal mode */
constexpr uint32_t RUBY4_RX_DATA_VALID_EN =
  0x83C10004; /* Downstream data crc checksum status flag, 0x01:CRC checksum normal, 0: CRC checksum failed */
constexpr uint32_t RUBY4_TOP_FPGA_VERSION_DATE = 0x83C10034; /* Top FPGA build date */
constexpr uint32_t RUBY4_TOP_FPGA_FLASH_STATUS = 0x83C10038; /* Top FPGA flash status */
constexpr uint32_t RUBY4_TOP_FPGA_BOARD_ID     = 0x83C1003C; /* Top FPGA board id */
constexpr uint32_t RUBY4_TOP_FPGA_MAIN_INPUT_VOL =
  0x83C10040;                                          /* Top voltage 3.3v. Calculation formula: (data/4096 * 10) */
constexpr uint32_t RUBY4_TOP_FPGA_2P_5V  = 0x83C10044; /* Top voltage 2.5v. Calculation formula: (data/4096 * 10) */
constexpr uint32_t RUBY4_TOP_FPGA_ANA_5V = 0x83C10048; /* Top simulation 5v. calculation formula: (data/4096 * 10) */
constexpr uint32_t RUBY4_TOP_FPGA_TX_5V  = 0x83C1004C; /* Top launch 5v. calculation formula: (data/4096 * 10) */
constexpr uint32_t RUBY4_TOP_FPGA_RX_5V =
  0x83C10050; /* Top receives negative 5v. calculation formula: ([0.9087 - data/4096]/0.0725) */
constexpr uint32_t RUBY4_TOP_FPGA_HV =
  0x83C10054; /* Top negative high voltage. calculation formula: ([1.6612 - data/4096]/0.6579) */

constexpr uint32_t RUBY4_PKT_CNT =
  0x83C10080; /* The number of point cloud packets received by the bot board, obtained by sending a write register command followed by a read command */
constexpr uint32_t RUBY4_PKT_ERROR_CNT =
  0x83C10084; /* The number of packets of point cloud data packet crc check error received by the bot board, get the way to send the write register command first and then send the read command */
constexpr uint32_t RUBY4_PKT_PL2PS_CNT =
  0x83C1008C; /* The number of point cloud packets transmitted to the ps by pl is obtained by sending a write register command followed by a read command */
constexpr uint32_t RUBY4_TOP_VERSION = 0x83C18000; /* Top Version */

/* Bot Communication */
constexpr uint32_t RUBY4_UPDATE_CTRL = 0x83C20000;
constexpr uint32_t RUBY4_UPDATE_INTERRUPT_ENABLE =
  0x83C20008; /* Top upgrade command return status update interrupt enable: (not used). 0: interrupt not enabled. 1: interrupt enable */
constexpr uint32_t RUBY4_UPDATE_RAM_START_ADDR = 0x83C21000; /* RAM data update start address */

/* 
TOP upgrade command returns status update instructions.
1: Top upgrade command has a new answer status update.
0: TOP command did not receive an answer status 
*/
constexpr uint32_t RUBY4_UPDATE_INTERRUPT_STATUS = 0x83C2000C;
constexpr uint32_t RUBY4_UPDATE_STATUS           = 0x83C20010;
constexpr uint32_t RUBY4_UPDATE_INTERRUPT_CLEAR  = 0x83C20014;
constexpr uint32_t RUBY4_COUNT_REG               = 0x83C20024; /* Counting register */
constexpr uint32_t RUBY4_TOP_AVALON_CRL          = 0x83C20030; /* Top control register */
constexpr uint32_t RUBY4_AVALON_INTERFACE        = 0x83C20034; /* Avalon interface */

/* TOP register offset */
constexpr uint32_t RUBY4_TOP_FPGA_TX_CTRL            = 0x3200;
constexpr uint32_t RUBY4_TOP_BOARD_ID                = 0x3009;
constexpr uint32_t RUBY4_TOP_WAVE_MODE_REG           = 0x200D;
constexpr uint32_t RUBY4_TOP_PERFORMANCE_MODE_REG    = 0x0000;
constexpr uint32_t RUBY4_TOP_NOISE_FILTER_SWITCH_REG = 0x0000;
constexpr uint32_t RUBY4_TOP_APD_H                   = 0x3018;
constexpr uint32_t RUBY4_TOP_APD_L                   = 0x3019;
constexpr uint32_t RUBY4_TOP_UNDER_TEMP_H            = 0x3024;
constexpr uint32_t RUBY4_TOP_UNDER_TEMP_L            = 0x3025;
constexpr uint32_t RUBY4_TOP_ABOVE_TEMP_H            = 0x3026;
constexpr uint32_t RUBY4_TOP_ABOVE_TEMP_L            = 0x3027;
constexpr uint32_t RUBY4_TOP_FPGA_TEMP_H             = 0x300A;
constexpr uint32_t RUBY4_TOP_FPGA_TEMP_L             = 0x300B;
constexpr uint32_t RUBY4_TOP_A_2V5_H                 = 0x3014;
constexpr uint32_t RUBY4_TOP_A_2V5_L                 = 0x3015;
constexpr uint32_t RUBY4_TOP_VBUS_H                  = 0x301A;
constexpr uint32_t RUBY4_TOP_VBUS_L                  = 0x301B;
constexpr uint32_t RUBY4_TOP_TX_5V_H                 = 0x3016;
constexpr uint32_t RUBY4_TOP_TX_5V_L                 = 0x3017;
constexpr uint32_t RUBY4_TOP_A_5V_H                  = 0x3028;
constexpr uint32_t RUBY4_TOP_A_5V_L                  = 0x3029;
constexpr uint32_t RUBY4_TOP_HV_H                    = 0x3012;
constexpr uint32_t RUBY4_TOP_HV_L                    = 0x3013;
constexpr uint32_t RUBY4_TOP_N_5V_H                  = 0x302A;
constexpr uint32_t RUBY4_TOP_N_5V_L                  = 0x302B;
constexpr uint32_t RUBY4_TOP_SHUNT_V_H               = 0x3044;
constexpr uint32_t RUBY4_TOP_SHUNT_V_L               = 0x3045;
constexpr uint32_t RUBY4_TOP_BUS_V_H                 = 0x3046;
constexpr uint32_t RUBY4_TOP_BUS_V_L                 = 0x3047;
constexpr uint32_t RUBY4_TOP_POWER_CONSUMPTION_H     = 0x3048;
constexpr uint32_t RUBY4_TOP_POWER_CONSUMPTION_L     = 0x3049;
constexpr uint32_t RUBY4_TOP_CURRENT_H               = 0x3050;
constexpr uint32_t RUBY4_TOP_CURRENT_L               = 0x3051;

constexpr uint32_t RUBY4_ADDR_OUT_EN_1_8         = 0x1103;
constexpr uint32_t RUBY4_ADDR_OUT_EN_9_16        = 0x1104;
constexpr uint32_t RUBY4_ADDR_OUT_EN_17_24       = 0x1105;
constexpr uint32_t RUBY4_ADDR_OUT_EN_25_32       = 0x1106;
constexpr uint32_t RUBY4_ADDR_OUT_EN_33_40       = 0x1107;
constexpr uint32_t RUBY4_ADDR_OUT_EN_41_48       = 0x1108;
constexpr uint32_t RUBY4_ADDR_OUT_EN_49_56       = 0x1109;
constexpr uint32_t RUBY4_ADDR_OUT_EN_57_64       = 0x110a;
constexpr uint32_t RUBY4_ADDR_OUT_EN_65_72       = 0x110b;
constexpr uint32_t RUBY4_ADDR_OUT_EN_73_80       = 0x110c;
constexpr uint32_t RUBY4_ADDR_OUT_EN_81_88       = 0x110d;
constexpr uint32_t RUBY4_ADDR_OUT_EN_89_96       = 0x110e;
constexpr uint32_t RUBY4_ADDR_OUT_EN_97_104      = 0x110f;
constexpr uint32_t RUBY4_ADDR_OUT_EN_105_112     = 0x1110;
constexpr uint32_t RUBY4_ADDR_OUT_EN_113_120     = 0x1111;
constexpr uint32_t RUBY4_ADDR_OUT_EN_121_128     = 0x1112;
constexpr uint32_t RUBY4_ADDR_CHARGE_FIX_EN      = 0X1208;
constexpr uint32_t RUBY4_ADDR_CHARGE_FIX_VALUE   = 0X1209;
constexpr uint32_t RUBY4_VGA_ADDR_FIX_EN         = 0X120A;
constexpr uint32_t RUBY4_VGA_ADDR_FIX_VALUE      = 0X120B;
constexpr uint32_t RUBY4_ADDR_GAIN_MIN           = 0x1221;
constexpr uint32_t RUBY4_ADDR_GAIN_MAX           = 0x1222;
constexpr uint32_t RUBY4_ADDR_CHNL_SWH           = 0X2603;
constexpr uint32_t RUBY4_ADDR_MODE_SWH           = 0X2604;
constexpr uint32_t RUBY4_ADDR_ADC_START_POINT_H  = 0X2605;
constexpr uint32_t RUBY4_ADDR_ADC_START_POINT_L  = 0X2606;
constexpr uint32_t RUBY4_ADDR_CYCLE_CNT          = 0X2607;
constexpr uint32_t RUBY4_ADDR_PWR_DEBUG_SEL      = 0X260A;
constexpr uint32_t RUBY4_ADDR_DATA_COLLECT_SWH   = 0X2602;
constexpr uint32_t RUBY4_ADDR_DATA_LOCK          = 0X2601;
constexpr uint32_t RUBY4_ADDR_CH_SEL             = 0X2600;
constexpr uint32_t RUBY4_ADDR_ECHO_AMP           = 0X2610;
constexpr uint32_t RUBY4_ADDR_SATU_NUM           = 0X2611;
constexpr uint32_t RUBY4_ADDR_AREA_GDI_H         = 0X2612;
constexpr uint32_t RUBY4_ADDR_AREA_GDI_L         = 0X2613;
constexpr uint32_t RUBY4_ADDR_DIST_GDI_H         = 0X2614;
constexpr uint32_t RUBY4_ADDR_DIST_GDI_L         = 0X2615;
constexpr uint32_t RUBY4_ADDR_SATU_CMP_EN        = 0x2231;
constexpr uint32_t RUBY4_ADDR_SATU_RAM_WR_EN     = 0x2232;
constexpr uint32_t RUBY4_ADDR_SATU_RAM_WR_DATA   = 0x2233;
constexpr uint32_t RUBY4_ADDR_SATU_RAM_WR_ADDR_H = 0x2234;
constexpr uint32_t RUBY4_ADDR_SATU_RAM_WR_ADDR_L = 0x2235;
constexpr uint32_t RUBY4_ADDR_SATU_RAM_RD_DATA   = 0x2236;
constexpr uint32_t RUBY4_ADDR_REF_DAS_MUX        = 0X2100;
constexpr uint32_t RUBY4_ADDR_STA_CMP_SWH        = 0x2200;

}  // namespace ruby

}  // namespace lidar
}  // namespace robosense

#endif  //RUBY_REGISTER_ADDRESS_H
