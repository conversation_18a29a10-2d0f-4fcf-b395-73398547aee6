﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef AIRY_REGISTER_ADDRESS_H
#define AIRY_REGISTER_ADDRESS_H

#include <cstdint>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
namespace airy
{

constexpr uint32_t CHANNEL_SIZE = 96;

constexpr uint32_t REG_MEM_VERSION     = 0x83c00000;
constexpr uint32_t REG_REAL_SPEED      = 0x83c00014;
constexpr uint32_t REG_SET_MOTOR_SPEED = 0x83c01000;

// 底板解包
constexpr uint32_t REG_TEM_N3V3 = 0x83c18010;
constexpr uint32_t REG_TX_3V    = 0x83c18014;

// 顶板top
constexpr uint32_t REG_SENSOR_SPI_CTRL   = 0x3000;
constexpr uint32_t REG_SENSOR_SPI_ADDR_0 = 0x3001;
constexpr uint32_t REG_SENSOR_SPI_ADDR_1 = 0x3002;
constexpr uint32_t REG_SENSOR_SPI_WDATA  = 0x3003;
constexpr uint32_t REG_SENSOR_SPI_RDATA  = 0x3006;
constexpr uint32_t REG_PWM_NHV_VALUE     = 0x3020;

constexpr uint32_t REG_TX_CHN_EN_ALL   = 0x1050;
constexpr uint32_t REG_TX_CHN_EN       = 0x1051;
constexpr uint32_t REG_TX_CHN_EN_95_88 = REG_TX_CHN_EN;
constexpr uint32_t REG_TX_CHN_EN_87_80 = 0x1052;
constexpr uint32_t REG_TX_CHN_EN_79_72 = 0x1053;
constexpr uint32_t REG_TX_CHN_EN_71_64 = 0x1054;
constexpr uint32_t REG_TX_CHN_EN_63_56 = 0x1055;
constexpr uint32_t REG_TX_CHN_EN_55_48 = 0x1056;
constexpr uint32_t REG_TX_CHN_EN_47_40 = 0x1057;
constexpr uint32_t REG_TX_CHN_EN_39_32 = 0x1058;
constexpr uint32_t REG_TX_CHN_EN_31_24 = 0x1059;
constexpr uint32_t REG_TX_CHN_EN_23_16 = 0x105a;
constexpr uint32_t REG_TX_CHN_EN_15_8  = 0x105b;
constexpr uint32_t REG_TX_CHN_EN_7_0   = 0x105c;

constexpr uint32_t REG_T_TX_CHARGE = 0x1066;

constexpr uint32_t REG_CHG_GEAR_0 = 0x2090;

}  // namespace airy

}  // namespace lidar
}  // namespace robosense

#endif  // AIRY_REGISTER_ADDRESS_H
