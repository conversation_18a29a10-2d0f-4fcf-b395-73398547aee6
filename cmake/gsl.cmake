﻿set(CMAKE_FIND_LIBRARY_SUFFIXES_TEMP ${CMAKE_FIND_LIBRARY_SUFFIXES})
set(CMAKE_FIND_LIBRARY_SUFFIXES ".a")
find_package(GSL 2.8 REQUIRED)
get_target_property(GSL_LOC GSL::gsl IMPORTED_LOCATION)
get_target_property(GSLCBLAS_LOC GSL::gslcblas IMPORTED_LOCATION)
message(STATUS "GSL path: ${GSL_LOC}")
message(STATUS "GSL gslcblas path: ${GSLCBLAS_LOC}")
set(CMAKE_FIND_LIBRARY_SUFFIXES ${CMAKE_FIND_LIBRARY_SUFFIXES_TEMP})
message(STATUS "GSL version: ${GSL_VERSION}")
