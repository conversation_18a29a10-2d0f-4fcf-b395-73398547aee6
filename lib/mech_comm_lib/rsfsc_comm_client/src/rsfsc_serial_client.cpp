#include "rsfsc_comm_client/rsfsc_serial_client.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <boost/asio/steady_timer.hpp>
#include <future>

namespace robosense
{
namespace lidar
{

RsfscSerialClient::RsfscSerialClient(const std::string& _port, uint32_t _baud_rate)
{
  CommClientConfig config;
  config.serial_config.port_name          = _port;
  config.serial_config.baudrate           = _baud_rate;
  config.serial_config.connect_timeout_ms = 35000;
  setConfig(config);
}
RsfscSerialClient::RsfscSerialClient(const SerialClientConfig& _config)
{
  CommClientConfig config;
  config.serial_config = _config;
  setConfig(config);
}

RsfscSerialClient::RsfscSerialClient() = default;

RsfscSerialClient::~RsfscSerialClient() { disconnect(); }

bool RsfscSerialClient::connect()
{
  auto config    = getConfig();
  auto port_name = config.serial_config.port_name;
  auto baud_rate = config.serial_config.baudrate;
  msg_header_    = fmt::format("RsfscSerialClient [{}:{}] :: ", port_name, baud_rate);
  try
  {
    startIoContext();
    serial_port_ = std::make_unique<boost::asio::serial_port>(ioContext());
    serial_port_->open(port_name);

    serial_port_->set_option(boost::asio::serial_port_base::baud_rate(baud_rate));
    serial_port_->set_option(boost::asio::serial_port_base::character_size(8));
    serial_port_->set_option(boost::asio::serial_port_base::stop_bits(boost::asio::serial_port_base::stop_bits::one));
    serial_port_->set_option(boost::asio::serial_port_base::parity(boost::asio::serial_port_base::parity::none));
    serial_port_->set_option(
      boost::asio::serial_port_base::flow_control(boost::asio::serial_port_base::flow_control::none));

    is_connected_ = true;
    startIoContext();
    return true;
  }
  catch (const std::exception& e)
  {
    serial_port_.reset();
    is_connected_ = false;
    LOG_INDEX_ERROR("{} 打开连接失败，错误信息: {}", msg_header_, e.what());
    return false;
  }
}

bool RsfscSerialClient::disconnect()
{
  abort();
  if (is_connected_)
  {
    boost::system::error_code error_code;
    // 尝试取消操作
    auto error_code_ret = serial_port_->cancel(error_code);
    if (error_code || error_code_ret)
    {
      LOG_INDEX_ERROR(msg_header_ + " -> cancel failed: " + error_code.message());
    }
    error_code_ret = serial_port_->close(error_code);
    if (error_code || error_code_ret)
    {
      LOG_INDEX_ERROR(msg_header_ + " -> close failed: " + error_code.message());
    }
    stopIoContext();
    is_connected_ = false;
  }
  return true;
}

bool RsfscSerialClient::isConnected() const { return is_connected_ && serial_port_ && serial_port_->is_open(); }

std::size_t RsfscSerialClient::write(const std::vector<uint8_t>& _data)
{
  if (!isConnected())
  {
    LOG_INDEX_ERROR(msg_header_ + "写入失败，连接未打开");
    return 0;
  }
  fmt::string_view omit_str = _data.size() > 100 ? " ..." : "";
  try
  {
    auto bytes_written = serial_port_->write_some(boost::asio::buffer(_data));
    LOG_INDEX_DEBUG("{}write data{}: {:02x}{}", msg_header_, bytes_written == _data.size() ? "" : " failed",
                    _data.size() > 100 ? fmt::join(_data.begin(), _data.begin() + 100, " ") : fmt::join(_data, " "),
                    omit_str);
    return bytes_written;
  }
  catch (const std::exception& e)
  {
    LOG_INDEX_ERROR(msg_header_ + "写入失败，错误信息：" + e.what());
    return 0;
  }
}

std::size_t RsfscSerialClient::readSome(std::vector<uint8_t>& _data)
{
  if (!isConnected())
  {
    return 0;
  }

  try
  {
    return serial_port_->read_some(boost::asio::buffer(_data));
  }
  catch (const std::exception&)
  {
    return 0;
  }
}

std::vector<uint8_t> RsfscSerialClient::readSomeTimeout(uint32_t _timeout_ms) { return {}; }

void RsfscSerialClient::asyncWrite(const std::vector<uint8_t>& _data, WriteHandler _handler)
{
  if (!isConnected())
  {
    _handler(boost::asio::error::not_connected, 0);
    return;
  }

  serial_port_->async_write_some(boost::asio::buffer(_data), _handler);
}

void RsfscSerialClient::asyncRead(std::vector<uint8_t>& _data, ReadHandler _handler)
{
  if (!isConnected())
  {
    _handler(boost::asio::error::not_connected, 0);
    return;
  }

  serial_port_->async_read_some(boost::asio::buffer(_data), _handler);
}

void RsfscSerialClient::abort() { RsfscCommClient::abort(); }

bool RsfscSerialClient::isAborted() const { return RsfscCommClient::isAborted(); }

void RsfscSerialClient::resetAbort() { RsfscCommClient::resetAbort(); }

std::vector<uint8_t> RsfscSerialClient::request(const std::vector<uint8_t>& _request, uint32_t _timeout_ms)
{
  if (isAborted())
  {
    LOG_INDEX_ERROR(msg_header_ + "请求失败，操作已被中止 (pre-check)");
    return {};
  }

  if (write(_request) <= 0U)
  {
    LOG_INDEX_ERROR(msg_header_ + "请求失败，写入失败");
    return {};
  }

  // Promise to signal completion or error from async operations
  std::promise<std::vector<uint8_t>> promise_for_response;
  std::future<std::vector<uint8_t>> future_for_response = promise_for_response.get_future();

  boost::asio::steady_timer timer(ioContext());
  timer.expires_after(std::chrono::milliseconds(_timeout_ms));

  // Use a shared_ptr for the response buffer to manage its lifetime across lambdas
  // And a flag to ensure promise is set only once.
  auto response_buffer_ptr     = std::make_shared<std::vector<uint8_t>>(2048);
  auto operation_completed_ptr = std::make_shared<std::atomic<bool>>(false);

  // Asynchronous read operation
  serial_port_->async_read_some(boost::asio::buffer(*response_buffer_ptr),
                                [&timer, response_buffer_ptr, &promise_for_response, operation_completed_ptr, this](
                                  const boost::system::error_code& _error_code, std::size_t _bytes_transferred) {
                                  // Attempt to set the promise only if no operation (timer or read) has completed yet
                                  if (operation_completed_ptr->exchange(true))
                                  {          // exchange returns previous value
                                    return;  // Already handled by timer or another callback
                                  }

                                  timer.cancel();  // Cancel the timer as read operation finished (or failed)

                                  if (_error_code == boost::asio::error::operation_aborted)
                                  {
                                    LOG_INDEX_ERROR(msg_header_ + "读取操作被取消 (可能由超时引起)");
                                    promise_for_response.set_value({});  // Or set_exception if preferred
                                  }
                                  else if (_error_code)
                                  {
                                    LOG_INDEX_ERROR(msg_header_ + "读取失败: " + _error_code.message());
                                    promise_for_response.set_value({});
                                  }
                                  else
                                  {
                                    response_buffer_ptr->resize(_bytes_transferred);
                                    promise_for_response.set_value(*response_buffer_ptr);
                                  }
                                });

  // Asynchronous wait on timer
  timer.async_wait(
    [this, &promise_for_response, operation_completed_ptr](const boost::system::error_code& _error_code) {
      if (operation_completed_ptr->exchange(true))
      {
        return;  // Already handled by read callback
      }

      // Timer expired or was cancelled.
      // If _error_code is operation_aborted, it means timer was cancelled by the read completing.
      if (_error_code == boost::asio::error::operation_aborted)
      {
        // This is expected if read completed first and cancelled the timer.
        // The read handler would have set the promise.
        return;
      }

      // If _error_code is set (and not operation_aborted), it's some other timer error.
      if (_error_code)
      {
        LOG_INDEX_ERROR(msg_header_ + "定时器错误: " + _error_code.message());
        // Try to cancel socket operations, though read might be stuck elsewhere
        boost::system::error_code ignored_cancel_ec;
        serial_port_->cancel(ignored_cancel_ec);
        promise_for_response.set_value({});
        return;
      }

      // Timer expired (no error, not aborted) - this is a genuine timeout.
      LOG_INDEX_ERROR(msg_header_ + "请求超时");
      boost::system::error_code ignored_ec;
      serial_port_->cancel(ignored_ec);  // Cancel the pending read operation
      promise_for_response.set_value({});
    });

  try
  {
    std::vector<uint8_t> result_from_future = future_for_response.get();
    if (result_from_future.empty() && !isAborted())
    {  // Additional check if empty means error
       // Log was already done in callbacks
      LOG_INDEX_ERROR(msg_header_ + "请求失败，响应为空");
      return {};
    }
    if (isAborted() && result_from_future.empty())
    {
      LOG_INDEX_ERROR(msg_header_ + "请求在等待期间被中止");
      return {};
    }
    LOG_INDEX_DEBUG("{}receive data: {:02x}", msg_header_, fmt::join(result_from_future, " "));
    return result_from_future;
  }
  catch (const std::future_error& e)
  {
    // This might happen if io_context::run() returns before promise is set
    // (e.g. io_context::stop() called from elsewhere, or unhandled exception in a handler)
    LOG_INDEX_ERROR(msg_header_ + "请求失败，Future异常: " + e.what());
    if (isAborted())
    {
      LOG_INDEX_ERROR(msg_header_ + "操作被中止导致Future异常");
    }
    // Ensure socket is cancelled if we land here unexpectedly
    boost::system::error_code ignored_ec;
    auto error_code = serial_port_->cancel(ignored_ec);
    LOG_INDEX_ERROR("{} cancel socket: {}", msg_header_, error_code.message());
    return {};
  }

  return {};
}

}  // namespace lidar
}  // namespace robosense
