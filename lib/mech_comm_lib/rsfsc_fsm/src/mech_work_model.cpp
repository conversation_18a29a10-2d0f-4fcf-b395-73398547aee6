﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "rsfsc_fsm/mech/mech_work_model.h"
#include "rsfsc_fsm/mech/lidar_manager.h"
#include "rsfsc_lib/include/widget_log_setting.h"
#include "rsfsc_log/rsfsc_log_macro.h"

#include <QFileInfo>
#include <fstream>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

MechWorkModel::~MechWorkModel() = default;

int MechWorkModel::getLidarIndex() { return getLidarManager()->getLidarInfo()->getLidarIndex(); }

void MechWorkModel::abort() { WorkModel::abort(); }
bool MechWorkModel::isAbort() { return WorkModel::isAbort(); }

LimitInfo MechWorkModel::getLimitInfo(const std::string& _name)
{
  auto limit_csv_utils = getLimitCsvUtils();
  if (limit_csv_utils == nullptr)
  {
    LOG_INDEX_ERROR("limit_csv_utils_ptr_ is nullptr，请查看limit文件是否被正确加载");
    return {};
  }
  return limit_csv_utils->getLimitInfo(_name);
}
bool MechWorkModel::addMeasureMessage(const std::string& _name, const bool _data)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }
  return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, static_cast<int>(_data),
                                                  rsfsc_lib::MEASURE_DATA_TYPE_INT);
}
bool MechWorkModel::addMeasureMessage(const std::string& _name, const double _data)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }
  return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data,
                                                  rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
}
bool MechWorkModel::addMeasureMessage(const std::string& _name, const float _data)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }
  return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data,
                                                  rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
}
bool MechWorkModel::addMeasureMessage(const std::string& _name, const int _data)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }
  return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data, rsfsc_lib::MEASURE_DATA_TYPE_INT);
}
bool MechWorkModel::addMeasureMessage(const std::string& _name, const uint32_t _data)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }
  return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data, rsfsc_lib::MEASURE_DATA_TYPE_HEX);
}
bool MechWorkModel::addMeasureMessage(const std::string& _name, const std::string& _data)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }
  return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data);
}

// bool MechWorkModel::addMeasureMessage(const std::string& _name, const QVariant& _value)
// {
//   auto limit_info = getLimitInfo(_name);
//   if (!limit_info.is_ok)
//   {
//     LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
//     return false;
//   }

//   if (!_value.isValid())
//   {
//     LOG_INDEX_ERROR("QVariant value key 无效: {}", _name);
//     return false;
//   }

//   switch (static_cast<QMetaType::Type>(_value.type()))
//   {
//   case QMetaType::Int:
//   {
//     return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _value.toInt(),
//                                                     rsfsc_lib::MEASURE_DATA_TYPE_INT);
//     break;
//   }
//   case QMetaType::UInt:
//   {
//     return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _value.toUInt(),
//                                                     rsfsc_lib::MEASURE_DATA_TYPE_HEX);
//     break;
//   }
//   case QMetaType::Double:
//   case QMetaType::Float:
//   {
//     return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _value.toDouble(),
//                                                     rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
//     break;
//   }
//   case QMetaType::QString:
//   {
//     return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _value.toString().toStdString());
//     break;
//   }
//   default:
//   {
//     LOG_INDEX_ERROR("未知的数据类型name: {}, type name: {},", limit_info.getName(), _value.typeName());
//     return false;
//   }
//   break;
//   }

//   LOG_INDEX_ERROR("QVariant value key 无效: {}", _name);
//   return false;
// }

bool MechWorkModel::addMeasureMessage(const LimitInfo& _limit_info,
                                      std::variant<float, double, int, uint32_t, std::string>& _value)
{
  if (!_limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 无效");
    return false;
  }

  return std::visit(
    [this, &_limit_info](auto&& arg) {
      using T = std::decay_t<decltype(arg)>;
      if constexpr (std::is_same_v<T, float> || std::is_same_v<T, double>)
      {
        return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), _limit_info, arg,
                                                        rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
      }
      else if constexpr (std::is_same_v<T, int>)
      {
        return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), _limit_info, arg,
                                                        rsfsc_lib::MEASURE_DATA_TYPE_INT);
      }
      else if constexpr (std::is_same_v<T, uint32_t>)
      {
        return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), _limit_info, arg,
                                                        rsfsc_lib::MEASURE_DATA_TYPE_HEX);
      }
      else if constexpr (std::is_same_v<T, std::string>)
      {
        return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), _limit_info, arg);
      }
      return false;
    },
    _value);
}
std::string MechWorkModel::varToStr(const std::variant<float, double, int, uint32_t, std::string>& _value)
{
  return std::visit([](auto&& arg) { return fmt::format("{}", arg); }, _value);
}

bool MechWorkModel::readRegDataByKey(const QString& _key, uint32_t& _data, const int _timeout)
{
  if (getRegCsvUtils() == nullptr)
  {
    LOG_INDEX_ERROR("获取reg csv解析器失败, 读取{}寄存器失败", _key);
    return false;
  }
  auto reg_info = getRegCsvUtils()->getRegisterInfo(_key.toStdString());
  if (!reg_info.is_ok)
  {
    LOG_INDEX_ERROR("获取{}寄存器信息失败", _key);
    return false;
  }

  if (_key.contains("cmd"))
  {
    if (!getLidarManager()->readCmd(reg_info.address, _data, _timeout))
    {
      LOG_INDEX_ERROR("[{0:}:{1:#x}] cmd读取失败", _key, reg_info.address);
      return false;
    }
  }

  // return getLidarManager()->readRegData(reg_info.address, _data, _timeout);
  if (!getLidarManager()->readRegData(reg_info.address, _data, _timeout))
  {
    LOG_INDEX_ERROR("[{:}:{:#x}] 读取失败", _key, reg_info.address);
    return false;
  }

  LOG_INDEX_INFO("[{:}:{:#x}] 读取成功, 数据为{:#x}", _key, reg_info.address, _data);
  return true;
}
bool MechWorkModel::readTopRegDataByKey(const QString& _key,
                                        uint32_t& _data,
                                        const uint32_t _byte_size,
                                        const int _timeout)
{
  if (getRegCsvUtils() == nullptr)
  {
    LOG_INDEX_ERROR("获取reg csv解析器失败, 读取{}寄存器失败", _key);
    return false;
  }
  auto reg_info = getRegCsvUtils()->getRegisterInfo(_key.toStdString());
  if (!reg_info.is_ok)
  {
    LOG_INDEX_ERROR("获取{}寄存器信息失败", _key);
    return false;
  }

  if (_key.contains("cmd"))
  {
    if (!getLidarManager()->readCmd(reg_info.address, _data, _timeout))
    {
      LOG_INDEX_ERROR("[{0:}:{1:#x}] cmd读取失败", _key, reg_info.address);
      return false;
    }
  }

  std::vector<uint32_t> data;
  if (!getLidarManager()->readRegData(reg_info.address, _byte_size, data, _timeout))
  {
    LOG_INDEX_ERROR("[{:}*{}:{:#x}] 读取失败", _key, _byte_size, reg_info.address);
    return false;
  }

  _data = 0;

  for (auto& item : data)
  {
    _data = (_data << 8U) | (item & 0xffU);
  }

  LOG_INDEX_INFO("[{:}*{}:{:#x}] 读取成功, 数据为{:#x}", _key, _byte_size, reg_info.address, _data);
  return true;
}
bool MechWorkModel::writeRegDataByKey(const QString& _key, const uint32_t _data)
{
  if (getRegCsvUtils() == nullptr)
  {
    LOG_INDEX_ERROR("获取reg csv解析器失败, 写入{}寄存器失败", _key);
    return false;
  }
  auto reg_info = getRegCsvUtils()->getRegisterInfo(_key.toStdString());
  if (!reg_info.is_ok)
  {
    LOG_INDEX_ERROR("获取{}寄存器信息失败", _key);
    return false;
  }
  if (!getLidarManager()->writeRegData(static_cast<uint32_t>(reg_info.address), _data))
  {
    LOG_INDEX_ERROR("[{:}:{:#x}] 写入{:#x}失败", _key, reg_info.address, _data);
    return false;
  }
  LOG_INDEX_INFO("[{:}:{:#x}] 写入成功, 数据为{:#x}", _key, reg_info.address, _data);
  return true;
}

bool MechWorkModel::writeRegDataByKeyWithVer(const QString& _key, const uint32_t _data)
{
  if (getRegCsvUtils() == nullptr)
  {
    LOG_INDEX_ERROR("获取reg csv解析器失败, 写入{}寄存器失败", _key);
    return false;
  }
  auto reg_info = getRegCsvUtils()->getRegisterInfo(_key.toStdString());
  if (!reg_info.is_ok)
  {
    LOG_INDEX_ERROR("获取{}寄存器信息失败", _key);
    return false;
  }
  if (!getLidarManager()->writeRegDataWithVer(static_cast<uint32_t>(reg_info.address), _data, reg_info.name.c_str()))
  {
    LOG_INDEX_ERROR("[{:}:{:#x}] 写入{:#x}失败", _key, reg_info.address, _data);
    return false;
  }
  LOG_INDEX_INFO("[{:}:{:#x}] 写入成功, 数据为{:#x}", _key, reg_info.address, _data);
  return true;
}

bool MechWorkModel::writeCsvData(const QString& _key)
{
  if (getRegCsvUtils() == nullptr)
  {
    LOG_INDEX_ERROR("获取reg csv解析器失败, 写入{}寄存器失败", _key);
    return false;
  }
  auto reg_info_map                 = getRegCsvUtils()->getSelectIndexPropertyRegisterInfo(_key.toStdString());
  bool overall_verification_success = true;

  for (auto& [reg_name, reg_info] : reg_info_map)
  {
    if (reg_name.find("cmd") != std::string::npos)
    {
      // CMD 寄存器使用原有的 writeCmd 方式
      int data_size = 4;
      if (reg_info.extra_str_info.at(0) != "")
      {
        data_size = std::stoi(reg_info.extra_str_info.at(0));
      }
      std::vector<uint8_t> data(data_size, 0);
      for (size_t i = 0; i < data.size(); i++)
      {
        data[i] = static_cast<uint8_t>(static_cast<uint32_t>(reg_info.value_at_calibration) >> (i * 8U));
      }

      bool write_success = getLidarManager()->writeCmd(reg_info.address, data);
      if (!write_success)
      {
        LOG_INDEX_ERROR("[{0:}:{1:#x}]写入[{2:}:{2:#x}]失败", reg_name, reg_info.address,
                        reg_info.value_at_calibration);
        // 记录写入失败的情况
        getLidarManager()->writeVerificationDataToFile(QString::fromStdString(reg_name), reg_info.address,
                                                       static_cast<uint32_t>(reg_info.value_at_calibration), 0U, false);
        return false;
      }
      LOG_INDEX_INFO("[{0:}:{1:#x}]写入[{2:}:{2:#x}]成功", reg_name, reg_info.address, reg_info.value_at_calibration);

      // CMD 寄存器跳过回读校验，不记录到文件
      continue;
    }

    // 普通寄存器使用 writeRegDataWithVer 进行写入和校验
    bool write_and_verify_success = getLidarManager()->writeRegDataWithVer(
      reg_info.address, static_cast<uint32_t>(reg_info.value_at_calibration), QString::fromStdString(reg_name));

    if (!write_and_verify_success)
    {
      LOG_INDEX_ERROR("[{0:}:{1:#x}]写入或校验失败", reg_name, reg_info.address);
      overall_verification_success = false;
    }
  }

  if (!overall_verification_success)
  {
    LOG_INDEX_ERROR("CSV数据写入回读校验存在失败项");
    return false;
  }

  return true;
}
bool MechWorkModel::writeCsvDataAfterCalib(const QString& _key)
{
  if (getRegCsvUtils() == nullptr)
  {
    LOG_INDEX_ERROR("获取reg csv解析器失败, 写入{}寄存器失败", _key);
    return false;
  }
  auto reg_info_map = getRegCsvUtils()->getSelectIndexPropertyRegisterInfo(_key.toStdString());

  for (auto& [reg_name, reg_info] : reg_info_map)
  {
    if (!getLidarManager()->writeRegData(reg_info.address, reg_info.value_after_calibration))
    {
      LOG_ERROR("[{0:}:{1:#x}]写入[{2:}:{2:#x}]失败", reg_name, reg_info.address, reg_info.value_after_calibration);
      return false;
    }
    LOG_INFO("[{0:}:{1:#x}]写入[{2:}:{2:#x}]成功", reg_name, reg_info.address, reg_info.value_after_calibration);
  }
  return true;
}

bool MechWorkModel::startUpOpticalErrorTest()
{
  // light_uptst_en set 1
  for (int i = 0; i < 3; ++i)
  {
    if (writeRegDataByKeyWithVer("light_uptst_en", 1))
    {
      return true;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(20));
  }
  return false;
}
bool MechWorkModel::stopUpOpticalErrorTest()
{
  // light_uptst_en set 0
  for (int i = 0; i < 3; ++i)
  {
    if (writeRegDataByKeyWithVer("light_uptst_en", 0))
    {
      return true;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(20));
  }
  return false;
}
bool MechWorkModel::getUpOpticalError(uint32_t& _error, uint32_t& _total)
{
  _error = 0;
  _total = 0;
  if (!readTopRegDataByKey("light_uptst_error", _error, 4) || !readTopRegDataByKey("light_uptst_total", _total, 4))
  {
    optical_up_error_rate_ = NAN;
    return false;
  }

  if (_total == 0)
  {
    optical_up_error_rate_ = NAN;
  }
  else
  {
    optical_up_error_rate_ = static_cast<double>(_error) / _total;
  }

  _error = static_cast<int>(_error);
  _total = static_cast<int>(_total);
  return true;
}
bool MechWorkModel::startDownOpticalErrorTest()
{
  // light_dwntst_en set 1
  for (int i = 0; i < 3; ++i)
  {
    if (writeRegDataByKey("light_dwntst_en", 1))
    {
      return true;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(20));
  }
  return false;
}
bool MechWorkModel::stopDownOpticalErrorTest()
{
  // light_dwntst_en set 0
  for (int i = 0; i < 3; ++i)
  {
    if (writeRegDataByKey("light_dwntst_en", 0))
    {
      return true;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(20));
  }
  return false;
}
bool MechWorkModel::getDownOpticalError(uint32_t& _error, uint32_t& _total)
{
  _error = 0;
  _total = 0;
  if (!readRegDataByKey("light_dwntst_error", _error) || !readRegDataByKey("light_dwntst_total", _total))
  {
    optical_down_error_rate_ = NAN;
    return false;
  }

  if (_total == 0)
  {
    optical_down_error_rate_ = NAN;
  }
  else
  {
    optical_down_error_rate_ = static_cast<double>(_error) / _total;
  }

  return true;
}
void MechWorkModel::getOpticalErrorRate(double& _up_error_rate, double& _down_error_rate) const
{
  _up_error_rate   = optical_up_error_rate_;
  _down_error_rate = optical_down_error_rate_;
}

bool MechWorkModel::writeBitFileToLidar(const QString& _bit_file_path, const bool _is_verify)
{
  if (_bit_file_path.isEmpty())
  {
    LOG_INDEX_ERROR("bit文件路径为空");
    return false;
  }

  // 读取文件内容
  std::ifstream file(_bit_file_path.toStdString(), std::ios::binary);
  if (!file.is_open())
  {
    LOG_INDEX_ERROR("打开文件失败，文件路径：{}", _bit_file_path.toStdString());
    return false;
  }

  // 获取文件大小
  file.seekg(0, std::ios::end);
  std::streamsize file_size = file.tellg();
  file.seekg(0, std::ios::beg);

  if (file_size <= 0)
  {
    LOG_INDEX_ERROR("文件大小无效，文件路径：{}", _bit_file_path.toStdString());
    file.close();
    return false;
  }

  // 读取文件内容到vector
  std::vector<uint8_t> file_data(file_size);
  if (!file.read(reinterpret_cast<char*>(file_data.data()), file_size))
  {
    LOG_INDEX_ERROR("读取文件失败，文件路径：{}", _bit_file_path.toStdString());
    file.close();
    return false;
  }
  file.close();

  // 根据_is_verify参数选择写入方式
  const uint32_t flash_addr = 0xF00000;
  bool write_result         = false;

  if (_is_verify)
  {
    // 使用带校验的写入方式
    QString file_name = QFileInfo(_bit_file_path).baseName();
    write_result      = getLidarManager()->writeTopFlashWithVer(file_name, file_data, flash_addr);
    if (!write_result)
    {
      LOG_INDEX_ERROR("写入bit文件到雷达失败（带校验），文件路径：{}", _bit_file_path.toStdString());
    }
    else
    {
      LOG_INDEX_INFO("写入bit文件到雷达成功（带校验），文件路径：{}", _bit_file_path.toStdString());
    }
  }
  else
  {
    // 使用普通写入方式
    write_result = getLidarManager()->writeTopFlash(file_data, flash_addr);
    if (!write_result)
    {
      LOG_INDEX_ERROR("写入bit文件到雷达失败，文件路径：{}", _bit_file_path.toStdString());
    }
    else
    {
      LOG_INDEX_INFO("写入bit文件到雷达成功，文件路径：{}", _bit_file_path.toStdString());
    }
  }

  return write_result;
}

}  // namespace lidar
}  // namespace robosense