/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "work_model/connectivity_work_model.h"
#include "app_event.h"
#include "lidar_ctl.h"
#include "mech_comm_func/mes.h"
#include "mech_communication/protocol/data_struct/airy_pld.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "mech_udp.h"
#include "rsfsc_log/rsfsc_log_macro.h"

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

ConnectivityWorkModel::ConnectivityWorkModel(rsfsc_lib::WidgetLidarInfo* _lidar_info) : MechWorkModel(_lidar_info)
{
  auto limit_csv = app()->getCsvUtils("airy_limit");
  auto reg_csv   = app()->getCsvUtils("airy_reg");
  if (limit_csv == nullptr)
  {
    LOG_INDEX_ERROR("获取limit csv解析器失败");
    return;
  }
  if (reg_csv == nullptr)
  {
    LOG_INDEX_ERROR("获取reg csv解析器失败");
    return;
  }
  setLimitCsvUtils(limit_csv);
  setRegCsvUtils(reg_csv);
}
// AgingWorkModel::~AgingWorkModel() {}

rsfsc_lib::WidgetLogSetting* ConnectivityWorkModel::getWidgetLogSetting() { return app()->getWidgetMes(); }

std::shared_ptr<FiniteStateMachine<ConnectivityWorkModel>> ConnectivityWorkModel::createFsm()
{
  auto fsm = std::make_shared<FiniteStateMachine<ConnectivityWorkModel>>();
  fsm->setIndex(getLidarIndex());
  fsm->registerHandler(std::make_shared<CheckMes>());
  fsm->registerHandler(std::make_shared<WriteSn>());
  fsm->registerHandler(std::make_shared<ConnectLidar>());
  fsm->registerHandler(std::make_shared<InitLidar>());
  fsm->registerHandler(std::make_shared<CheckDifop>());
  fsm->registerHandler(std::make_shared<CheckMsop>());
  fsm->registerHandler(std::make_shared<CheckLightError>());
  fsm->registerHandler(std::make_shared<SuccessHandler>());
  fsm->registerHandler(std::make_shared<FailHandler>());
  fsm->registerHandler(std::make_shared<AbortHandler>());
  fsm->registerHandler(std::make_shared<FinalHandler>());
  return fsm;
}

bool ConnectivityWorkModel::initPath(const Path& _path)
{
  path_ = _path;
  return true;
}

bool ConnectivityWorkModel::turnOffRelay()
{
  LOG_INDEX_INFO("暂未实现继电器off");
  return false;
}
bool ConnectivityWorkModel::turnOnRelay()
{
  LOG_INDEX_INFO("暂未实现继电器on");
  return false;
}
bool ConnectivityWorkModel::addMeasureMessage(const QString& _name, const bool _data)
{
  return app()->getWidgetMes()->addMeasureMessage(getLidarIndex(), _name, static_cast<int>(_data),
                                                  rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
}
bool ConnectivityWorkModel::addMeasureMessage(const QString& _name,
                                              const double _data,
                                              const rsfsc_lib::MeasureDataType _data_type)
{
  return app()->getWidgetMes()->addMeasureMessage(getLidarIndex(), _name, _data, _data_type);
}
bool ConnectivityWorkModel::addMeasureMessage(const QString& _name, const std::string& _data)
{
  return app()->getWidgetMes()->addMeasureMessage(getLidarIndex(), _name, _data);
}

template <typename T>
bool ConnectivityWorkModel::addDifopMeasureMessage(const T& _difop_pkt)
{
  auto limit_name_vec = getLimitCsvUtils()->getLimitNameVec();
  std::map<std::string, std::string> fail_msg;
  auto difop_data = _difop_pkt.getDifopData();
  for (const auto& key : limit_name_vec)
  {
    auto limit_info = getLimitCsvUtils()->getLimitInfo(key);
    if (limit_info.extra_str_info.size() < 2)
    {
      continue;
    }
    if (limit_info.extra_str_info.at(1) != "difop")
    {
      continue;
    }
    if (difop_data.find(key) == difop_data.end())
    {
      LOG_INDEX_ERROR("difop数据中未找到key: {}", key);
      addMeasureMessage(key.c_str(), NAN, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_FLOAT);
      continue;
    }
    std::variant value  = difop_data.at(key);
    auto val_str        = varToStr(value);
    std::string name_zh = limit_info.extra_str_info.at(0);
    if (!MechWorkModel::addMeasureMessage(limit_info, value))
    {
      if (limit_info.getUnit() == "text")
      {
        fail_msg[key] = fmt::format("检测到异常值, 监控项: {}, value: {}, 下限: {}, 上限: {}", name_zh, val_str,
                                    limit_info.min_th_text, limit_info.max_th_text);
      }
      else
      {
        fail_msg[key] = fmt::format("检测到异常值, 监控项: {}, value: {}, 下限: {}, 上限: {}", name_zh, val_str,
                                    limit_info.min_th, limit_info.max_th);
      }
    }
  }

  if (!fail_msg.empty())
  {
    for (const auto& [key, msg] : fail_msg)
    {
      LOG_INDEX_ERROR("{}", msg);
    }
    return false;
  }
  return true;
}

template bool ConnectivityWorkModel::addDifopMeasureMessage<airy_pld::DifopPacket>(const airy_pld::DifopPacket&);
template bool ConnectivityWorkModel::addDifopMeasureMessage<mech::DifopPacket>(const mech::DifopPacket&);

bool ConnectivityWorkModel::requireVbd()
{
  if (auto vbd_vol = mech::requireVbdData(app()->getWidgetMes(), getLidarIndex()))
  {
    vbd_voltage_ = *vbd_vol;
    LOG_INDEX_INFO("VBD电压: {:.2f}V", vbd_voltage_);
    if (!addMeasureMessage("fsm_vbd_voltage", vbd_voltage_, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_FLOAT))
    {
      setFailLabel("vbd电压校验失败");
      setFailLabel("电压超限");
      return false;
    }
    return true;
  }

  LOG_INDEX_ERROR("获取VBD电压失败");
  return addMeasureMessage("fsm_require_vbd_data", false);
}

bool ConnectivityWorkModel::setTimeSyncModeGps()
{
  is_need_restore_time_sync_mode_ = false;
  if (!getLidarManager()->readConfigParamater() && !getLidarManager()->readConfigParamater())
  {
    return false;
  }
  auto mode = mech::TimeSyncMode::GPS;

  if (getLidarManager()->getConfigParaCache().time_sync_mode == mode)
  {
    LOG_INDEX_INFO("时间同步模式[{}:{}]已经是GPS模式，无需设置", mode, static_cast<int>(mode));
    return true;
  }

  if (!getLidarManager()->writeCmd(mech::NET_CMD_CONFIG_SET_TIME_SYNC_MODE, static_cast<uint8_t>(mode)))
  {
    LOG_INDEX_ERROR("设置时间同步模式[{}:{}]失败", mode, static_cast<int>(mode));
    return false;
  }
  is_need_restore_time_sync_mode_ = true;
  LOG_INDEX_INFO("设置时间同步模式[{}:{}]成功", mode, static_cast<int>(mode));
  return true;
}
bool ConnectivityWorkModel::restoreTimeSyncMode()
{
  // if (!is_need_restore_time_sync_mode_)
  // {
  //   LOG_INDEX_INFO("测试中未修改同步模式，无需再恢复");
  //   return true;
  // }

  // if (config_para_bak_.time_sync_mode == mech::TimeSyncMode::GPS)
  // {
  //   LOG_INDEX_INFO("时间同步模式[{}:{}]已经是GPS模式，无需恢复", config_para_bak_.time_sync_mode,
  //                  static_cast<int>(config_para_bak_.time_sync_mode));
  //   return true;
  // }

  if (!getLidarManager()->writeCmd(mech::NET_CMD_CONFIG_SET_TIME_SYNC_MODE,
                                   static_cast<uint8_t>(mech::TimeSyncMode::GPTP)))
  {
    LOG_INDEX_ERROR("恢复时间同步模式[{}:{}]失败", mech::TimeSyncMode::GPTP,
                    static_cast<int>(mech::TimeSyncMode::GPTP));
    return false;
  }
  is_need_restore_time_sync_mode_ = false;

  LOG_INDEX_INFO("恢复时间同步模式[{}:{}]成功", mech::TimeSyncMode::GPTP, static_cast<int>(mech::TimeSyncMode::GPTP));
  return true;
}
std::vector<mech::MsopPacket> ConnectivityWorkModel::getMsopData(const int _count, const int _timeout)
{
  // 首先检查是否已经中止
  if (isAbort())
  {
    LOG_INDEX_ERROR("任务已中止");
    return {};
  }

  // 读取配置参数
  if (!getLidarManager()->readConfigParamater())
  {
    LOG_INDEX_ERROR("读取配置参数失败");
    return {};
  }

  // 获取IP和端口信息
  auto config_para = getLidarManager()->getConfigParaCache();
  auto ip_addr     = config_para.getIpLocal();
  auto port        = config_para.getMsopPort();

  // 创建UDP客户端并设置回调
  std::vector<mech::MsopPacket> msop_pkt_vec;
  auto msop_udp_client = std::make_shared<MechUdp>(sizeof(mech::MsopPacket));
  msop_udp_client->setLogIndex(getLidarIndex());

  std::mutex mtx_msop;
  std::condition_variable cv_msop;
  bool is_completed = false;

  msop_udp_client->regRecvCallback([&](const char* _udp_data) {
    if (isAbort())
    {
      cv_msop.notify_one();
      return;
    }

    mech::MsopPacket msop_pkt {};
    std::memcpy(&msop_pkt, _udp_data, sizeof(mech::MsopPacket));
    if (!msop_pkt.isValid())
    {
      return;
    }

    {
      std::lock_guard<std::mutex> lock(mtx_msop);
      msop_pkt_vec.emplace_back(msop_pkt);
      if (static_cast<int>(msop_pkt_vec.size()) >= _count)
      {
        is_completed = true;
        cv_msop.notify_one();
      }
    }
  });

  // 启动UDP监听
  if (!msop_udp_client->start(ip_addr, port))
  {
    LOG_INDEX_ERROR("MSOP监控启动失败 [{}:{}]", ip_addr, port);
    return {};
  }

  // 等待数据接收完成或超时
  {
    std::unique_lock<std::mutex> lock(mtx_msop);
    cv_msop.wait_for(lock, std::chrono::milliseconds(_timeout), [&]() { return is_completed || isAbort(); });
  }

  // 停止UDP监听
  msop_udp_client->stop();

  // 检查是否因中止而退出
  if (isAbort())
  {
    LOG_INDEX_ERROR("数据采集过程中任务被中止");
    return {};
  }

  // 检查数据包数量
  if (static_cast<int>(msop_pkt_vec.size()) < _count)
  {
    LOG_INDEX_ERROR("获取MSOP数据不足，当前数量: {}, 期望数量: {}", msop_pkt_vec.size(), _count);
  }

  // 调整数据包数量并返回
  msop_pkt_vec.resize(std::min(static_cast<int>(msop_pkt_vec.size()), _count));
  return msop_pkt_vec;
}

bool ConnectivityWorkModel::checkMsopData()
{
  int msop_size = 1000;

  auto msop_vec = getMsopData(msop_size);

  bool res = msop_vec.size() >= msop_size;

  bool is_pass = true;
  if (!addMeasureMessage("fsm_msop_obtain", res))
  {
    setFailLabel("check_msop");
    setFailMsg("获取msop数据失败");
    is_pass = false;
  }

  res = checkMsopData(msop_vec);
  if (!addMeasureMessage("fsm_msop_valid", res))
  {
    setFailLabel("check_msop");
    setFailMsg("MSOP数据异常");
    is_pass = false;
  }
  return true;
}
bool ConnectivityWorkModel::checkDifopData()
{
  auto difop_pkt = getDifopPacket<mech::DifopPacket>();
  if (!difop_pkt.has_value())
  {
    addMeasureMessage("fsm_difop_obtain", false);
    // setWorkModelFailMsg("DIFOP数据异常");
    setFailLabel("check_difop");
    setFailMsg("获取difop数据失败");
    LOG_INDEX_ERROR("获取difop数据失败");
    return false;
  }

  if (!addDifopMeasureMessage(*difop_pkt))
  {
    setFailLabel("check_difop");
    setFailMsg("DIFOP数据异常");
    return false;
  }

  return true;
}

template <typename T>
std::optional<T> ConnectivityWorkModel::getDifopPacket(const int _timeout_ms)
{
  if (!getLidarManager()->readConfigParamater())
  {
    return {};
  }

  auto config_para = getLidarManager()->getConfigParaCache();
  auto ip_addr     = config_para.getIpLocal();
  auto port        = config_para.getDifopPort();

  auto difop_udp_client = std::make_shared<MechUdp>(1248);
  difop_udp_client->setLogIndex(getLidarIndex());

  std::mutex mtx_difop;
  std::condition_variable cv_difop;
  bool is_completed = false;

  T difop_pkt {};
  difop_udp_client->regRecvCallback([&](const char* _udp_data) {
    if (isAbort())
    {
      cv_difop.notify_one();
      return;
    }
    std::memcpy(&difop_pkt, _udp_data, sizeof(T));
    if (!difop_pkt.isValid())
    {
      return;
    }
    {
      std::lock_guard<std::mutex> lock(mtx_difop);
      is_completed = true;
      cv_difop.notify_one();
    }
  });

  if (!difop_udp_client->start(ip_addr, port))
  {
    LOG_INDEX_ERROR("DIFOP监控启动失败 [{}:{}]", ip_addr, port);
    return {};
  }

  {
    std::unique_lock<std::mutex> lock(mtx_difop);
    cv_difop.wait_for(lock, std::chrono::milliseconds(_timeout_ms), [&]() { return is_completed || isAbort(); });
  }

  difop_udp_client->stop();

  if (isAbort())
  {
    LOG_INDEX_ERROR("任务已中止");
    return {};
  }

  if (!is_completed)
  {
    LOG_INDEX_ERROR("获取DIFOP数据超时");
    return {};
  }

  return difop_pkt;
}
// 显式实例化支持的类型
template std::optional<mech::DifopPacket> ConnectivityWorkModel::getDifopPacket<mech::DifopPacket>(int);
template std::optional<airy_pld::DifopPacket> ConnectivityWorkModel::getDifopPacket<airy_pld::DifopPacket>(int);

bool ConnectivityWorkModel::checkMsopData(std::vector<mech::MsopPacket>& _msop_pkt_vec)
{
  if (static_cast<int>(_msop_pkt_vec.size()) < 1000)
  {
    LOG_INDEX_ERROR("MSOP数据不足，当前数量: {}, 期望数量: {}", _msop_pkt_vec.size(), 1000);
    return false;
  }
  std::map<int, int> zero_count;
  auto check_if_zero = [&](const auto& _dist_refl_low, const auto& _dist_refl_high, size_t _offset) {
    for (int j = 0; j < static_cast<int>(_dist_refl_low.size()); ++j)
    {
      int channel_num = static_cast<int>(_offset) + j + 1;
      uint16_t area   = _dist_refl_low.at(j).refl + (_dist_refl_high.at(j).refl << 8U);
      uint16_t dist   = _dist_refl_low.at(j).dist;

      if (dist == 0 && area == 0)
      {
        zero_count[channel_num]++;
      }
    }
  };
  for (auto& msop_packet : _msop_pkt_vec)
  {
    if (!msop_packet.isValid())
    {
      LOG_INDEX_ERROR("pkt_head error: 0x{:x}", msop_packet.pkt_head);
      continue;
    }
    for (size_t i = 0; i < (msop_packet.data_block.size() - 1); i += 4)
    {
      const auto& block_low1  = msop_packet.data_block.at(i);
      const auto& block_high1 = msop_packet.data_block.at(i + 2);

      const auto& block_low2  = msop_packet.data_block.at(i + 1);
      const auto& block_high2 = msop_packet.data_block.at(i + 3);

      check_if_zero(block_low1.dist_refl, block_high1.dist_refl, 0);
      check_if_zero(block_low2.dist_refl, block_high2.dist_refl, block_low1.dist_refl.size());
    }
  }
  bool is_data_normal = true;
  for (const auto& [channel_num, count] : zero_count)
  {
    if (count >= static_cast<int>(_msop_pkt_vec.size()))
    {
      LOG_INDEX_ERROR("通道{}存在连续{}个包的距离与面积都为零", channel_num, count);
      is_data_normal = false;
    }
  }
  return is_data_normal;
}

bool ConnectivityWorkModel::powerOn()
{
  if (getBoxPtr() == nullptr)
  {
    LOG_INDEX_ERROR("获取box失败");
    return false;
  }
  if (getBoxPtr()->switchLidarPower(getLidarIndex(), true))
  {
    last_power_on_time_ = QDateTime::currentDateTime();
    return true;
  }
  return false;
}
bool ConnectivityWorkModel::powerOff()
{
  if (getBoxPtr() == nullptr)
  {
    LOG_INDEX_ERROR("获取box失败");
    return false;
  }
  if (getBoxPtr()->switchLidarPower(getLidarIndex(), false))
  {
    return true;
  }
  return false;
}
QDateTime ConnectivityWorkModel::getLastPowerOnTime() { return last_power_on_time_; }

bool ConnectivityWorkModel::writeSn()
{
  if (!getLidarManager()->readConfigParamater())
  {
    return false;
  }

  auto* lidar_info = getLidarManager()->getLidarInfo();
  auto config_para = getLidarManager()->getConfigParaCache();
  LOG_INDEX_INFO("sn: {} --->>> {}", config_para.getSn(), lidar_info->getLidarSN());

  std::vector<uint8_t> sn_data;
  sn_data.reserve(6);
  for (int i = 0; i < 6; i++)
  {
    sn_data.push_back(static_cast<uint8_t>(lidar_info->getLidarSN().mid(i * 2, 2).toUInt(nullptr, 16)));
  }

  if (!getLidarManager()->writeCmd(mech::NET_CMD_CONFIG_SET_SN, sn_data))
  {
    LOG_INDEX_ERROR("写入SN失败");
    addMeasureMessage("fsm_write_sn", false);
    return false;
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(100));
  // 读取配置参数，确认SN是否写入成功
  if (!getLidarManager()->readConfigParamater())
  {
    return false;
  }
  if (getLidarManager()->getConfigParaCache().getSn() != lidar_info->getLidarSN().toStdString())
  {
    LOG_INDEX_ERROR("写入SN失败");
    addMeasureMessage("fsm_write_sn", false);
    return false;
  }

  LOG_INDEX_INFO("写入SN成功: {}", getLidarManager()->getConfigParaCache().getSn());
  addMeasureMessage("fsm_write_sn", true);
  return true;
}
}  // namespace lidar
}  // namespace robosense