#----------------------------------------------------------------
# Generated CMake target import file for configuration "RelWithDebInfo".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "RSFSCLog::RSFSCLog" for configuration "RelWithDebInfo"
set_property(TARGET RSFSCLog::RS<PERSON>CLog APPEND PROPERTY IMPORTED_CONFIGURATIONS RELWITHDEBINFO)
set_target_properties(RSFSCLog::RSFSCLog PROPERTIES
  IMPORTED_LOCATION_RELWITHDEBINFO "${_IMPORT_PREFIX}/lib/librsfsc_log.so.2.2.5"
  IMPORTED_SONAME_RELWITHDEBINFO "librsfsc_log.so.2.2"
  )

list(APPEND _cmake_import_check_targets RSFSCLog::RSFSCLog )
list(APPEND _cmake_import_check_files_for_RSFSCLog::RSFSCLog "${_IMPORT_PREFIX}/lib/librsfsc_log.so.2.2.5" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
