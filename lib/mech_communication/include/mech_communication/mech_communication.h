﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef MECH_COMMUNICATION_H
#define MECH_COMMUNICATION_H

#include "mech_communication/protocol/codec/mech_base_codec.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "protocol/factory.h"
#include "rsfsc_comm_client/rsfsc_comm_client.h"
#include <atomic>
#include <condition_variable>
#include <unordered_map>
#include <vector>

// modernize-concat-nested-namespaces
namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
class MechCommunication
{
public:
  explicit MechCommunication(const CommunicationInfo& _comm_info = CommunicationInfo());
  explicit MechCommunication(const std::string& _project_code, const int _log_index = -1);
  // explicit MechCommunication(ProtocolType _protocol_type);
  explicit MechCommunication(MechCommunication&&)      = delete;
  explicit MechCommunication(const MechCommunication&) = delete;
  MechCommunication& operator=(MechCommunication&&) = delete;
  MechCommunication& operator=(const MechCommunication&) = delete;
  virtual ~MechCommunication();

  constexpr static uint32_t DEFAULT_TIMEOUT = 10000;

  void setLogIndex(const int _index);
  int getLogIndex() const;

  [[nodiscard]] CommClientType getClientType() const;
  [[nodiscard]] ProtocolType getProtocolType() const;

  void init(ProtocolType _protocol_type);

  /*
    * @brief     try tcp connect and set timeout
    * 
    * @param     _ip                lidar ip
    * @param     _msop_port         lidar msop port
    * @param     _msec        timeout for connection milliseconds
    * @return    true               connect lidar successfully
    * @return    false              fail to connect to lidar
   **/
  bool connect(const std::string& _ip, const uint16_t _msop_port, const uint32_t _msec = 35000);
  bool connect(const TcpClientConfig& _tcp_config, const uint32_t _msec = 35000);

  // serial connect
  /**
    * @brief     try serial connect and set timeout
    * 
    * @param     _serial_port       serial port
    * @param     _baudrate          baudrate
    * @param     _msec              timeout for connection milliseconds
    * @return    true               connect lidar successfully
    * @return    false              fail to connect to lidar
   **/
  // bool connect(const std::string& _serial_port, const uint32_t _msec = DEFAULT_TIMEOUT);
  bool connect(const SerialClientConfig& _serial_config, const uint32_t _msec = DEFAULT_TIMEOUT);
  /**
    * @brief     try tcp connect and set timeout
    * 
    * @param     _msec        timeout for connection milliseconds
    * @return    true               connect lidar successfully
    * @return    false              fail to connect to lidar
   **/
  bool connect(const uint32_t _msec = DEFAULT_TIMEOUT);

  /**
    * @brief     you should call this after all
    *
    * @retval    true
    * @retval    false
    */
  bool disconnect();

  /**
    * @brief     return whether tcp is connected
    *
    * @retval    true
    * @retval    false
    */
  [[nodiscard]] bool isConnected() const;

  virtual void abort();
  void resetAbort();

  // void startIOThread();

  std::optional<mech::Version> getVersion();

  bool writeLidarNet(const std::string& _ip,
                     const uint16_t _msop_port,
                     const uint16_t _difop_port = 0,
                     const uint32_t _msec       = DEFAULT_TIMEOUT);

  /**
    * @brief     write cmd data
    * 
    * @param     _cmd_type          command type
    * @param     _value             command value
    * @param     _msec              timeout
    * @return    true               write and response successfully
    * @return    false              some error happen
   **/
  template <typename T>
  bool writeCmd(const uint32_t _cmd_type, const T _value, const uint32_t _msec = DEFAULT_TIMEOUT);
  bool writeCmd(const uint32_t _cmd_type, const std::vector<uint8_t>& _data, const uint32_t _msec = DEFAULT_TIMEOUT);
  /**
    * @brief     read cmd data
    * 
    * @param     _cmd_type          command type
    * @param     _value             command value
    * @param     _msec              timeout
    * @return    true               read and response successfully
    * @return    false              some error happen
   **/
  template <typename T>
  bool readCmd(const uint32_t _cmd_type, T& _value, const uint32_t _msec = DEFAULT_TIMEOUT);
  bool readCmd(const uint32_t _cmd_type, std::vector<uint8_t>& _data, const uint32_t _msec = DEFAULT_TIMEOUT);

  template <typename T>
  bool readMonitorData(T& _monitor_data, const uint32_t _msec = DEFAULT_TIMEOUT);

  /**
    * @brief     write register
    * 
    * @param     _reg_addr           vector of address to write
    * @param     _reg_val            vector of value corresponding to reg_addr
    * @param     _msec               wait miliseconds for reply from lidar
    * @retval    true                write and response successfully
    * @retval    false               some error happen
   **/
  bool writeRegData(const uint32_t _reg_addr, const uint32_t _reg_val, const uint32_t _msec = DEFAULT_TIMEOUT);
  bool writeTopRegData(const uint32_t _reg_addr, const uint32_t _reg_val, const uint32_t _msec = DEFAULT_TIMEOUT);
  bool writeTopPairRegData(const uint32_t _reg_addr, const uint16_t _reg_value, const uint32_t _msec = DEFAULT_TIMEOUT);
  bool write459RegData(const uint32_t _reg_addr, const uint32_t _reg_value, const uint32_t _msec = DEFAULT_TIMEOUT);

  bool write459RegData(const std::vector<uint32_t>& _reg_addr,
                       const std::vector<uint32_t>& _reg_val,
                       const uint32_t _msec = DEFAULT_TIMEOUT);
  bool writeTopRegData(const std::vector<uint32_t>& _reg_addr,
                       const std::vector<uint32_t>& _reg_val,
                       const uint32_t _msec = DEFAULT_TIMEOUT);
  bool writeTopRegData(const uint32_t _reg_start_addr,
                       const std::vector<uint32_t>& _reg_val,
                       const uint32_t _msec = DEFAULT_TIMEOUT);
  bool writeTopRegData(const std::vector<RegisterData>& _reg_data_vec, const uint32_t _msec = DEFAULT_TIMEOUT);

  /**
    * @brief     write vector of address and value
    *
    * @param     _reg_addr           vector of address to write
    * @param     _reg_val            vector of value corresponding to reg_addr
    * @param     _msec            wait n 10 milliseconds for reply from lidar
    * @param     _download_data_path path where to write download data file
    * @retval    true                write successfully
    * @retval    false               some error happen
    */
  bool writeRegData(const std::vector<uint32_t>& _reg_addr,
                    const std::vector<uint32_t>& _reg_val,
                    const uint32_t _msec = DEFAULT_TIMEOUT);
  /**
    * @brief     写入连续寄存器
    * 
    * @param     _reg_start_addr    
    * @param     _reg_val           
    * @param     _msec              
    * @return    true               
    * @return    false              
   **/
  bool writeRegData(const uint32_t _reg_start_addr,
                    const std::vector<uint32_t>& _reg_val,
                    const uint32_t _msec = DEFAULT_TIMEOUT);
  bool write459RegData(const uint32_t _reg_start_addr,
                       const std::vector<uint32_t>& _reg_val,
                       const uint32_t _msec = DEFAULT_TIMEOUT);
  bool writeRegData(const std::vector<RegisterData>& _reg_data_vec, const uint32_t _msec = DEFAULT_TIMEOUT);

  /**
    * @brief     read mix register can contain both top and bottom register
    * 
    * @param     _reg_addr          register address
    * @param     _reg_val           return register value
    * @param     _msec           timeout
    * @return    true               read successfully
    * @return    false              some error happen
   **/
  bool readMixRegData(const std::vector<uint32_t>& _reg_addr,
                      std::vector<uint32_t>& _reg_val,
                      const uint32_t _msec = DEFAULT_TIMEOUT);

  /*
    * @brief     read top register
    * 
    * @param     _reg_addr          register address
    * @param     _reg_val           return register value
    * @param     _msec           timeout
    * @return    true               read successfully
    * @return    false              some error happen
   **/
  bool readTopRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec = DEFAULT_TIMEOUT);
  bool readTopRegData(const std::vector<uint32_t>& _reg_addr,
                      std::vector<uint32_t>& _reg_val,
                      const uint32_t _msec = DEFAULT_TIMEOUT);

  bool readTopPairRegData(const uint32_t _reg_addr, uint16_t& _reg_val, const uint32_t _msec = DEFAULT_TIMEOUT);

  bool read459RegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec = DEFAULT_TIMEOUT);

  /**
    * @brief     read register data from address
    * 
    * @param     _reg_addr           register address to read 
    * @param     _reg_val            vector of register value to read
    * @retval    true                read successfully
    * @retval    false               some error happen
   **/
  bool readRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec = DEFAULT_TIMEOUT);

  bool read459RegData(const std::vector<uint32_t>& _reg_addr,
                      std::vector<uint32_t>& _reg_val,
                      const uint32_t _msec = DEFAULT_TIMEOUT);
  bool read459PairRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec = DEFAULT_TIMEOUT);

  /**
    * @brief     read multi register data from address in a vector
    * 
    * @param     _reg_addr           register address to read 
    * @param     _reg_val            vector of register value to read
    * @retval    true                read successfully
    * @retval    false               some error happen
   **/
  bool readRegData(const std::vector<uint32_t>& _reg_addr,
                   std::vector<uint32_t>& _reg_val,
                   const uint32_t _msec = DEFAULT_TIMEOUT);
  bool readRegData(const uint32_t _start_reg_addr,
                   const uint32_t _reg_number,
                   std::vector<uint32_t>& _reg_val,
                   const uint32_t _msec_10 = DEFAULT_TIMEOUT);
  bool readTopRegData(const uint32_t _start_reg_addr,
                      const uint32_t _reg_number,
                      std::vector<uint32_t>& _reg_val,
                      const uint32_t _msec_10);
  bool read459RegData(const uint32_t _start_reg_addr,
                      const uint32_t _reg_number,
                      std::vector<uint32_t>& _reg_val,
                      const uint32_t _msec_10 = DEFAULT_TIMEOUT);

  bool writeTopFlash(const std::vector<uint8_t>& _data_buffer, uint32_t _addr_start);
  bool readTopFlash(std::vector<uint8_t>& _data_buffer, uint32_t _addr_start, uint32_t _len);

  /*
    * @brief     Get the Eyes Safe status
    * 
    * @param     _is_open           
    * @param     _msec              
    * @return    true               
    * @return    false              
   **/
  bool getEyesSafe(uint32_t& _is_open, const uint32_t _msec = DEFAULT_TIMEOUT);

  /**
    * @brief     Set the Eyes Safe
    * 
    * @param     _is_open           true 
    * @param     _msec           wait n 10 seconds for reply from lidar
    * @return    true               write successfully
    * @return    false              some error happen
   **/
  bool setEyesSafe(const uint32_t _is_open, const uint32_t _msec = DEFAULT_TIMEOUT);

  /**
    * @brief     read config parameter, only support for mech helios lidar
    * 
    * @param     _config_paramater  helios config parameter data struct
    * @param     _msec           time out
    * @return    true               read successfully
    * @return    false              some error happen
   **/
  // bool readConfigParamater(ConfigPara& _config_paramater, const uint32_t _msec = 200);

  /**
    * @brief     写入零度角
    * 
    * @param     _angle             
    * @param     _msec              
    * @return    true               
    * @return    false              
   **/
  bool writeZeroAngle(const float _angle, const uint32_t _msec = DEFAULT_TIMEOUT);
  bool readZeroAngle(float& _angle, const uint32_t _msec = DEFAULT_TIMEOUT);

  bool stopMotorToAngle(const float& _angle, const uint32_t _msec = DEFAULT_TIMEOUT);
  bool startMotor(const uint32_t _msec = DEFAULT_TIMEOUT);

  /**
    * @brief     read config parameter, support helios::ConfigPara and mech::ConfigPara
    * 
    * @tparam    T                  
    * @param     _config_paramater  
    * @param     _msec              
    * @return    true               
    * @return    false              
   **/
  template <typename T>
  bool readConfigParamater(T& _config_paramater, const uint32_t _msec = DEFAULT_TIMEOUT);
  bool readConfigParamater(const uint32_t _msec = DEFAULT_TIMEOUT);
  [[nodiscard]] mech::ConfigPara getConfigParaCache() const;

  bool writeConfigParamater(const helios::ConfigPara& _config_paramater, const uint32_t _msec = DEFAULT_TIMEOUT);

  bool waitForTopStartUp(const uint32_t _msec = 50000);

  bool getIntensityCalibData(mech::IntensityData& _intensity_data, const uint32_t _msec = 1000);

  bool writeChnAngle(const std::vector<float>& _ver_angle_vec,
                     const std::vector<float>& _hor_angle_vec,
                     const uint32_t _msec = DEFAULT_TIMEOUT);
  bool readChnAngle(std::vector<float>& _ver_angle_vec,
                    std::vector<float>& _hor_angle_vec,
                    const uint16_t& _angle_num = 96,
                    const uint32_t _msec       = DEFAULT_TIMEOUT);

  bool wait(const uint32_t _msec = DEFAULT_TIMEOUT);

  bool isValidMsop(const char* _packet);

  std::string getCurrentProtocolType();

  bool setIP(const std::string& _ip);
  std::string getIP() const;

  std::string getLocalHostAddress() const;

  bool setPort(const uint16_t _port);
  uint16_t getPort() const;

  static bool ping(const std::string& _ip, const std::string& _local_address = "*************");
  bool pingWait(const std::string& _ip, const uint32_t _msec, const std::string& _local_ip = "*************");

  bool readConfigRegister(const uint32_t _cmd_type,
                          std::vector<uint32_t>& _reg_addr,
                          std::vector<uint32_t>& _reg_val,
                          const uint32_t _msec = DEFAULT_TIMEOUT);

  void signalTCPDisconnected();
  void signalTCPDisconnectedAbnormal();
  void signalTCPConnected();

private:
  bool writeWaitResponse(const std::vector<uint8_t>& _packet,
                         const uint32_t _expected_packet_response_code,
                         const uint32_t _msec = DEFAULT_TIMEOUT);
  bool writeWaitCondition(const std::vector<uint8_t>& _packet,
                          bool& _condition,
                          const uint32_t _msec = DEFAULT_TIMEOUT);

  void handleDisconnect(const std::string& _message);
  void handleParseError();

  bool checkRegisterAddress(const std::vector<uint32_t>& _reg_addr, const std::queue<uint32_t>& _register_addr_queue);

private:
  /**
   * @brief     请求写入顶板flash
   * 
   * @param     _start_addr        flash的起始地址
   * @param     _len               写入的字节长度
   * @param     _msec              超时时间
   * @return    true               请求成功
   * @return    false              请求失败
  **/
  bool startWriteTopFlash(const uint32_t _start_addr, const uint32_t _len, const uint32_t _msec = DEFAULT_TIMEOUT);
  /**
   * @brief     请求写入flash之后，数据写入flash，写入前调用startWriteTopFlash，写入完成后调用finishWriteTopFlash
   * 
   * @param     _pkt_count         包计数
   * @param     _data              写入的数据，最大1024字节
   * @param     _msec              超时时间
   * @return    true               写入成功
   * @return    false              写入失败
  **/
  bool writeTopFlash(const uint32_t _pkt_count,
                     const std::vector<uint8_t>& _data,
                     const uint32_t _msec = DEFAULT_TIMEOUT);
  /**
   * @brief     完成写入顶板flash
   * 
   * @param     _msec              超时时间
   * @return    true               成功
   * @return    false              失败
  **/
  bool finishWriteTopFlash(const uint32_t _msec = DEFAULT_TIMEOUT);
  bool writeFlashFrames(const std::vector<uint8_t>& _data_buffer, uint32_t _frame_size);

  std::mutex comm_mutex_;
  std::string msg_header_ = "MechCommunication: ";
  std::string ip_         = "*************";
  uint16_t port_          = 6699;
  std::atomic<bool> is_abort_ { false };
  int log_index_ = -1;

  std::size_t timeout_count_ms_ { 5000 };

  std::unique_ptr<RsfscCommClient> rsfsc_comm_client_ptr_;

  std::shared_ptr<MechBaseCodec> ptr_proto_parser_;
  std::mutex mutex_;
  std::condition_variable cv_;

  mech::ConfigPara config_para_;

  static constexpr std::size_t MAX_READ_BUFFER = 2048;
  std::array<char, MAX_READ_BUFFER> recv_buffer_ { 0x0 };
  std::vector<uint8_t> receive_buffer_;

  void slotReadTCPData();
  void slotDisconnected();
  void slotConnected();

  inline void waitForReach(uint32_t _expected_packet_response_code, std::size_t _msec = 100);
};
}  // namespace lidar
}  // namespace robosense

#endif  // MECH_COMMUNICATION_H