﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   widget_git_manager.h
 * <AUTHOR> Wang (<EMAIL>)
 * @brief
 * @version 1.0.0
 * @date 2024-06-12
 *
 * Copyright (c) 2021, RoboSense Co.,Ltd. - www.robosense.ai
 * All Rights Reserved.
 *
 * You can not use, copy or spread without official authorization.
 *
**/
#ifndef RSFSCLIB_WIDGET_GIT_MANAGER_H
#define RSFSCLIB_WIDGET_GIT_MANAGER_H
#include "rsfsc_msg.h"
#include <QGroupBox>
#include <QtCore/QString>
#include <QtWidgets/QWidget>
#include <qframe.h>
#include <qline.h>
#include <string>
#include <utility>  // for pair

class QLineEdit;
class QSpinBox;
class QComboBox;
class QGridLayout;
class QPushButton;

namespace robosense
{
namespace lidar
{
namespace rsfsc_lib
{

class WidgetGitManager final : public QWidget
{
  Q_OBJECT
public:
  /**
   * @brief     Construct a new Widget Lidar Info object
   * 
   * @param     _program_name      your program name, this should be same as what you 
   *                               put in WidgetLogSetting's constructor
   * @param     _parent            usually is your WidgetGitManager
   */
  explicit WidgetGitManager(QWidget* _parent);
  explicit WidgetGitManager(WidgetGitManager&&)      = delete;
  explicit WidgetGitManager(const WidgetGitManager&) = delete;
  WidgetGitManager& operator=(WidgetGitManager&&) = delete;
  WidgetGitManager& operator=(const WidgetGitManager&) = delete;
  ~WidgetGitManager() final;

  std::string getRepositoryUrl();
  std::string getLocalPath();
  void setRepositoryUrl(const std::string& _url);
  void setLocalPath(const std::string& _path);

  void clearBranchItem();
  void clearCommitItem();
  void addBranchItem(const std::string& _branch_name);
  void addCommitItem(const std::string& _commit_info);
  void setCurrentBranch(const std::string& _branch_name);
  std::string getCurrentBranchName();
  std::string getInputText();

private Q_SLOTS:
  void slotBranchItemChanged(const QString& _branch_name);

Q_SIGNALS:
  void signalClone();
  void signalOpen();
  void signalPull();
  void signalRemoteCompare();
  void signalLocalCompare();
  void signalCommit();
  void signalPush();
  void signalCheckoutBranch(const std::string& _branch_name);

private:
  QGridLayout* layout_git_manager_;
  QGroupBox* group_box_;
  QLineEdit* line_edit_repo_url_;
  QPushButton* push_button_clone_;
  QLineEdit* line_edit_local_path_;
  QPushButton* push_button_open_;
  QPushButton* push_button_pull_;
  QComboBox* combo_box_branch_;
  QComboBox* combo_box_commit_;
  QPushButton* push_button_remote_compare_;
  QPushButton* push_button_local_compare_;
  QLineEdit* line_edit_commit_msg_;
  QPushButton* push_button_commit_;
  QPushButton* push_button_push_;
};
}  // namespace rsfsc_lib
}  // namespace lidar
}  // namespace robosense

#endif  // RSFSCLIB_WIDGET_GIT_MANAGER_H
