﻿/****************************************************************
 * @file      config.h
 * <AUTHOR>
 * @brief     this file was generated automatically, dont try to change it
****************************************************************/

#define PROJECT_VERSION_MAJOR @PROJECT_VERSION_MAJOR@
#define PROJECT_VERSION_MINOR @PROJECT_VERSION_MINOR@
#define PROJECT_VERSION_PATCH @PROJECT_VERSION_PATCH@
#define PROJECT_VERSION_TWEAK @PROJECT_VERSION_TWEAK@
#define PROJECT_VERSION_STR   "@PROJECT_VERSION@"
#define PROJECT_NAME                     "@CMAKE_PROJECT_NAME@"
#define PROJECT_NAME_ZH                  "@PROJECT_NAME_ZH@"
#define PROJECT_CODE		          "@PROJECT_CODE@"
#define PROJECT_COMPILE_COMMIT           "@PROJECT_COMPILE_COMMIT@"
#define PROJECT_COMPILE_TIME             "@PROJECT_COMPILE_TIME@"
#define CMAKE_BUILD_TYPE                 "@CMAKE_BUILD_TYPE@"
#define INSTALL_PREFIX_SHARE             "@INSTALL_PREFIX_SHARE@"

#define FACTORY_LOCATION "@FACTORY_LOCATION@"

#cmakedefine01 SHOW_MESSAGE_ON_TERMINAL
#cmakedefine01 SEND_MAIL_SUPPORT
#cmakedefine01 MARKER_SUPPORT
