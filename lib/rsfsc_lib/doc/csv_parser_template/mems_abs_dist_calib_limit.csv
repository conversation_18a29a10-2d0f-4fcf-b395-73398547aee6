# 参数名称会直接作为索引读到程序中，请注意不要随意更改,,,,
# 下限、上限不得留空，如果范围不确定或者很广，则写一个很大的数即可。默认为浮点数,,,,
# 上下限采用小于等于、大于等于的形式，即当特性等于上下限时为合格,,,,
# 单位写明方便测试和工程人员查看分析，若没有单位，则统一写 unitless,,,,
# 备注用于说明一些特殊情况，方便其他人查看，本文件从第8行开始解析，请不要随意增删前面的行数,,,,
,,,,
参数名称,下限,上限,单位,备注
calib_roll_threshold,0,0.05,m,两侧地面与基准点云偏离的平均距离阈值
calib_pitch_threshold,0,0.02,m,地面与基准点云偏离的平均距离阈值
calib_yaw_threshold,0,0.03,m,地面与基准点云墙题中心偏离的平均距离阈值
calib_yk_threshold,0,0.03,m,两侧墙面距离与基准点云偏离的平均距离阈值
evaluation_pitch_threshold,0,0.05,m,地面中心区域与基准点云偏离的距离阈值
evaluation_yaw_threshold,0,0.07,m,两侧墙面与基准墙面偏离距离绝对值的和
evaluation_ground_threshold,0,0.05,m,地面接缝处的厚度阈值
evaluation_wall_threshold,0,0.07,m,两侧墙面平均厚度阈值(当墙面倾斜时，其厚度是会超出阈值的)
phase_wall_thickness_threshold,0,0.03,m,评估相位墙体厚度收敛的距离阈值
phase_thickness_step_threshold,0,0.005,m,放宽相位收敛条件的阶梯阈值
