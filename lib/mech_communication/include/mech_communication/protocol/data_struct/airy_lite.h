﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef AIRY_LITE_DATA_STRUCT_H
#define AIRY_LITE_DATA_STRUCT_H

#include "mech.h"
#include <array>
#include <cstdint>
#include <optional>
#include <vector>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
namespace airy
{
namespace lite
{
using CheckSumType = uint16_t;
enum class FrameType : uint8_t
{
  MSOP,
  IMU,
  DIFOP,
  UDS
};

constexpr uint16_t FRAME_FLAG = 0x5aff;
constexpr uint8_t FRAME_TAIL  = 0xfe;
#pragma pack(push, 1)
struct FrameHead
{
  uint16_t flag;
  std::array<uint8_t, 3> reserve1;
  FrameType data_type;
  void swapEndian();
};
struct Frame
{
  FrameHead head;
  uint16_t length;
  // static constexpr uint16_t wrapSize() { return sizeof(Frame) + sizeof(FRAME_TAIL) + sizeof(CheckSumType); }
  static constexpr uint16_t wrapSize() { return sizeof(Frame); }
  static constexpr uint16_t frameFlag() { return FRAME_FLAG; }
  // static constexpr uint8_t frameTail() { return FRAME_TAIL; }
  [[nodiscard]] uint16_t getExpectedSize() const
  {
    // return static_cast<uint16_t>(sizeof(FrameHead) + length + sizeof(FRAME_TAIL) + sizeof(CheckSumType));
    return static_cast<uint16_t>(sizeof(FrameHead) + length);
  }
  bool isValid();
  static std::optional<std::vector<uint8_t>> isValid(std::vector<uint8_t>& _data);
  void swapEndian();
};

namespace server
{
struct FrameHead
{
  uint16_t flag;
  std::array<uint8_t, 3> reserve1;
  FrameType data_type;
  void swapEndian();
};
struct Frame
{
  server::FrameHead head;
  uint16_t length;
  // static constexpr uint16_t wrapSize() { return sizeof(Frame) + sizeof(FRAME_TAIL) + sizeof(CheckSumType); }
  static constexpr uint16_t wrapSize() { return sizeof(Frame); }
  static constexpr uint16_t frameFlag() { return FRAME_FLAG; }
  // static constexpr uint8_t frameTail() { return FRAME_TAIL; }
  void swapEndian();
  [[nodiscard]] uint16_t getExpectedSize() const
  {
    // return static_cast<uint16_t>(sizeof(FrameHead) + length + sizeof(FRAME_TAIL) + sizeof(CheckSumType));
    return static_cast<uint16_t>(sizeof(FrameHead) + length);
  }
  bool isValid();
  static std::optional<std::vector<uint8_t>> isValid(std::vector<uint8_t>& _data);
};
#pragma pack(pop)
}  // namespace server
}  // namespace lite
}  // namespace airy
}  // namespace lidar
}  // namespace robosense

#endif  // AIRY_LITE_DATA_STRUCT_H