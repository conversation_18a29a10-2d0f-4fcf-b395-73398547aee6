﻿/******************************************************************************
* Copyright 2022 RoboSense All rights reserved.
* Suteng Innovation Technology Co., Ltd. www.robosense.ai

* This software is provided to you directly by RoboSense and might
* only be used to access RoboSense LiDAR. Any compilation,
* modification, exploration, reproduction and redistribution are
* restricted without RoboSense's prior consent.

* THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
* WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
* DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.
*****************************************************************************/

#include "lidar_communication_box.h"
#include <QDateTime>
#include <boost/bind.hpp>
#include <boost/lambda/lambda.hpp>
#include <iomanip>
#include <iostream>
#include <sstream>

#include "rsfsc_log/rsfsc_log.h"

namespace robosense
{
namespace lidar
{
LidarCommunicationBox::LidarCommunicationBox()
{
  serial_port_.reset(new SerialPortDriver);
  serial_port_->regDealDataCallback(std::bind(&LidarCommunicationBox::readCallBack, this, std::placeholders::_1));
}

bool LidarCommunicationBox::connect(std::string _port_name, std::string _password)
{
  if (!setSerialPermission(_password))
  {
    return false;
  }
  if (!serial_port_->openSerial("/dev/" + _port_name, SerialPortDriver::BAUD_RATE_115200,
                                SerialPortDriver::FLOW_CONTROL_NONE, SerialPortDriver::PARITY_NONE,
                                SerialPortDriver::STOP_BITS_ONE, SerialPortDriver::CHARACTER_SIZE_8))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("连接控制板失败[{0}]", _port_name);
    return false;
  }
  is_connect_ = true;
  robosense::lidar::RSFSCLog::getInstance()->info("连接控制板成功[{0}]", _port_name);
  return true;
}
bool LidarCommunicationBox::connect(std::string _port_name)
{
  if (!serial_port_->openSerial("/dev/" + _port_name, SerialPortDriver::BAUD_RATE_115200,
                                SerialPortDriver::FLOW_CONTROL_NONE, SerialPortDriver::PARITY_NONE,
                                SerialPortDriver::STOP_BITS_ONE, SerialPortDriver::CHARACTER_SIZE_8))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("连接控制板失败[{0}]", _port_name);
    return false;
  }
  is_connect_ = true;
  robosense::lidar::RSFSCLog::getInstance()->info("连接控制板成功[{0}]", _port_name);
  return true;
}

bool LidarCommunicationBox::disconnect()
{
  serial_port_->closeSerial();
  return true;
}

bool LidarCommunicationBox::getFirmwareVersion(std::string& _version)
{
  std::lock_guard<std::mutex> lock(mutex_);
  uint8_t function_code    = 0x01;
  std::vector<uint8_t> cmd = { 0xA1, 0xB2, function_code, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1A, 0x2B };
  recive_size_             = 12;
  recive_finish_           = false;
  if (!serial_port_->write(cmd))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("写固件版本读取命令失败");
    return false;
  }

  if (!waitReciveFinish(function_code))
  {
    return false;
  }

  std::stringstream ss;
  ss << std::hex << std::setfill('0') << std::setw(2) << static_cast<unsigned>(recive_data_buffer_.at(6))
     << std::setw(2) << static_cast<unsigned>(recive_data_buffer_.at(5)) << std::setw(2)
     << static_cast<unsigned>(recive_data_buffer_.at(4)) << std::setw(2)
     << static_cast<unsigned>(recive_data_buffer_.at(3));

  _version = ss.str();

  return true;
}

bool LidarCommunicationBox::getElectricity(int _lidar_idx, uint32_t& _electricity)
{
  std::lock_guard<std::mutex> lock(mutex_);
  if (_lidar_idx <= 0 || _lidar_idx > LIDAR_NUM)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("雷达索引超出范围:{0}", _lidar_idx);
    return false;
  }
  uint8_t function_code    = 0x04;
  uint8_t lidar_index      = _lidar_idx;
  std::vector<uint8_t> cmd = { 0xA1, 0xB2, function_code, lidar_index, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1A, 0x2B };
  recive_size_             = 12;
  recive_finish_           = false;
  if (!serial_port_->write(cmd))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("查询电流信息失败");
    return false;
  }

  if (!waitReciveFinish(function_code))
  {
    return false;
  }

  if (!checkReturnIsError(function_code, lidar_index, recive_data_buffer_.at(9)))
  {
    return false;
  }

  _electricity = (recive_data_buffer_.at(7) << 24) + (recive_data_buffer_.at(6) << 16) +
                 (recive_data_buffer_.at(5) << 8) + recive_data_buffer_.at(4);

  return true;
}

bool LidarCommunicationBox::getVoltage(int _lidar_idx, uint32_t& _voltage)
{
  std::lock_guard<std::mutex> lock(mutex_);
  if (_lidar_idx <= 0 || _lidar_idx > LIDAR_NUM)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("雷达索引超出范围:{0}", _lidar_idx);
    return false;
  }

  uint8_t function_code    = 0x05;
  uint8_t lidar_index      = _lidar_idx;
  std::vector<uint8_t> cmd = { 0xA1, 0xB2, function_code, lidar_index, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1A, 0x2B };
  recive_size_             = 12;
  recive_finish_           = false;
  if (!serial_port_->write(cmd))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("查询电压信息失败");
    return false;
  }

  if (!waitReciveFinish(function_code))
  {
    return false;
  }

  if (!checkReturnIsError(function_code, lidar_index, recive_data_buffer_.at(9)))
  {
    return false;
  }

  _voltage = (recive_data_buffer_.at(7) << 24) + (recive_data_buffer_.at(6) << 16) + (recive_data_buffer_.at(5) << 8) +
             recive_data_buffer_.at(4);

  return true;
}

bool LidarCommunicationBox::getSync(int _lidar_idx, uint32_t& _sync)
{
  std::lock_guard<std::mutex> lock(mutex_);
  if ((_lidar_idx <= 0 || _lidar_idx > LIDAR_NUM) && (_lidar_idx != 0xff))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("雷达索引超出范围:{0}", _lidar_idx);
    return false;
  }

  uint8_t function_code    = 0x0B;
  uint8_t lidar_index      = _lidar_idx;
  std::vector<uint8_t> cmd = { 0xA1, 0xB2, function_code, 0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1A, 0x2B };
  recive_size_             = 12;
  recive_finish_           = false;
  if (!serial_port_->write(cmd))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("查询电压信息失败");
    return false;
  }

  if (!waitReciveFinish(function_code))
  {
    return false;
  }

  if (!checkReturnIsError(function_code, lidar_index, recive_data_buffer_.at(9)))
  {
    return false;
  }

  _sync = recive_data_buffer_.at(3);

  return true;
}

bool LidarCommunicationBox::switchLidarPower(int _lidar_idx, bool _is_turn_on)
{
  std::lock_guard<std::mutex> lock(mutex_);
  if ((_lidar_idx <= 0 || _lidar_idx > LIDAR_NUM) && (_lidar_idx != 0xff))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("雷达索引超出范围:{0}", _lidar_idx);
    return false;
  }

  uint8_t function_code    = 0x02;
  uint8_t turn_code        = _is_turn_on ? 0x01 : 0x00;
  uint8_t lidar_idx        = _lidar_idx;
  std::vector<uint8_t> cmd = {
    0xA1, 0xB2, function_code, lidar_idx, turn_code, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1A, 0x2B
  };
  recive_size_   = 12;
  recive_finish_ = false;
  if (!serial_port_->write(cmd))
  {
    robosense::lidar::RSFSCLog::getInstance()->error(
      QString("设置电源开关%1失败").arg(_is_turn_on ? "上电" : "下电").toStdString());
    return false;
  }

  if (!waitReciveFinish(function_code))
  {
    return false;
  }

  if (!checkReturnIsError(function_code, lidar_idx, recive_data_buffer_.at(9)))
  {
    return false;
  }

  return true;
}

bool LidarCommunicationBox::switchWakeSignal(int _lidar_idx, bool _is_wake)
{
  std::lock_guard<std::mutex> lock(mutex_);
  if ((_lidar_idx <= 0 || _lidar_idx > LIDAR_NUM) && (_lidar_idx != 0xff))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("雷达索引超出范围:{0}", _lidar_idx);
    return false;
  }

  uint8_t function_code    = 0x03;
  uint8_t turn_code        = _is_wake ? 0x01 : 0x00;
  uint8_t lidar_idx        = _lidar_idx;
  std::vector<uint8_t> cmd = {
    0xA1, 0xB2, function_code, lidar_idx, turn_code, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1A, 0x2B
  };
  recive_size_   = 12;
  recive_finish_ = false;
  if (!serial_port_->write(cmd))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("设置唤醒信号失败");
    return false;
  }

  if (!waitReciveFinish(function_code))
  {
    return false;
  }

  if (!checkReturnIsError(function_code, lidar_idx, recive_data_buffer_.at(9)))
  {
    return false;
  }

  return true;
}

bool LidarCommunicationBox::setNetworkStandby(int _lidar_idx, bool _is_standby)
{
  std::lock_guard<std::mutex> lock(mutex_);
  if ((_lidar_idx <= 0 || _lidar_idx > LIDAR_NUM) && (_lidar_idx != 0xff))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("雷达索引超出范围:{0}", _lidar_idx);
    return false;
  }

  uint8_t function_code    = 0x09;
  uint8_t lidar_index      = _lidar_idx;
  uint8_t ctrl             = _is_standby ? 0x01 : 0x00;
  std::vector<uint8_t> cmd = { 0xA1, 0xB2, function_code, lidar_index, ctrl, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1A, 0x2B };
  recive_size_             = 12;
  recive_finish_           = false;
  if (!serial_port_->write(cmd))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("设置网络待机失败");
    return false;
  }

  if (!waitReciveFinish(function_code))
  {
    return false;
  }

  if (!checkReturnIsError(function_code, lidar_index, recive_data_buffer_.at(9)))
  {
    return false;
  }

  return true;
}

bool LidarCommunicationBox::getLidarIsExist(int _lidar_idx, bool& _is_exist)
{
  std::lock_guard<std::mutex> lock(mutex_);
  if (_lidar_idx <= 0 || _lidar_idx > LIDAR_NUM)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("雷达索引超出范围:{0}", _lidar_idx);
    return false;
  }

  uint8_t function_code    = 0x011;
  uint8_t lidar_index      = _lidar_idx;
  std::vector<uint8_t> cmd = { 0xA1, 0xB2, function_code, lidar_index, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1A, 0x2B };
  recive_size_             = 12;
  recive_finish_           = false;
  if (!serial_port_->write(cmd))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("查询电压信息失败");
    return false;
  }

  if (!waitReciveFinish(function_code))
  {
    return false;
  }

  if (!checkReturnIsError(function_code, lidar_index, recive_data_buffer_.at(9)))
  {
    return false;
  }

  _is_exist = (recive_data_buffer_.at(4) == 1) ? true : false;

  return true;
}

bool LidarCommunicationBox::setLidarInputVotlage(const int _voltage_mv)
{
  std::lock_guard<std::mutex> lock(mutex_);
  if (_voltage_mv < 9000 || _voltage_mv > 16000)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("设置的电压超过范围:{0}", _voltage_mv);
    return false;
  }

  uint8_t function_code    = 0x12;
  uint8_t lidar_index      = 1;
  uint8_t voltage_l        = _voltage_mv & 0xff;
  uint8_t voltage_h        = (_voltage_mv & 0xff00) >> 8;
  std::vector<uint8_t> cmd = { 0xA1, 0xB2, function_code, lidar_index, voltage_l, voltage_h,
                               0x00, 0x00, 0x00,          0x00,        0x1A,      0x2B };
  recive_size_             = 12;
  recive_finish_           = false;
  if (!serial_port_->write(cmd))
  {
    robosense::lidar::RSFSCLog::getInstance()->error("设置电压失败");
    return false;
  }

  if (!waitReciveFinish(function_code))
  {
    return false;
  }

  if (!checkReturnIsError(function_code, lidar_index, recive_data_buffer_.at(9)))
  {
    return false;
  }

  return true;
}

void LidarCommunicationBox::readCallBack(std::deque<char>& _data)
{
  if (_data.size() <= 0 || recive_size_ <= 0)
  {
    return;
  }
  if (_data.size() != recive_size_ && _data.size() > 2)
  {
    std::stringstream stream;
    for (auto it : _data)
    {
      stream << std::hex << static_cast<uint16_t>(it) << " ";
    }
    robosense::lidar::RSFSCLog::getInstance()->debug(stream.str());
    return;
  }
  if (_data.at(_data.size() - 1) != 0x2b || _data.at(_data.size() - 2) != 0x1a)
  {
    std::stringstream stream;
    for (auto it : _data)
    {
      stream << std::hex << static_cast<uint16_t>(it) << " ";
    }
    robosense::lidar::RSFSCLog::getInstance()->debug(stream.str());
    return;
  }
  std::vector<uint8_t>().swap(recive_data_buffer_);
  for (auto it : _data)
  {
    recive_data_buffer_.push_back(static_cast<uchar>(it));
  }
  recive_finish_ = true;
}

bool LidarCommunicationBox::waitReciveFinish(const uint8_t _function_code)
{
  QDateTime start_time = QDateTime::currentDateTime();
  while (!recive_finish_)
  {
    QDateTime cur_time = QDateTime::currentDateTime();
    if (start_time.msecsTo(cur_time) >= 500)
    {
      robosense::lidar::RSFSCLog::getInstance()->error("读取串口数据超时");
      return false;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
  };

  std::stringstream stream;
  stream << "recive msg -> ";
  for (auto it : recive_data_buffer_)
  {
    stream << std::hex << static_cast<uint16_t>(it) << " ";
  }
  robosense::lidar::RSFSCLog::getInstance()->debug(stream.str());

  if (recive_data_buffer_.size() != recive_size_)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("串口返回的数据长度错误");
    return false;
  }

  if (recive_data_buffer_.at(2) != _function_code)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("串口返回的数据功能码错误错误");
    return false;
  }

  return true;
}

bool LidarCommunicationBox::setSerialPermission(const std::string _password)
{
  std::string cmd = "echo " + _password + " | sudo -S chmod 777 /dev/ttyUSB*;";
  if (system(cmd.c_str()) != 0)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("设置串口权限失败");
    return false;
  };

  return true;
}

bool LidarCommunicationBox::checkReturnIsError(const uint8_t _function_code,
                                               const uint8_t _lidar_index,
                                               const uint8_t _error_code)
{
  if (_error_code == 0x55)
  {
    std::stringstream stream;
    stream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(_function_code);
    robosense::lidar::RSFSCLog::getInstance()->error(
      "function code = " + stream.str() + " lidar index = " + std::to_string(_lidar_index) + " 请检查雷达索引");
    return false;
  }
  return true;
}
}  // namespace lidar
}  // namespace robosense
