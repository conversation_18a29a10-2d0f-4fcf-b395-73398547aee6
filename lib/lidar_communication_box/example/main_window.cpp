﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "main_window.h"
#include <QSerialPortInfo>
#include <iostream>
#include <regex>

namespace robosense
{
namespace lidar
{

MainWindow::MainWindow(int _argc, char** _argv, QWidget* _parent) : QMainWindow(_parent)
{
  Q_UNUSED(_argc);
  Q_UNUSED(_argv);
  setupLayout();

  connect(pushbutton_connect_, &QPushButton::clicked, this, &MainWindow::slotConnect);
  connect(pushbutton_read_firmware_version_, &QPushButton::clicked, this, &MainWindow::slotReadFirmware);
  connect(pushbutton_read_electricity_, &QPushButton::clicked, this, &MainWindow::slotReadElectricity);
  connect(pushbutton_read_voltage_, &QPushButton::clicked, this, &MainWindow::slotReadVoltage);
  connect(pushbutton_get_lidar_is_exist_, &QPushButton::clicked, this, &MainWindow::slotReadLidarIsExist);
  connect(checkbox_switch_lidar_power_, &QPushButton::clicked, this, &MainWindow::slotSwitchLidarPower);
  connect(checkbox_switch_wake_signal_, &QPushButton::clicked, this, &MainWindow::slotSwitchWakeSignal);
  connect(checkbox_set_network_standby_, &QPushButton::clicked, this, &MainWindow::slotSetNetworkStandby);
  connect(pushbutton_set_voltage_, &QPushButton::clicked, this, &MainWindow::slotSetLidarInputVoltage);

  QList<QSerialPortInfo> port_info = QSerialPortInfo::availablePorts();
  for (int i = 0; i < port_info.size(); i++)
  {
    combox_serial_port_->addItem(port_info.value(i).portName());
  }

  QStringList list = { "1", "2", "3", "4", "5", "6", "7", "8", "0xff" };
  combox_lidar_index_->addItems(list);

  ptr_communication_box_.reset(new LidarCommunicationBox);
}

MainWindow::~MainWindow() {}

void MainWindow::slotConnect()
{
  std::string port_name = combox_serial_port_->currentText().toStdString();
  std::string password  = lineedit_password_->text().toStdString();
  ptr_communication_box_->connect(port_name, password);
}

void MainWindow::slotReadFirmware()
{
  std::string firmware;
  ptr_communication_box_->getFirmwareVersion(firmware);
  lineedit_firmware_version_->setText(firmware.data());
}

void MainWindow::slotReadElectricity()
{
  int lidar_index      = combox_lidar_index_->currentText().toInt(nullptr, 16);
  uint32_t electricity = 0;
  ptr_communication_box_->getElectricity(lidar_index, electricity);
  lineedit_electricity_->setText(QString::number(electricity));
}

void MainWindow::slotReadVoltage()
{
  int lidar_index  = combox_lidar_index_->currentText().toInt(nullptr, 16);
  uint32_t voltage = 0;
  ptr_communication_box_->getVoltage(lidar_index, voltage);
  lineedit_voltage_->setText(QString::number(voltage));
}

void MainWindow::slotReadLidarIsExist()
{
  int lidar_index = combox_lidar_index_->currentText().toInt(nullptr, 16);
  bool is_exist   = 0;
  ptr_communication_box_->getLidarIsExist(lidar_index, is_exist);
  lineedit_get_lidar_is_exist_->setText(is_exist ? "雷达在位" : "雷达不存在");
}

void MainWindow::slotSwitchLidarPower(bool _is_open)
{
  int lidar_index = combox_lidar_index_->currentText().toInt(nullptr, 16);
  ptr_communication_box_->switchLidarPower(lidar_index, _is_open);
}

void MainWindow::slotSwitchWakeSignal(bool _is_open)
{
  int lidar_index = combox_lidar_index_->currentText().toInt(nullptr, 16);
  ptr_communication_box_->switchWakeSignal(lidar_index, _is_open);
}

void MainWindow::slotSetNetworkStandby(bool _is_standby)
{
  int lidar_index = combox_lidar_index_->currentText().toInt(nullptr, 16);
  ptr_communication_box_->setNetworkStandby(lidar_index, _is_standby);
}

void MainWindow::slotSetLidarInputVoltage()
{
  int voltage = spinbox_voltage_->value();
  ptr_communication_box_->setLidarInputVotlage(voltage);
}

void MainWindow::setupLayout()
{
  QGridLayout* main_window_layout = new QGridLayout;

  combox_lidar_index_ = new QComboBox(this);
  lineedit_password_  = new QLineEdit("Sti123456", this);

  combox_serial_port_ = new QComboBox(this);
  pushbutton_connect_ = new QPushButton("连接盒子", this);

  pushbutton_read_firmware_version_ = new QPushButton("读盒子固件", this);
  lineedit_firmware_version_        = new QLineEdit(this);

  pushbutton_read_electricity_ = new QPushButton("读雷达输入电流", this);
  lineedit_electricity_        = new QLineEdit(this);

  pushbutton_read_voltage_ = new QPushButton("读雷达输入电压", this);
  lineedit_voltage_        = new QLineEdit(this);

  pushbutton_get_lidar_is_exist_ = new QPushButton("获取雷达是否在位", this);
  lineedit_get_lidar_is_exist_   = new QLineEdit(this);

  pushbutton_set_voltage_ = new QPushButton("设置雷达输入电压", this);
  spinbox_voltage_        = new QSpinBox(this);
  spinbox_voltage_->setRange(9000, 16000);
  spinbox_voltage_->setValue(12000);
  spinbox_voltage_->setSingleStep(1000);

  checkbox_switch_lidar_power_ = new QCheckBox("切换雷达电源", this);
  checkbox_switch_wake_signal_ = new QCheckBox("切换雷达唤醒信号", this);

  checkbox_set_network_standby_ = new QCheckBox("设置网络待机", this);

  main_window_layout->addWidget(combox_lidar_index_, 0, 0, 1, 1);
  main_window_layout->addWidget(lineedit_password_, 0, 1, 1, 1);
  main_window_layout->addWidget(combox_serial_port_, 1, 0, 1, 1);
  main_window_layout->addWidget(pushbutton_connect_, 1, 1, 1, 1);
  main_window_layout->addWidget(pushbutton_read_firmware_version_, 2, 0, 1, 1);
  main_window_layout->addWidget(lineedit_firmware_version_, 2, 1, 1, 1);
  main_window_layout->addWidget(pushbutton_read_electricity_, 3, 0, 1, 1);
  main_window_layout->addWidget(lineedit_electricity_, 3, 1, 1, 1);
  main_window_layout->addWidget(pushbutton_read_voltage_, 4, 0, 1, 1);
  main_window_layout->addWidget(lineedit_voltage_, 4, 1, 1, 1);
  main_window_layout->addWidget(pushbutton_get_lidar_is_exist_, 5, 0, 1, 1);
  main_window_layout->addWidget(lineedit_get_lidar_is_exist_, 5, 1, 1, 1);
  main_window_layout->addWidget(pushbutton_set_voltage_, 6, 0, 1, 1);
  main_window_layout->addWidget(spinbox_voltage_, 6, 1, 1, 1);
  main_window_layout->addWidget(checkbox_switch_lidar_power_, 7, 0, 1, 1);
  main_window_layout->addWidget(checkbox_switch_wake_signal_, 7, 1, 1, 1);
  main_window_layout->addWidget(checkbox_set_network_standby_, 8, 0, 1, 1);

  this->setWindowTitle("电源盒子测试");

  QWidget* widget_main = new QWidget;
  widget_main->setLayout(main_window_layout);
  this->setCentralWidget(widget_main);
}
}  // namespace lidar
}  // namespace robosense
