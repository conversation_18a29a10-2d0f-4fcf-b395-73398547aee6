﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "widget_para_setting.h"
#include "config.h"
#include "rsfsc_lib/include/rsfsc_qsettings.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QCheckBox>
#include <QCloseEvent>
#include <QFileDialog>
#include <QHBoxLayout>
#include <QLineEdit>
#include <QMetaProperty>
#include <QPushButton>
#include <QTextCodec>
#include <QTimer>
#include <QWidget>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

WidgetParaSetting::WidgetParaSetting(QWidget* _parent) :
  QDialog(_parent), tree_widget_(new QTreeWidget(this)), button_save_para_(new QPushButton("保存参数", this))
{
  qRegisterMetaType<ParaInt>("ParaInt");
  qRegisterMetaType<ParaDouble>("ParaDouble");
  qRegisterMetaType<ParaPath>("ParaPath");
  qRegisterMetaType<ParaBool>("ParaBool");

  QVBoxLayout* main_layout = new QVBoxLayout(this);

  tree_widget_->setColumnCount(2);
  tree_widget_->setHeaderLabels(QStringList() << "参数"
                                              << "数值");
  main_layout->addWidget(tree_widget_);
  main_layout->addWidget(button_save_para_);
  button_save_para_->setMaximumWidth(200);

  createPropertyContainers();

  auto para_info = std::make_shared<ParaInfo>();
  QTimer::singleShot(1000, [this, para_info]() {
    if (!compareProperties(*para_info))
    {
      LOG_ERROR("para info 与 widget para 不匹配，请联系开发者，确认para info 与 widget之间的映射关系");
    }
  });

  connect(button_save_para_, &QPushButton::clicked, this, &WidgetParaSetting::slotButtonSaveParaClicked);

  readParaInfoSettings();
  readLayoutSettings();
};
WidgetParaSetting::~WidgetParaSetting() { LOG_INFO("destructed"); }

void WidgetParaSetting::show()
{
  updateCurrToWidgetValue();
  readLayoutSettings();
  if (!isVisible())
  {
    QDialog::show();
  }
}
void WidgetParaSetting::slotButtonSaveParaClicked()
{
  updateWidgetToCurrValue();
  writeParaInfoSettings();
}

std::shared_ptr<ParaInfo> WidgetParaSetting::getCurrParaInfo()
{
  auto para_info = std::make_shared<ParaInfo>();
  if (!compareProperties(*para_info))
  {
    LOG_ERROR("para info 与 widget 不匹配，请联系开发者，确认para info 与 widget之间的映射关系");
  }

  const QMetaObject* meta_obj = this->metaObject();
  for (int i = 0; i < meta_obj->propertyCount(); ++i)
  {
    QMetaProperty meta_prop = meta_obj->property(i);
    QString prop_name       = meta_prop.name();
    QString name_suffix     = PARA_VAR_SUFFIX;
    if (!prop_name.endsWith(name_suffix))
    {
      continue;
    }
    QString para_name = prop_name.left(prop_name.size() - name_suffix.size());
    QVariant para_var = meta_prop.read(this);
    if (!para_var.isValid())
    {
      LOG_INFO("para_var is invalid: {}", para_name);
      continue;
    }

    if (para_var.canConvert<ParaInt>())
    {
      ParaInt para = para_var.value<ParaInt>();
      para_info->setProperty(prop_name.toStdString().c_str(), QVariant::fromValue(para.curr_val));
    }
    else if (para_var.canConvert<ParaDouble>())
    {
      ParaDouble para = para_var.value<ParaDouble>();
      para_info->setProperty(prop_name.toStdString().c_str(), QVariant::fromValue(para.curr_val));
    }
    else if (para_var.canConvert<ParaPath>())
    {
      ParaPath para = para_var.value<ParaPath>();
      para_info->setProperty(prop_name.toStdString().c_str(), QVariant::fromValue(para.curr_val));
    }
    else if (para_var.canConvert<ParaBool>())
    {
      ParaBool para = para_var.value<ParaBool>();
      para_info->setProperty(prop_name.toStdString().c_str(), QVariant::fromValue(para.curr_val));
    }
    else
    {
      LOG_ERROR("para_var covert fail: {}", prop_name.left(prop_name.size() - name_suffix.size()));
    }
  }

  return para_info;
}

bool WidgetParaSetting::compareProperties(const ParaInfo& _para_info) const
{
  QSet<QString> this_prop_set;
  const QMetaObject* meta_obj = this->metaObject();
  for (int i = 0; i < meta_obj->propertyCount(); ++i)
  {
    QMetaProperty meta_prop = meta_obj->property(i);
    QString prop_name       = meta_prop.name();
    QString name_suffix     = PARA_VAR_SUFFIX;
    if (!prop_name.endsWith(name_suffix))
    {
      continue;
    }
    this_prop_set.insert(prop_name.left(prop_name.size() - name_suffix.size()));
  }

  const QMetaObject* para_meta_obj = _para_info.metaObject();
  for (int i = 0; i < para_meta_obj->propertyCount(); ++i)
  {
    QMetaProperty meta_prop = para_meta_obj->property(i);
    QString prop_name       = meta_prop.name();
    QString name_suffix     = PARA_VAR_SUFFIX;
    if (!prop_name.endsWith(name_suffix))
    {
      continue;
    }
    QString para_name = prop_name.left(prop_name.size() - name_suffix.size());

    if (this_prop_set.find(para_name) != this_prop_set.end())
    {
      this_prop_set.remove(para_name);
    }
  }
  if (!this_prop_set.isEmpty())
  {
    LOG_ERROR("para info 与 widget 不匹配，widget 多余参数: ");
    for (const auto& prop_name : this_prop_set)
    {
      LOG_ERROR("[{}] not found in para info", prop_name);
    }
    return false;
  }
  return true;
}

void WidgetParaSetting::createPropertyContainers()
{
  const QMetaObject* meta_obj = this->metaObject();
  for (int i = 0; i < meta_obj->propertyCount(); ++i)
  {
    QMetaProperty meta_prop = meta_obj->property(i);
    QString prop_name       = meta_prop.name();
    QString name_suffix     = PARA_VAR_SUFFIX;
    if (!prop_name.endsWith(name_suffix))
    {
      continue;
    }
    QVariant para_var = meta_prop.read(this);
    if (!para_var.isValid())
    {
      LOG_INFO("para_var is invalid: {}", prop_name.left(prop_name.size() - name_suffix.size()));
      continue;
    }

    auto check_create_group_item = [this](const QString& _group_name) {
      if (group_item_map_.find(_group_name) == group_item_map_.end())
      {
        QTreeWidgetItem* group_item = new QTreeWidgetItem(tree_widget_);
        group_item->setText(0, _group_name);
        group_item_map_[_group_name] = group_item;
        group_item->setExpanded(true);
      }
    };

    if (para_var.canConvert<ParaInt>())
    {
      ParaInt para  = para_var.value<ParaInt>();
      para.curr_val = para.default_val;
      check_create_group_item(para.disp_group);
      QTreeWidgetItem* item = new QTreeWidgetItem(group_item_map_[para.disp_group]);
      item->setText(0, para.disp_name);
      tree_widget_->setColumnWidth(0, 200);
      para.container = new QSpinBox(this);
      para.container->setRange(para.min_val, para.max_val);
      para.container->setValue(para.curr_val);
      tree_widget_->setItemWidget(item, 1, para.container);
      meta_prop.write(this, QVariant::fromValue(para));
    }
    else if (para_var.canConvert<ParaPath>())
    {
      ParaPath para = para_var.value<ParaPath>();
      para.curr_val = para.default_val;
      check_create_group_item(para.disp_group);
      QTreeWidgetItem* item = new QTreeWidgetItem(group_item_map_[para.disp_group]);
      QWidget* widget       = new QWidget(this);
      item->setText(0, para.disp_name);
      para.container = new QLineEdit(this);
      para.container->setText(para.curr_val);
      QPushButton* button = new QPushButton("...", this);
      button->setMaximumWidth(30);
      connect(button, &QPushButton::clicked, [para, para_var, para_container = para.container]() {
        // QString file_name = QFileDialog::getOpenFileName();
        // 要求QFileDialog置顶在前面吧
        QString file_name = QFileDialog::getOpenFileName(nullptr, "选择文件", para_container->text());
        if (!file_name.isEmpty())
        {
          para_container->setText(file_name);
        }
      });
      QHBoxLayout* layout = new QHBoxLayout(this);
      layout->addWidget(para.container);
      layout->addWidget(button);
      widget->setLayout(layout);
      tree_widget_->setItemWidget(item, 1, widget);
      meta_prop.write(this, QVariant::fromValue(para));
    }
    else if (para_var.canConvert<ParaBool>())
    {
      ParaBool para = para_var.value<ParaBool>();
      para.curr_val = para.default_val;
      check_create_group_item(para.disp_group);
      QTreeWidgetItem* item = new QTreeWidgetItem(group_item_map_[para.disp_group]);
      item->setText(0, para.disp_name);
      tree_widget_->setColumnWidth(0, 200);
      para.container = new QCheckBox(this);
      para.container->setChecked(para.curr_val);
      tree_widget_->setItemWidget(item, 1, para.container);
      meta_prop.write(this, QVariant::fromValue(para));
    }
    else
    {
      LOG_ERROR("para_var covert fail: {}", prop_name.left(prop_name.size() - name_suffix.size()));
    }
  }
}

void WidgetParaSetting::updateCurrToWidgetValue()
{
  const QMetaObject* meta_obj = this->metaObject();
  for (int i = 0; i < meta_obj->propertyCount(); ++i)
  {
    QMetaProperty meta_prop = meta_obj->property(i);
    QString prop_name       = meta_prop.name();
    QString name_suffix     = PARA_VAR_SUFFIX;
    if (!prop_name.endsWith(name_suffix))
    {
      continue;
    }
    QVariant para_var = meta_prop.read(this);
    if (!para_var.isValid())
    {
      LOG_INFO("para_var is invalid: {}", prop_name.left(prop_name.size() - name_suffix.size()));
      continue;
    }

    if (para_var.canConvert<ParaInt>())
    {
      ParaInt para = para_var.value<ParaInt>();
      para.container->setValue(para.curr_val);
    }
    else if (para_var.canConvert<ParaDouble>())
    {
      ParaDouble para = para_var.value<ParaDouble>();
      para.container->setValue(para.curr_val);
    }
    else if (para_var.canConvert<ParaPath>())
    {
      ParaPath para = para_var.value<ParaPath>();
      para.container->setText(para.curr_val);
    }
    else if (para_var.canConvert<ParaBool>())
    {
      ParaBool para = para_var.value<ParaBool>();
      para.container->setChecked(para.curr_val);
    }
    else
    {
      LOG_ERROR("para_var covert fail: {}", prop_name.left(prop_name.size() - name_suffix.size()));
    }
  }
}
void WidgetParaSetting::updateWidgetToCurrValue()
{
  const QMetaObject* meta_obj = this->metaObject();
  for (int i = 0; i < meta_obj->propertyCount(); ++i)
  {
    QMetaProperty meta_prop = meta_obj->property(i);
    QString prop_name       = meta_prop.name();
    QString name_suffix     = PARA_VAR_SUFFIX;
    if (!prop_name.endsWith(name_suffix))
    {
      continue;
    }
    QVariant para_var = meta_prop.read(this);
    if (!para_var.isValid())
    {
      LOG_INFO("para_var is invalid: {}", prop_name.left(prop_name.size() - name_suffix.size()));
      continue;
    }

    if (para_var.canConvert<ParaInt>())
    {
      ParaInt para  = para_var.value<ParaInt>();
      para.curr_val = para.container->value();
      meta_prop.write(this, QVariant::fromValue(para));
    }
    else if (para_var.canConvert<ParaDouble>())
    {
      ParaDouble para = para_var.value<ParaDouble>();
      para.curr_val   = para.container->value();
      meta_prop.write(this, QVariant::fromValue(para));
    }
    else if (para_var.canConvert<ParaPath>())
    {
      ParaPath para = para_var.value<ParaPath>();
      para.curr_val = para.container->text();
      meta_prop.write(this, QVariant::fromValue(para));
    }
    else if (para_var.canConvert<ParaBool>())
    {
      ParaBool para = para_var.value<ParaBool>();
      para.curr_val = para.container->isChecked();
      meta_prop.write(this, QVariant::fromValue(para));
    }
    else
    {
      LOG_ERROR("para_var covert fail: {}", prop_name.left(prop_name.size() - name_suffix.size()));
    }
  }
}

void WidgetParaSetting::readParaInfoSettings()
{
  rsfsc_lib::RSFSCQSettings settings("RoboSense", QString(PROJECT_NAME) + "/widget_para_setting");
  settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
  const QMetaObject* meta_obj = this->metaObject();
  for (int i = 0; i < meta_obj->propertyCount(); ++i)
  {
    QMetaProperty meta_prop = meta_obj->property(i);
    QString prop_name       = meta_prop.name();
    QString name_suffix     = PARA_VAR_SUFFIX;
    if (!prop_name.endsWith(name_suffix))
    {
      continue;
    }
    QVariant para_var = meta_prop.read(this);
    QString para_name = prop_name.left(prop_name.size() - name_suffix.size());
    if (!para_var.isValid())
    {
      LOG_INFO("para_var is invalid: {}", para_name);
      continue;
    }

    if (para_var.canConvert<ParaInt>())
    {
      ParaInt para  = para_var.value<ParaInt>();
      para.curr_val = settings.value(para_name, para.default_val).toInt();
      meta_prop.write(this, QVariant::fromValue(para));
    }
    else if (para_var.canConvert<ParaDouble>())
    {
      ParaDouble para = para_var.value<ParaDouble>();
      para.curr_val   = settings.value(para_name, para.default_val).toDouble();
      meta_prop.write(this, QVariant::fromValue(para));
    }
    else if (para_var.canConvert<ParaPath>())
    {
      ParaPath para = para_var.value<ParaPath>();
      para.curr_val = settings.value(para_name, para.default_val).toString();
      meta_prop.write(this, QVariant::fromValue(para));
    }
    else if (para_var.canConvert<ParaBool>())
    {
      ParaBool para = para_var.value<ParaBool>();
      para.curr_val = settings.value(para_name, para.default_val).toBool();
      meta_prop.write(this, QVariant::fromValue(para));
    }
    else
    {
      LOG_ERROR("para_var covert fail: {}", para_name);
    }
  }
}
void WidgetParaSetting::writeParaInfoSettings()
{
  rsfsc_lib::RSFSCQSettings settings("RoboSense", QString(PROJECT_NAME) + "/widget_para_setting");
  settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
  const QMetaObject* meta_obj = this->metaObject();
  for (int i = 0; i < meta_obj->propertyCount(); ++i)
  {
    QMetaProperty meta_prop = meta_obj->property(i);
    QString prop_name       = meta_prop.name();
    QString name_suffix     = PARA_VAR_SUFFIX;
    if (!prop_name.endsWith(name_suffix))
    {
      continue;
    }
    QVariant para_var = meta_prop.read(this);
    QString para_name = prop_name.left(prop_name.size() - name_suffix.size());
    if (!para_var.isValid())
    {
      LOG_INFO("para_var is invalid: {}", para_name);
      continue;
    }

    if (para_var.canConvert<ParaInt>())
    {
      ParaInt para = para_var.value<ParaInt>();
      settings.setValue(para_name, para.curr_val);
    }
    else if (para_var.canConvert<ParaDouble>())
    {
      ParaDouble para = para_var.value<ParaDouble>();
      settings.setValue(para_name, para.curr_val);
    }
    else if (para_var.canConvert<ParaPath>())
    {
      ParaPath para = para_var.value<ParaPath>();
      settings.setValue(para_name, para.curr_val);
    }
    else if (para_var.canConvert<ParaBool>())
    {
      ParaBool para = para_var.value<ParaBool>();
      settings.setValue(para_name, para.curr_val);
    }
    else
    {
      LOG_ERROR("para_var covert fail: {}", para_name);
    }
  }
}

void WidgetParaSetting::readLayoutSettings()
{
  rsfsc_lib::RSFSCQSettings settings("RoboSense", QString(PROJECT_NAME) + "/widget_para_setting");
  settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
  settings.beginGroup("layout");
  restoreGeometry(settings.value("geometry").toByteArray());
  settings.endGroup();
}
void WidgetParaSetting::writeLayoutSettings()
{
  rsfsc_lib::RSFSCQSettings settings("RoboSense", QString(PROJECT_NAME) + "/widget_para_setting");
  settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
  settings.beginGroup("layout");
  settings.setValue("geometry", saveGeometry());
  settings.endGroup();
}

void WidgetParaSetting::closeEvent(QCloseEvent* _event)
{
  writeLayoutSettings();
  _event->accept();
}

ParaInfo::ParaInfo(QObject* _parent) : QObject(_parent) {};
ParaInfo::ParaInfo(const ParaInfo& _para_info) { copyProperties(&_para_info); }
ParaInfo::~ParaInfo() = default;
[[nodiscard]] std::shared_ptr<ParaInfo> ParaInfo::clone() const { return std::make_shared<ParaInfo>(*this); }

void ParaInfo::copyProperties(const ParaInfo* _source)
{
  const QMetaObject* meta_obj    = this->metaObject();
  const QMetaObject* source_meta = _source->metaObject();
  for (int i = 0; i < meta_obj->propertyCount(); ++i)
  {
    QString prop_name   = meta_obj->property(i).name();
    QString name_suffix = PARA_VAR_SUFFIX;
    if (!prop_name.endsWith(name_suffix))
    {
      continue;
    }
    QMetaProperty property = meta_obj->property(i);
    if (property.isWritable() && source_meta->indexOfProperty(property.name()) != -1)
    {
      property.write(this, property.read(_source));
    }
  }
}

ParaInfo* ParaInfo::clone(QObject* _parent) const
{
  ParaInfo* para_info = new ParaInfo(_parent);
  para_info->copyProperties(this);
  return para_info;
}
void ParaInfo::clone(const ParaInfo& _para_info) { copyProperties(&_para_info); }
void ParaInfo::clone(const ParaInfo* _para_info)
{
  if (_para_info != nullptr)
  {
    copyProperties(_para_info);
    return;
  }
  LOG_ERROR("para info拷贝失败, 源对象为空");
}
void ParaInfo::clone(std::shared_ptr<ParaInfo> _para_info)
{
  auto para_info = std::move(_para_info);
  if (para_info != nullptr)
  {
    copyProperties(para_info.get());
    return;
  }
  LOG_ERROR("para info拷贝失败,shared_ptr 源对象为空");
}

}  // namespace lidar
}  // namespace robosense