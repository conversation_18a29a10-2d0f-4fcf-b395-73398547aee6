﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include "rsfsc_lib/ui/widget_about.h"
#include "widgets/label_test_state.h"
#include "widgets/widget_mes.h"
#include "widgets/widget_para_setting.h"
#include <QMainWindow>

QT_BEGIN_NAMESPACE
namespace Ui
{
class MainWindow;
}  // namespace Ui
QT_END_NAMESPACE
// NOLINTNEXTLINE
namespace robosense
{
namespace lidar
{
class AppController;
}  // namespace lidar
}  // namespace robosense

class MainWindow : public QMainWindow
{
  Q_OBJECT
public:
  friend class robosense::lidar::AppController;
  explicit MainWindow(QWidget* _parent = nullptr);
  MainWindow(const MainWindow&) = delete;
  MainWindow& operator=(const MainWindow&) = delete;
  MainWindow(MainWindow&&)                 = delete;
  MainWindow& operator=(MainWindow&&) = delete;
  ~MainWindow() override;

  struct LidarInfoStruct
  {
    robosense::lidar::rsfsc_lib::WidgetLidarInfo* widget_lidar_info;
    robosense::lidar::rsfsc_lib::WidgetResultTable* widget_result_table;
    robosense::lidar::LabelTestState* label_test_state;
    QPushButton* button_connect;
    QPushButton* button_open_data_folder;
  };

protected:
  void closeEvent(QCloseEvent* _event) override;
  void readSettings();
  void writeSettings();

public Q_SLOTS:
  void slotFsmStarting(const int _index);
  void slotFsmStarted(const int _index);
  void slotFsmStopping(const int _index);
  void slotFsmStopped(const int _index);
  void slotShowErrorMessageBox(const QString& _msg);

  // void slotButtonConnectClicked(const int _index);
  // void slotButtonDisconnectClicked(const int _index);
  // void slotButtonOpenDataFolderClicked(const int _index);

  // lidar connection
  void slotLidarConnecting(const int _lidar_index);
  void slotLidarConnected(const int _lidar_index);
  void slotLidarDisconnecting(const int _lidar_index);
  void slotLidarDisconnected(const int _lidar_index);

  void slotRefreshPortName();

Q_SIGNALS:
  void signalMainwindowInitFinished();

private:
  void initWorkModel();
  void initMesWidget();
  void initWidgets();
  static void initCsvLimit();
  void initSignalSlots();

  void updateModelParaInfo();

  void initFsm();
  // void startUpFsm();
  // void shutDownFsm();
  Ui::MainWindow* ui_;
  robosense::lidar::WidgetAbout* widget_about_              = nullptr;
  robosense::lidar::WidgetParaSetting* widget_para_setting_ = nullptr;
  std::map<int, LidarInfoStruct> lidar_info_map_;
};
#endif  // MAINWINDOW_H
