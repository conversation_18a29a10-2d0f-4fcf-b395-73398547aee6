#ifndef RSFSC_TCP_CLIENT_H
#define RSFSC_TCP_CLIENT_H

#include "rsfsc_comm_client/rsfsc_comm_client.h"
#include <boost/asio/ip/tcp.hpp>

namespace robosense
{
namespace lidar
{

class RsfscTcpClient : public RsfscCommClient
{
public:
  explicit RsfscTcpClient();
  explicit RsfscTcpClient(const TcpClientConfig& _tcp_config);
  explicit RsfscTcpClient(const std::string& _host, const uint16_t _port);
  ~RsfscTcpClient() override;

  bool connect() override;
  bool disconnect() override;
  [[nodiscard]] bool isConnected() const override;

  std::size_t write(const std::vector<uint8_t>& _data) override;
  std::size_t readSome(std::vector<uint8_t>& _data) override;

  std::vector<uint8_t> readSomeTimeout(uint32_t _timeout_ms) override;

  void asyncWrite(const std::vector<uint8_t>& _data, WriteHandler _handler) override;
  void asyncRead(std::vector<uint8_t>& _data, ReadHandler _handler) override;

  std::vector<uint8_t> request(const std::vector<uint8_t>& _request, uint32_t _timeout_ms) override;

  void abort() override;
  [[nodiscard]] bool isAborted() const override;
  void resetAbort() override;

private:
  bool setClientOptions();

  std::string host_;
  uint16_t port_;
  uint32_t connect_timeout_ms_;
  std::string msg_header_;
  std::unique_ptr<boost::asio::ip::tcp::socket> socket_;
  boost::asio::ip::tcp::endpoint endpoint_;
  bool is_connected_ = false;
};

}  // namespace lidar
}  // namespace robosense

#endif  // RSFSC_TCP_CLIENT_H
