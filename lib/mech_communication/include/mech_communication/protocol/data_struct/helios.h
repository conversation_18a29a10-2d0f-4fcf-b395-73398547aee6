﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef HELIOS_DATA_STRUCT_H
#define HELIOS_DATA_STRUCT_H

#include <array>
#include <cstdint>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

namespace helios
{
#pragma pack(push, 1)
using Tail = struct Tail
{
  std::array<uint8_t, 4> reserved_data1;
  std::array<uint8_t, 2> line_end;
};

struct BigEndianU16
{
  uint8_t high;
  uint8_t low;
  [[nodiscard]] uint16_t toHost() const
  {
    return static_cast<uint16_t>(static_cast<uint16_t>(high << 8U) + static_cast<uint16_t>(low));
  }
};

struct ADCPoint
{
  std::array<uint8_t, 3> point_array;
};

constexpr std::array<uint8_t, 2> BLOCK_HEADER_FLAG = { 0xff, 0xee };
constexpr std::array<uint8_t, 2> LINE_END_FLAG     = { 0x00, 0xff };
constexpr std::array<uint8_t, 8> MSOP_HEADER_FLAG  = { 0x55, 0xaa, 0x05, 0x5a, 0x00, 0x01, 0x00, 0x00 };

using DataBlock = struct DataBlock
{
  std::array<uint8_t, 2> block_header1;
  BigEndianU16 azimuth1;
  BigEndianU16 fixed_data1;
  uint8_t fixed_data2;
  BigEndianU16 area;
  uint8_t max_amp;
  uint8_t sample_gap;
  BigEndianU16 fixed_data3;
  std::array<ADCPoint, 29> adc_point_array1;
  std::array<uint8_t, 2> block_header2;
  BigEndianU16 azimuth2;
  BigEndianU16 distance;
  uint8_t channel;
  BigEndianU16 gain;
  uint8_t reflect;
  std::array<ADCPoint, 30> adc_point_array2;
};

using DataBlock2 = struct DataBlock2
{
  std::array<uint8_t, 2> block_header2;
  BigEndianU16 azimuth2;
  BigEndianU16 distance;
  uint8_t channel;
  BigEndianU16 gain;
  uint8_t reflect;
  std::array<ADCPoint, 30> adc_point_array2;
  std::array<uint8_t, 2> block_header1;
  BigEndianU16 azimuth1;
  BigEndianU16 fixed_data1;
  uint8_t fixed_data2;
  BigEndianU16 area;
  uint8_t max_amp;
  uint8_t sample_gap;
  BigEndianU16 fixed_data3;
  std::array<ADCPoint, 29> adc_point_array1;
};

using DataPacket = struct DataPacket
{
  std::array<DataBlock, 6> data_blocks;
};

using DataPacket2 = struct DataPacket2
{
  std::array<DataBlock2, 6> data_blocks;
};

using Header = struct Header
{
  std::array<uint8_t, 8> base_header;
  std::array<uint8_t, 4> top_down_package;
  uint32_t bot_upload_package;
  std::array<uint8_t, 4> reserved_data1;
  std::array<uint8_t, 6> timestamp_s;
  uint32_t timestamp_ns;
  std::array<uint8_t, 12> reserved_data2;
};

using MsopDataPacket = struct MsopDataPacket
{
  Header pkt_head;
  DataPacket data_packet;
  Tail tail;
};

using MsopDataPacket2 = struct MsopDataPacket2
{
  Header pkt_head;
  DataPacket2 data_packet;
  Tail tail;
};
#pragma pack(pop)

constexpr uint32_t FRAME_FLAG = 0x55AA2552;

constexpr uint32_t NET_CMD_BEGIN               = 0x000;
constexpr uint32_t NET_CMD_READ_REGISTER       = 0x001;
constexpr uint32_t NET_CMD_WRITE_REGISTER      = 0x002;
constexpr uint32_t NET_CMD_TOP_BIN_UPDATE      = 0x003;
constexpr uint32_t NET_CMD_TOP_BACKUP_UPDATE   = 0x004;
constexpr uint32_t NET_CMD_BOT_BIN_UPDATE      = 0x005;
constexpr uint32_t NET_CMD_BOT_BACKUP_UPDATE   = 0x006;
constexpr uint32_t NET_CMD_LINUX_APP_UPDATE    = 0x007;
constexpr uint32_t NET_CMD_LAPP_BACKUP_UPDATE  = 0x008;
constexpr uint32_t NET_CMD_READ_CONFIG         = 0x009;
constexpr uint32_t NET_CMD_WRITE_CONFIG        = 0x00A;
constexpr uint32_t NET_CMD_TOP_PARA_UPDATE     = 0x00B;
constexpr uint32_t NET_CMD_BOT_PARA_UPDATE     = 0x00C;
constexpr uint32_t NET_CMD_TOP_FLASH_READ      = 0x00D;
constexpr uint32_t NET_CMD_TOP_FLASH_WRITE     = 0x00E;
constexpr uint32_t NET_CMD_TOP_READ_REGISTER   = 0x00F;
constexpr uint32_t NET_CMD_TOP_WRITE_REGISTER  = 0x010;
constexpr uint32_t NET_CMD_TOP_GET_INTENSITY   = 0x011;
constexpr uint32_t NET_CMD_ZERO_ANGLE_READ     = 0x012;
constexpr uint32_t NET_CMD_ZERO_ANGLE_WRITE    = 0x013;
constexpr uint32_t NET_CMD_CHANNEL_ANGLE_READ  = 0x014;
constexpr uint32_t NET_CMD_CHANNEL_ANGLE_WRITE = 0x015;
constexpr uint32_t NET_CMD_MOTOR_UPDATE        = 0x016;
constexpr uint32_t NET_CMD_MOTOR_SEND          = 0x017;
constexpr uint32_t NET_CMD_MOTOR_RECV          = 0x018;
constexpr uint32_t NET_CMD_CODE_WHEEL_CALI     = 0x019;
#ifdef _M_RUBY_4_0_
#  define NET_CMD_TOP_HIGH_LOW_INTENSITY 0x019
#endif
constexpr uint32_t NET_CMD_INSPECTION              = 0x020;
constexpr uint32_t NET_CMD_CLEAR_BOT_CONFIG        = 0x021;
constexpr uint32_t NET_CMD_EYES_SAFE               = 0x022;
constexpr uint32_t NET_CMD_ENCODER_CURING          = 0x023;
constexpr uint32_t NET_CMD_WAVE_SHOW               = 0x024;
constexpr uint32_t NET_CMD_CODE_WHEEL_SAVE         = 0x025;
constexpr uint32_t NET_CMD_GET_MONITOR_PARAM       = 0x026;
constexpr uint32_t NET_CMD_END                     = 0x050;
constexpr uint32_t NET_CMD_ACK_BEGIN               = 0x100;
constexpr uint32_t NET_CMD_ACK_READ_REGISTER       = 0x101;
constexpr uint32_t NET_CMD_ACK_WRITE_REGISTER      = 0x102;
constexpr uint32_t NET_CMD_ACK_TOP_BIN_UPDATE      = 0x103;
constexpr uint32_t NET_CMD_ACK_TOP_BACKUP_UPDATE   = 0x104;
constexpr uint32_t NET_CMD_ACK_BOT_BIN_UPDATE      = 0x105;
constexpr uint32_t NET_CMD_ACK_BOT_BACKUP_UPDATE   = 0x106;
constexpr uint32_t NET_CMD_ACK_LINUX_APP_UPDATE    = 0x107;
constexpr uint32_t NET_CMD_ACK_LAPP_BACKUP_UPDATE  = 0x108;
constexpr uint32_t NET_CMD_ACK_READ_CONFIG         = 0x109;
constexpr uint32_t NET_CMD_ACK_WRITE_CONFIG        = 0x10A;
constexpr uint32_t NET_CMD_ACK_TOP_PARA_UPDATE     = 0x10B;
constexpr uint32_t NET_CMD_ACK_BOT_PARA_UPDATE     = 0x10C;
constexpr uint32_t NET_CMD_ACK_TOP_FLASH_READ      = 0x10D;
constexpr uint32_t NET_CMD_ACK_TOP_FLASH_WRITE     = 0x10E;
constexpr uint32_t NET_CMD_ACK_TOP_READ_REGISTER   = 0x10F;
constexpr uint32_t NET_CMD_ACK_TOP_WRITE_REGISTER  = 0x110;
constexpr uint32_t NET_CMD_ACK_TOP_GET_INTENSITY   = 0x111;
constexpr uint32_t NET_CMD_ACK_ZERO_ANGLE_READ     = 0x112;
constexpr uint32_t NET_CMD_ACK_ZERO_ANGLE_WRITE    = 0x113;
constexpr uint32_t NET_CMD_ACK_CHANNEL_ANGLE_READ  = 0x114;
constexpr uint32_t NET_CMD_ACK_CHANNEL_ANGLE_WRITE = 0x115;
constexpr uint32_t NET_CMD_ACK_MOTOR_UPDATE        = 0x116;
constexpr uint32_t NET_CMD_ACK_MOTOR_SEND          = 0x117;
constexpr uint32_t NET_CMD_ACK_MOTOR_RECV          = 0x118;
constexpr uint32_t NET_CMD_ACK_WHEEL_CALI          = 0x119;
#ifdef _M_RUBY_4_0_
#  define NET_CMD_TOP_HIGH_LOW_INTENSITY 0x019
#endif
constexpr uint32_t NET_CMD_ACK_INSPECTION        = 0x120;
constexpr uint32_t NET_CMD_ACK_CLEAR_BOT_CONFIG  = 0x121;
constexpr uint32_t NET_CMD_ACK_EYES_SAFE         = 0x122;
constexpr uint32_t NET_CMD_ACK_ENCODER_CURING    = 0x123;
constexpr uint32_t NET_CMD_ACK_WAVE_SHOW         = 0x124;
constexpr uint32_t NET_CMD_ACK_CODE_WHEEL_SAVE   = 0x125;
constexpr uint32_t NET_CMD_ACK_GET_MONITOR_PARAM = 0x126;
constexpr uint32_t NET_CMD_ACK_END               = 0x150;

constexpr uint32_t GS_FRAME_FLAG = 0x55AA2552;

constexpr std::array<uint8_t, 4> MSOP_FRAME_FLAG = { 0x55, 0xaa, 0x05, 0x5a };

constexpr uint32_t CH_SEL = 0x2600;

constexpr uint32_t TX_EN_A1_A8  = 0x1103;
constexpr uint32_t TX_EN_A9_A16 = 0x1104;
constexpr uint32_t TX_EN_B1_B8  = 0x1105;
constexpr uint32_t TX_EN_B9_B16 = 0x1106;

constexpr uint32_t RX_EN_A1_A8  = 0x1113;
constexpr uint32_t RX_EN_A9_A16 = 0x1114;
constexpr uint32_t RX_EN_B1_B8  = 0x1115;
constexpr uint32_t RX_EN_B9_B16 = 0x1116;

constexpr uint32_t DATA_LOCK  = 0x2601;
constexpr uint32_t AREA_GDI_H = 0x2612;
constexpr uint32_t AREA_GDI_L = 0x2613;
constexpr uint32_t DIST_GDI_H = 0x2614;
constexpr uint32_t DIST_GDI_L = 0x2615;
constexpr uint32_t TMP_H      = 0x301f;
constexpr uint32_t TMP_L      = 0x3020;

constexpr uint32_t CHARGE_FIX_EN    = 0x1208;
constexpr uint32_t CHARGE_FIX_VALUE = 0x1209;

constexpr uint32_t PW_MID_H = 0x1219;
constexpr uint32_t PW_MID_L = 0x121a;

constexpr uint32_t VGA_FIX_ADDR_EN = 0x120a;
constexpr uint32_t VGA_FIX_ADDR    = 0x120b;

constexpr uint32_t GAIN_ADDR   = 0x1220;
constexpr uint32_t GAIN_ADDR_L = 0x1221;
constexpr uint32_t GAIN_ADDR_H = 0x1222;

#pragma pack(push, 1)
struct MsopPacket
{
  std::array<uint8_t, 4> frame_flag;
  uint16_t protocol_version;
  uint16_t reserve0;
  uint32_t top_download_count;
  uint32_t bot_upload_count;
  uint8_t reserve1;

  uint8_t accuracy;

  uint16_t angle_pulse;

  std::array<uint8_t, 10> timestamp;  // 20
  uint8_t reserve2;
  uint8_t lidar_type;
  uint8_t lidar_model;

  std::array<uint8_t, 9> reserve3;

  std::array<uint8_t, 1200> data;

  uint32_t reserve4;
  uint16_t frame_tail;
};
#pragma pack(pop)

using FrameHead = struct FrameHead
{
  uint32_t frame_flag;
  uint32_t length;
  uint32_t cmd;
  uint32_t check_sum;
};

inline uint16_t checkSum(FrameHead _frame_head)
{
  uint32_t sum = 0;

  sum += _frame_head.frame_flag & 0xFFFFU;
  sum += (_frame_head.frame_flag >> 16U) & 0xFFFFU;

  sum += _frame_head.length & 0xFFFFU;
  sum += (_frame_head.length >> 16U) & 0xFFFFU;

  sum += _frame_head.cmd & 0xFFFFU;
  sum += (_frame_head.cmd >> 16U) & 0xFFFFU;

  sum = (sum >> 16U) + (sum & 0xFFFFU);

  return static_cast<uint16_t>(~sum);
}

struct RegisterData
{
  uint32_t address;
  uint32_t value;
};

using LidarPara = struct LidarPara
{
  // sizeof(struct lidarPara) = 256 Bytes
  uint16_t motor_speed;       // res[0]
  uint16_t motor_phase_lock;  // res[1]
  uint16_t time_sync_mode;    // res[2]
  uint16_t start_fov;         // res[3]
  uint16_t end_fov;           // res[4]
  uint16_t wave_mode;         // res[5]

  uint32_t top_version;       // res[6, 7]
  uint32_t bot_version;       // res[8, 9]
  uint32_t app_version;       // res[10,11]
  uint32_t motor_version;     // res[12,13]
  uint32_t top_version_time;  // res[14,15]

  uint16_t temperature;                 // res[16]
  uint16_t motor_real_time_speed;       // res[17]
  uint16_t motor_real_time_phase_lock;  // res[18]
  uint16_t phase_lock_status;           // res[19]
  uint16_t disk_calib_status;           // res[20]
  uint16_t angle_pulse;                 // res[21]

  uint32_t web_version;  // res[22, 23]
};

using UnionParam = union UnionParam
{
  LidarPara lidar_para;
  std::array<uint16_t, 128> res;
};

using ConfigPara = struct ConfigPara
{
  std::array<uint8_t, 6> sn;
  std::array<uint8_t, 6> mac;

  std::array<uint8_t, 4> ip_local;
  std::array<uint8_t, 4> ip_remote;

  uint16_t msop_port;
  uint16_t difop_port;

  UnionParam param;

  std::array<uint8_t, 4> netmask_local;
  std::array<uint8_t, 4> gateway_local;
};

}  // namespace helios

}  // namespace lidar
}  // namespace robosense
#endif  // HELIOS_DATA_STRUCT_H