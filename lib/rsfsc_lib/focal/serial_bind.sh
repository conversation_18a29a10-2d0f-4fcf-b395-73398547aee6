#!/bin/bash

#################################################################################
# Copyright 2022 RoboSense All rights reserved.
# Suteng Innovation Technology Co., Ltd. www.robosense.ai

# This software is provided to you directly by RoboSense and might
# only be used to access RoboSense LiDAR. Any compilation,
# modification, exploration, reproduction and redistribution are
# restricted without RoboSense's prior consent.

# THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
# WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
# OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
# INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON>Q<PERSON><PERSON>IAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
# HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
# STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
# IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.
#################################################################################
# @brief   多个串口设备在同一台电脑时的连接设置
# <AUTHOR>
# @version 2022-09-21
# @details 更多的介绍，请看 http://gitlab.robosense.cn/system_release/factory_tool/useful_skill 工程中的介绍

echo "本脚本可用于绑定多个串口设备至固定的rsUSB*端口，以解决Ubuntu下ttyUSB*经常变的问题，本脚本属于RoboSense版权所有"
echo -e "注意：\n1. 请确保主软件支持进行串口绑定设置"
echo "2. 更换不同型号转接线，或更换了插入位置，需要重新设置"
echo "3. 请注意按下述提示完成操作"

read -p $'\n请输入要配置的串口数量,并按下回车继续\n' num
if [[ ! "$num" =~ ^[0-9]+$ ]]; then
    echo "输入错误"
    exit -1
fi
read -p $'请确保没有串口设备插到电脑上\n然后输入回车进入下一步\n' confirm

if [ "$confirm" == "" ]; then
    mkdir -p /tmp/rs_serial_bind
    cd /tmp/rs_serial_bind
    sudo rm -f /etc/udev/rules.d/80-rsUSB.rules
    sudo rm -f /etc/udev/rules.d/rsUSB*.rules
    echo $password | sudo -p "" -S touch /etc/udev/rules.d/80-rsUSB.rules
    sudo chmod +666 /etc/udev/rules.d/80-rsUSB.rules
    touch usb1.txt
    touch usb2.txt
    lsusb >usb1.txt
    echo "请按顺序插入串口设备,记住插入的顺序"
    IFS_old=$IFS # 记录老的分隔符
    IFS=$'\n'    # 以换行符作为分隔符
    ttyusb_num=-1
    ttyacm_num=-1
    all_num=0
    while [ $((all_num)) -lt ${num} ]; do
        lsusb >usb2.txt
        #得到usb2和usb1的区别
        l1=0
        l2=0
        for line1 in $(cat usb1.txt); do
            l1=$(($l1 + 1))
        done
        for line2 in $(cat usb2.txt); do
            l2=$(($l2 + 1))
        done

        if [ $l1 -ne $l2 ]; then #usb2和usb1不同时记录信息
            touch diff.txt
            touch info.txt
            for line in $(cat usb1.txt); do
                grep -v "$line" usb2.txt >diff.txt
                cat diff.txt >usb2.txt
            done
            id_vendor=$(grep -o "[0-9a-z]\{4\}:[0-9a-z]\{4\}" diff.txt | cut -d ':' -f 1)
            id_product=$(grep -o "[0-9a-z]\{4\}:[0-9a-z]\{4\}" diff.txt | cut -d ':' -f 2)
            echo "$id_vendor:$id_product"
            ttyusb_num=$(($ttyusb_num + 1))
            udevadm info --attribute-walk --name=/dev/ttyUSB$ttyusb_num > info.txt 2>/dev/null
            if [ $? -ne 0 ];then
                ttyusb_num=$(($ttyusb_num - 1))
                ttyacm_num=$(($ttyacm_num + 1))
                udevadm info --attribute-walk --name=/dev/ttyACM$ttyacm_num > info.txt 2>/dev/null
            fi

            if [ $? -ne 0 ] || [ "$id_vendor" == "" ] || [ "$id_product" == "" ]; then
                echo -e "\033[31m 检测出错，可能的原因： \033[0m"
                echo -e "\033[31m 1. 插入的设备不是ttyUSB或ttyACM串口，或已损坏 \033[0m"
                echo -e "\033[31m 2. 拔出了已经存在的串口设备 \033[0m"
                echo -e "\033[31m 3. 其他未知原因  \033[0m"
                echo -e "\033[31m 请确认上述情况，并重新运行 \033[0m"
                IFS=$IFS_old # 分隔符改回去，不影响后续使用
                sudo rm -f /etc/udev/rules.d/80-rsUSB.rules
                rm -r /tmp/rs_serial_bind
                read -n 1 -p $'请按任意键退出...' confirm
                exit -1
            fi

            serial=$(grep -o "ATTRS{serial}==\"[0-9A-Za-z]\{5,\}\"" info.txt)
            kernel=$(grep -o "KERNEL==\"tty[A-Z]*" info.txt)
            echo "$kernel*\", ATTRS{idVendor}==\"$id_vendor\", ATTRS{idProduct}==\"$id_product\", "$serial", MODE:=\"0666\", SYMLINK+=\"rsUSB${all_num}\"" >>/etc/udev/rules.d/80-rsUSB.rules
            echo "完成了rsUSB$all_num的插入设置"
            all_num=$(($all_num + 1))
            rm -f diff.txt
            rm -f info.txt
            lsusb >./usb1.txt
        fi
    done
    IFS=$IFS_old # 分隔符改回去，不影响后续使用
    sudo service udev reload
    sudo service udev restart
    echo -e "已完成所有串口设置，请用rsUSB0、rsUSB1、rsUSB2...(插入设备的顺序)代替ttyUSB0(或ttyACM0)、ttyUSB1、ttyUSB2...\n无需在前面加/dev/\n重启完成配置"
    rm -r /tmp/rs_serial_bind
fi
read -n 1 -p $'请按任意键退出...' confirm
exit 0
