#!/bin/bash
function checkFileIfNotExit(){
	if [ ! -e $1 ];then
		echo $1"不存在，请确认该文件存在后再试！"
		exit
	fi
}
#替换成你自己的应用名字

#APP_NAME="mes_example"
APP_NAME=$1
echo "Program: "$APP_NAME
APP_SYM_FILE=$APP_NAME".sym"
APP_FILE_PATH="./"$APP_NAME

checkFileIfNotExit $APP_FILE_PATH

CMD_DUMP_SYM_FILE="./tools/dump_syms"
CMD_DUMP_STACK_FILE="./tools/minidump_stackwalk"
PARSE_RESULT_SUFFIX="_parse_result.txt"

checkFileIfNotExit $CMD_DUMP_SYM_FILE
checkFileIfNotExit $CMD_DUMP_STACK_FILE

echo “/*********** 程序文件转sym文件开始执行，可能需要等待几十秒到几分钟..... ************/”
trans=`$CMD_DUMP_SYM_FILE $APP_FILE_PATH > $APP_SYM_FILE`
# echo "result=$?"
result=$?
echo "result=$result"
if [ $result -ne 0 ];then
	# echo $trans
	echo "执行"$CMD_DUMP_SYM_FILE"报错：程序文件转sym文件失败，具体信息请看上面的错误信息，请根据错误信息解决问题后再试！"
	exit
fi
echo “/*********** 程序文件转sym文件结束 ************/”
head1=`head -n1 $APP_SYM_FILE`
#取head1中被空格分割的第四个参数
symbol_num=`echo $head1 | awk '{print $4}'`
echo $symbol_num

symbols_dir="./symbols/"$APP_NAME"/"$symbol_num
mkdir -p $symbols_dir
mv $APP_SYM_FILE $symbols_dir


for file in `ls ./dmp/*.dmp`
do
	echo $file
	fileName=$file
	$CMD_DUMP_STACK_FILE $file ./symbols > $fileName$PARSE_RESULT_SUFFIX

done

echo "/******* dmp文档崩溃信息解析结果保存在下面文件中：*******/"
for file in `ls ./dmp/*.dmp`
do
	if echo "$file" | grep -q -E '\.dmp$'
	then
		fileName=${file}
		echo $fileName$PARSE_RESULT_SUFFIX
	fi
done


