﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef DATA_STRUCT_H
#define DATA_STRUCT_H

#include "mech_communication/protocol/data_struct/mech.h"
#include "rsfsc_log/rsfsc_log.h"
#include <QHostAddress>
#include <QObject>
#include <QString>
#include <QVariant>
#include <qdatetime.h>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
enum class ActionState
{
  STATE_CHECK_ALL_STATE,
  STATE_WRITE_SN,
  STATE_CONNECT_LIDAR,
  STATE_INIT_LIDAR,
  STATE_CHECK_DIFOP,
  STATE_CHECK_MSOP,
  STATE_CHECK_LIGHT_ERROR,
  STATE_FAIL,
  STATE_ABORT,
  STATE_SUCCESS,
  STATE_FINAL,
  STATE_END = -1
};
enum class TestState
{
  NOT_START = 0,
  RUNNING,
  PASS,
  ABORT,
  FAILED
};
}  // namespace lidar
}  // namespace robosense

#endif  // DATA_STRUCT_H
