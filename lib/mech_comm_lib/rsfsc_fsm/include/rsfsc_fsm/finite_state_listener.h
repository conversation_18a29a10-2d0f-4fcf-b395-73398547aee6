﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef FINITE_STATE_LISTENER_H
#define FINITE_STATE_LISTENER_H

#include "rsfsc_utils/decl_name.h"

namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

enum class ActionState;

/**
 * @class FiniteStateListener 
 * @brief 状态监听器
 * @note 当状态机状态发生改变时,通知所有状态监听器
 */
class FiniteStateListener
{
public:
  DECL_CLASSNAME(FiniteStateListener);

  explicit FiniteStateListener(const ActionState _state) : state_(_state) {};
  virtual ~FiniteStateListener()                  = default;
  FiniteStateListener(const FiniteStateListener&) = default;
  FiniteStateListener& operator=(const FiniteStateListener&) = default;
  FiniteStateListener(FiniteStateListener&&)                 = default;
  FiniteStateListener& operator=(FiniteStateListener&&) = default;
  virtual void onStateChanged()                         = 0;

  [[nodiscard]] ActionState getState() const { return state_; }

private:
  ActionState state_ = static_cast<ActionState>(-1);
};

}  // namespace lidar
}  // namespace robosense

#endif  // FINITE_STATE_LISTENER_H
