﻿cmake_minimum_required(VERSION 3.10)

project(
  rsfsc_fsm
  VERSION 1.0.0
  LANGUAGES CXX)

# 创建INTERFACE库
add_library(rsfsc_fsm INTERFACE)
add_library(rsfsc::fsm ALIAS rsfsc_fsm)

# 获取所有源文件
file(GLOB_RECURSE SOURCE_FILES CONFIGURE_DEPENDS src/*.cpp)
target_sources(rsfsc_fsm INTERFACE ${SOURCE_FILES})

# 设置包含目录
target_include_directories(rsfsc_fsm INTERFACE $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
                                               $<INSTALL_INTERFACE:include>)

# 设置依赖
target_link_libraries(rsfsc_fsm INTERFACE Qt5::Core Qt5::Widgets rsfsc::utils)
