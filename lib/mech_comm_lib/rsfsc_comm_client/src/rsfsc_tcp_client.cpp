#include "rsfsc_comm_client/rsfsc_tcp_client.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <boost/asio/ip/tcp.hpp>
#include <boost/asio/steady_timer.hpp>
#include <chrono>
#include <future>

namespace robosense
{
namespace lidar
{
RsfscTcpClient::RsfscTcpClient() : port_(0), connect_timeout_ms_(35000), socket_(nullptr) {}
RsfscTcpClient::RsfscTcpClient(const TcpClientConfig& _tcp_config) :
  host_(_tcp_config.host),
  port_(_tcp_config.port),
  connect_timeout_ms_(_tcp_config.connect_timeout_ms),
  socket_(nullptr)
{
  CommClientConfig config;
  config.tcp_config.host               = _tcp_config.host;
  config.tcp_config.port               = _tcp_config.port;
  config.tcp_config.connect_timeout_ms = _tcp_config.connect_timeout_ms;
  setConfig(config);
}
RsfscTcpClient::RsfscTcpClient(const std::string& _host, const uint16_t _port) :
  host_(_host), port_(_port), connect_timeout_ms_(35000), socket_(nullptr)
{
  CommClientConfig config;
  config.tcp_config.host               = _host;
  config.tcp_config.port               = _port;
  config.tcp_config.connect_timeout_ms = 35000;
  setConfig(config);
}

RsfscTcpClient::~RsfscTcpClient() { disconnect(); }

bool RsfscTcpClient::connect()
{
  auto config         = getConfig();
  host_               = config.tcp_config.host;
  port_               = config.tcp_config.port;
  connect_timeout_ms_ = config.tcp_config.connect_timeout_ms;
  msg_header_         = fmt::format("RsfscTcpClient [{}:{}] :: ", host_, port_);
  resetAbort();
  try
  {
    startIoContext();
    socket_   = std::make_unique<boost::asio::ip::tcp::socket>(ioContext());
    endpoint_ = boost::asio::ip::tcp::endpoint(boost::asio::ip::address::from_string(host_), port_);
    // 超时取消连接
    // 利用 promise/future 机制获取异步连接结果
    std::promise<boost::system::error_code> connect_promise;
    auto connect_future = connect_promise.get_future();
    boost::asio::steady_timer timer(ioContext());
    bool timed_out = false;
    timer.expires_after(std::chrono::milliseconds(connect_timeout_ms_));

    timer.async_wait([this, &timed_out](const boost::system::error_code& _ec) {
      if (!_ec)
      {
        timed_out = true;
        socket_->cancel();
      }
    });

    // 发起异步连接操作
    socket_->async_connect(
      endpoint_, [&connect_promise](const boost::system::error_code& _ec) { connect_promise.set_value(_ec); });

    // 等待连接结果
    auto err_code = connect_future.get();
    timer.cancel();

    if (timed_out)
    {
      LOG_INDEX_ERROR(msg_header_ + "连接超时，等待时间：{0}s", static_cast<float>(connect_timeout_ms_) / 1000.F);
      disconnect();
      return false;
    }

    if (err_code)
    {
      LOG_INDEX_ERROR(msg_header_ + "连接失败，错误信息：" + err_code.message());
      disconnect();
      return false;
    }

    is_connected_ = true;
    LOG_INDEX_DEBUG(msg_header_ + "连接成功");
    setClientOptions();
    return true;
  }
  catch (const std::exception& e)
  {
    socket_.reset();
    is_connected_ = false;
    LOG_INDEX_ERROR(msg_header_ + "连接失败，错误信息：" + e.what());
    return false;
  }
}

bool RsfscTcpClient::disconnect()
{
  abort();
  if (is_connected_)
  {
    boost::system::error_code error_code;
    // 尝试取消操作
    auto error_code_ret = socket_->cancel(error_code);
    if (error_code || error_code_ret)
    {
      LOG_INDEX_ERROR(msg_header_ + " -> cancel failed: " + error_code.message());
    }
    // 执行 shutdown 操作，并传递 error_code 来捕获错误
    error_code_ret = socket_->shutdown(boost::asio::ip::tcp::socket::shutdown_both, error_code);
    if (error_code || error_code_ret)
    {
      LOG_INDEX_ERROR(msg_header_ + " -> shutdown failed: " + error_code.message());
    }
    error_code_ret = socket_->close(error_code);
    if (error_code || error_code_ret)
    {
      LOG_INDEX_ERROR(msg_header_ + " -> close failed: " + error_code.message());
    }
    stopIoContext();
    is_connected_ = false;
  }
  return true;
}

bool RsfscTcpClient::isConnected() const { return is_connected_ && socket_ && socket_->is_open(); }

std::size_t RsfscTcpClient::write(const std::vector<uint8_t>& _data)
{
  if (!isConnected())
  {
    LOG_INDEX_ERROR(msg_header_ + "写入失败，连接未打开");
    return 0;
  }
  fmt::string_view omit_str = _data.size() > 100 ? " ..." : "";
  try
  {
    auto bytes_written = socket_->write_some(boost::asio::buffer(_data));
    LOG_INDEX_DEBUG("{}write data{}: {:02x}{}", msg_header_, bytes_written == _data.size() ? "" : " failed",
                    _data.size() > 100 ? fmt::join(_data.begin(), _data.begin() + 100, " ") : fmt::join(_data, " "),
                    omit_str);
    return bytes_written;
  }
  catch (const std::exception& e)
  {
    LOG_INDEX_ERROR(msg_header_ + "写入失败，错误信息：" + e.what());
    return 0;
  }
}

std::size_t RsfscTcpClient::readSome(std::vector<uint8_t>& _data)
{
  if (!isConnected())
  {
    return 0;
  }

  try
  {
    return socket_->read_some(boost::asio::buffer(_data));
  }
  catch (const std::exception&)
  {
    return 0;
  }
}

std::vector<uint8_t> RsfscTcpClient::readSomeTimeout(uint32_t _timeout_ms)
{
  if (!isConnected())
  {
    return {};
  }

  std::vector<uint8_t> buffer(2048);
  boost::asio::steady_timer timer(ioContext());
  timer.expires_after(std::chrono::milliseconds(_timeout_ms));

  std::promise<std::vector<uint8_t>> read_promise;
  auto read_future = read_promise.get_future();
  bool timed_out   = false;

  timer.async_wait([this, &timed_out](const boost::system::error_code& _ec) {
    if (!_ec)
    {
      timed_out = true;
      socket_->cancel();
    }
  });

  socket_->async_read_some(boost::asio::buffer(buffer), [&buffer, &read_promise](const boost::system::error_code& _ec,
                                                                                 std::size_t _bytes_transferred) {
    if (!_ec)
    {
      buffer.resize(_bytes_transferred);
      read_promise.set_value(buffer);
    }
    else
    {
      read_promise.set_value({});
    }
  });

  try
  {
    auto result = read_future.get();
    timer.cancel();
    if (timed_out)
    {
      return {};
    }
    return result;
  }
  catch (const std::exception&)
  {
    return {};
  }
}

void RsfscTcpClient::asyncWrite(const std::vector<uint8_t>& _data, WriteHandler _handler)
{
  if (!isConnected())
  {
    _handler(boost::asio::error::not_connected, 0);
    return;
  }

  socket_->async_write_some(boost::asio::buffer(_data), _handler);
}

void RsfscTcpClient::asyncRead(std::vector<uint8_t>& _data, ReadHandler _handler)
{
  if (!isConnected())
  {
    _handler(boost::asio::error::not_connected, 0);
    return;
  }

  socket_->async_read_some(boost::asio::buffer(_data), _handler);
}

void RsfscTcpClient::abort() { RsfscCommClient::abort(); }

bool RsfscTcpClient::isAborted() const { return RsfscCommClient::isAborted(); }

void RsfscTcpClient::resetAbort() { RsfscCommClient::resetAbort(); }

bool RsfscTcpClient::setClientOptions()
{
  socket_->set_option(boost::asio::ip::tcp::socket::keep_alive(true));
  socket_->set_option(boost::asio::ip::tcp::socket::send_buffer_size(61440));
  socket_->set_option(boost::asio::ip::tcp::socket::receive_buffer_size(61440));
  socket_->set_option(boost::asio::ip::tcp::no_delay(true));
  return true;
}
std::vector<uint8_t> RsfscTcpClient::request(const std::vector<uint8_t>& _request, uint32_t _timeout_ms)
{
  if (isAborted())
  {  // Check before starting any new operation
    LOG_INDEX_ERROR(msg_header_ + "请求失败，操作已被中止 (pre-check)");
    return {};
  }

  if (write(_request) <= 0)
  {
    LOG_INDEX_ERROR(msg_header_ + "请求失败，写入失败");
    return {};
  }

  // Promise to signal completion or error from async operations
  std::promise<std::vector<uint8_t>> promise_for_response;
  std::future<std::vector<uint8_t>> future_for_response = promise_for_response.get_future();

  boost::asio::steady_timer timer(ioContext());
  timer.expires_after(std::chrono::milliseconds(_timeout_ms));

  // Use a shared_ptr for the response buffer to manage its lifetime across lambdas
  // And a flag to ensure promise is set only once.
  auto response_buffer_ptr     = std::make_shared<std::vector<uint8_t>>(2048);
  auto operation_completed_ptr = std::make_shared<std::atomic<bool>>(false);

  // Asynchronous read operation
  socket_->async_read_some(boost::asio::buffer(*response_buffer_ptr),
                           [&timer, response_buffer_ptr, &promise_for_response, operation_completed_ptr, this](
                             const boost::system::error_code& _error_code, std::size_t _bytes_transferred) {
                             // Attempt to set the promise only if no operation (timer or read) has completed yet
                             if (operation_completed_ptr->exchange(true))
                             {          // exchange returns previous value
                               return;  // Already handled by timer or another callback
                             }

                             timer.cancel();  // Cancel the timer as read operation finished (or failed)

                             if (_error_code == boost::asio::error::operation_aborted)
                             {
                               LOG_INDEX_ERROR(msg_header_ + "读取操作被取消 (可能由超时引起)");
                               promise_for_response.set_value({});  // Or set_exception if preferred
                             }
                             else if (_error_code)
                             {
                               LOG_INDEX_ERROR(msg_header_ + "读取失败: " + _error_code.message());
                               promise_for_response.set_value({});
                             }
                             else
                             {
                               response_buffer_ptr->resize(_bytes_transferred);
                               promise_for_response.set_value(*response_buffer_ptr);
                             }
                           });

  // Asynchronous wait on timer
  timer.async_wait(
    [this, &promise_for_response, operation_completed_ptr](const boost::system::error_code& _error_code) {
      if (operation_completed_ptr->exchange(true))
      {
        return;  // Already handled by read callback
      }

      // Timer expired or was cancelled.
      // If _error_code is operation_aborted, it means timer was cancelled by the read completing.
      if (_error_code == boost::asio::error::operation_aborted)
      {
        // This is expected if read completed first and cancelled the timer.
        // The read handler would have set the promise.
        return;
      }

      // If _error_code is set (and not operation_aborted), it's some other timer error.
      if (_error_code)
      {
        LOG_INDEX_ERROR(msg_header_ + "定时器错误: " + _error_code.message());
        // Try to cancel socket operations, though read might be stuck elsewhere
        boost::system::error_code ignored_cancel_ec;
        socket_->cancel(ignored_cancel_ec);
        promise_for_response.set_value({});
        return;
      }

      // Timer expired (no error, not aborted) - this is a genuine timeout.
      LOG_INDEX_ERROR(msg_header_ + "请求超时");
      boost::system::error_code ignored_ec;
      socket_->cancel(ignored_ec);  // Cancel the pending read operation
      promise_for_response.set_value({});
    });

  try
  {
    std::vector<uint8_t> result_from_future = future_for_response.get();
    if (result_from_future.empty() && !isAborted())
    {  // Additional check if empty means error
       // Log was already done in callbacks
      LOG_INDEX_ERROR(msg_header_ + "请求失败，响应为空");
      return {};
    }
    if (isAborted() && result_from_future.empty())
    {
      LOG_INDEX_ERROR(msg_header_ + "请求在等待期间被中止");
      return {};
    }
    LOG_INDEX_DEBUG("{}receive data: {:02x}", msg_header_, fmt::join(result_from_future, " "));
    return result_from_future;
  }
  catch (const std::future_error& e)
  {
    // This might happen if io_context::run() returns before promise is set
    // (e.g. io_context::stop() called from elsewhere, or unhandled exception in a handler)
    LOG_INDEX_ERROR(msg_header_ + "请求失败，Future异常: " + e.what());
    if (isAborted())
    {
      LOG_INDEX_ERROR(msg_header_ + "操作被中止导致Future异常");
    }
    // Ensure socket is cancelled if we land here unexpectedly
    boost::system::error_code ignored_ec;
    auto error_code = socket_->cancel(ignored_ec);
    LOG_INDEX_ERROR("{} cancel socket: {}", msg_header_, error_code.message());
    return {};
  }
}

}  // namespace lidar
}  // namespace robosense
