﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#pragma once

//
// Include a bundled header-only copy of spdlog fmt lib or an external one.
// By default spdlog include its own copy.
//

#if defined(SPDLOG_USE_STD_FORMAT)  // SPDLOG_USE_STD_FORMAT is defined - use std::format
#  include <format>
#elif !defined(SPDLOG_FMT_EXTERNAL)
#  if !defined(SPDLOG_COMPILED_LIB) && !defined(FMT_HEADER_ONLY)
#    define FMT_HEADER_ONLY
#  endif
#  ifndef FMT_USE_WINDOWS_H
#    define FMT_USE_WINDOWS_H 0
#  endif
// enable the 'n' flag in for backward compatibility with fmt 6.x
#  define FMT_DEPRECATED_N_SPECIFIER
#  include "rsfsc_log/fmt/bundled/ranges.h"
#  if defined(RSFSCLOG_FORMAT_ENUM)
#    include "rsfsc_log/fmt/magic_enum_format.hpp"
#  endif
#else  // SPDLOG_FMT_EXTERNAL is defined - use external fmt lib
#  include <fmt/core.h>
#  include <fmt/format.h>
#endif
