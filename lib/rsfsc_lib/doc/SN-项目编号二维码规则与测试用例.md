﻿# SN-项目编号二维码测试

每个MEMS项目有自己的SN规则，这里列举了可能遇到的情况给出对应的测试用例及二维码。需要NPI同事做更多的测试，并走ECR做测试。目前统计的规则：


|  项目   |      SN命名规则      | 存储到雷达的SN(12个半字) |                                          位数含义描述                                           |                            备注                            |
| :-----: | :------------------: | :--------------------: | :------------------------------------------------------------------------------------- | :--------------------------------------------------------: |
|  0210   | 1250XXXX#### <br>(12个) |      1250XXXX####      |    1-4：RS内部产品编码;<br>5-8：年周编号;<br>9-12：随机数流水号     |       M1内部产品编码1250；有16进制的内部映射转化关系       |
|  0211   | YYDDD#####<br>(10个） |      YYDDD#####FF      |           1-2：年份后2位；<br>3-5：自然天数；<br>6-10：流水号，自增1            |               闰年最大值366，非闰年最大值365               |
| 0212_df | YMDD######<br>(10个） |      YYMMDD######      |     1：年份编码；<br>2：月份编码；<br>3-4：日期；<br>5-10：流水号，自增1     |            具体编码，参考下图0212_df的编码格式             |
| 0212_zq | YYMMDD####<br>(10个） |      YYMMDD####FF      |   1-2：年份后两位；<br>3-4：月份；<br>5-6：日期；<br>7-10：流水号   |  |
|  0214   | YMMDD#####<br>(10个） |      YYMMDD#####F      |     1：年份编码；<br>2-3：月份；<br>4-5：日期；<br>6-10：流水号，自增1     |              具体编码，参考下述0214的编码格式              |
|0215|YYMMDD#######`R或L`<br>(14个)|YW WD ## ##|1-2：年份后2位<br>3-4：月份<br>5-6：日期<br>7-13：流水号<br>14：区分左右，仅做生产管控||
|  0216   |    YMD####`R或者L`<br>(8个）    |      YYMMDD####FF      | 1：年份编码;<br>2：月份编码；<br>3：日期编码；<br>4-7：流水号<br>8：区分左右雷达，仅做生产管控，不烧录到雷达内；20220208已不区分左右，最后一位不做要求 |          具体编码，参考下面comment 0216的编码格式          |
|0217|HAYYMDDA########`AR,AL,BR,BL,CR,CL,DR,DL`<br>(17个)|HAYYMDDA########|1：厂区缩写<br>2: 早晚班<br>3-4：年份后两位<br>5： 月份编码<br>6-7：日期<br>8：特殊代码，为A<br>9-16：流水号<br>17：区分ABCD车，仅做生产管控，不烧录到雷达内<br>18：区分左右雷达，仅做生产管控，不烧录到雷达内|具体编码，参考下述0217的编码格式|
|0218|YYMMDD####<br>(10个)|0Y 0Y 0M 0M 0D 0D 0# 0# 0# 0#|1-2：年份后2位<br>3-4：月份<br>5-6：日期<br>7-10：流水号||
|0219||||SN暂同0215|
|021A|YYMMDD####`FM,FL,FR`|0Y 0Y 0M 0M 0D 0D 0# 0# 0# 0#|1-2：年份后2位<br>3-4：月份<br>5-6：日期<br>7-10：流水号<br>11-12：区分前向，前左，前右雷达，仅做生产管控，不烧录到雷达内||
|021B|BYMDD####`R或者L`<br>(10个)|YMDD0####|1：项目代号B<br>2：年份编码<br>3：月份编码<br>4-5：日期<br>6-9：流水号<br>10：区分左右雷达，仅做生产管控|年份编码与GB16735标准保持一致，详见下述021B的编码格式|

|0212df SN规则|
|:---:|
|![](img/0212df_sn_rule.png)|

|0214SN规则|
|:---:|
|![](img/0214_sn_rule.png)|

|0216SN规则|
|:---:|
|![](img/0216_sn_rule.png)|

|0217SN规则|
|:---:|
|![](img/0217_sn_rule.png)|

|021BSN规则|
|:---:|
|![](img/021B_sn_rule.png)|

## 1 测试用例

|    SN-项目编码    |              二维码               | 结果                      |
| :---------------: | :-------------------------------: | :------------------------ |
| 1250BA09B90A-0210 | ![](img/sn_test_case/1250BA09B90A-0210.png) | SN及项目正确              |
|   MSA1010-0210    |   ![](img/sn_test_case/MSA1010-0210.png)    | SN及项目正确              |
|1251BA09B90A-0210  | ![](img/sn_test_case/1251BA09B90A-0210.png) |SN正确，项目显示not_found|
|    MG09000010     |    ![](img/sn_test_case/MG09000010.png)     | SN正确，项目显示0210      |
|  2110400008-0211  |  ![](img/sn_test_case/2110400008-0211.png)  | SN及项目正确              |
|2136700001-0211|![](img/sn_test_case/2136700001-0211.png)|SN正确，项目显示not_found|
| 1250BA09B90A-0211 | ![](img/sn_test_case/1250BA09B90A-0211.png) | SN正确，项目显示not_found |
|  MG09000010-0212  |  ![](img/sn_test_case/MG09000010-0212.png)  | SN及项目正确              |
|  2107090101-0212  |  ![](img/sn_test_case/2107090101-0212.png)  | SN及项目正确              |
|2101010101-0212|![](img/sn_test_case/2101010101-0212.png)|SN及项目正确|
|OG09000010-0212|![](img/sn_test_case/OG09000010-0212.png)|SN正确，项目显示not_found|
|  M070900010-0214  |  ![](img/sn_test_case/M070900010-0214.png)  | SN及项目正确              |
|   MSH0152-0214    |   ![](img/sn_test_case/MSH0152-0214.png)    | SN正确，项目显示not_found          |
|  MG09000010-0214  |  ![](img/sn_test_case/MG09000010-0214.png)  | SN正确，项目显示not_found |
|2205260000001R-0215|![](img/sn_test_case/25270001R-0215.png)|SN及项目正确|
|25370001R-0215|![](img/sn_test_case/25370001R-0215.png)|SN正确，项目显示not_found|
|4AY0001R-0216|![](img/sn_test_case/4AY0001R-0216.png)|SN及项目正确|
|MSX0152-0216|![](img/sn_test_case/MSX0152-0216.png)|SN正确，项目显示not_found|
|OAY0001R-0216|![](img/sn_test_case/OAY0001R-0216.png)|SN正确，项目显示not_found|
|SA22A31A00000001AR-0217|![](img/sn_test_case/SA22A31A00000001R-0217.png)|SN及项目正确|
|MSA0125R-0217|![](img/sn_test_case/MSA0125R-0217.png)|SN及项目正确|
|SA22A31B00000001AR-0217|![](img/sn_test_case/SA22A31B00000001R-0217.png)|SN正确，项目显示not_found|
|2212310001-0218|![](img/sn_test_case/2212310001-0218.png)|SN及项目正确|
|2212320001-0218|![](img/sn_test_case/2212320001-0218.png)|SN正确，项目显示not_found|
|BN9310001R-021B|![](img/sn_test_case/BN9310001R-021B.png)|SN及项目正确|
|N9310001R-021B|![](img/sn_test_case/N9310001R-021B.png)|SN正确，项目显示not_found|
