/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef LIDAR_CTL_H
#define LIDAR_CTL_H

#include "data_struct.h"
#include "rsfsc_fsm/finite_state_handler.h"
#include "rsfsc_utils/decl_name.h"
#include "work_model/connectivity_work_model.h"
#include <QMutex>
#include <QMutexLocker>
#include <QWaitCondition>
#include <cstdint>
#include <string>
#include <vector>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{

namespace lidar
{
/*修改为支持状态机的形式*/

class InterruptException : public std::exception
{
public:
  explicit InterruptException(const std::string& _msg) : msg_(_msg) {}
  explicit InterruptException(const QString& _msg) : msg_(_msg.toStdString()) {}
  explicit InterruptException(const char* _msg) : msg_(_msg) {}

  [[nodiscard]] const char* what() const noexcept override { return msg_.c_str(); }

private:
  std::string msg_;
};

class ConnectivityWorkHandler : public WorkHandler<ConnectivityWorkModel>
{
  IMPL_CLASSNAME(AgingWorkHandler)
public:
  explicit ConnectivityWorkHandler(const ActionState& _state) : WorkHandler<ConnectivityWorkModel>(_state) {}
  ConnectivityWorkHandler(const ConnectivityWorkHandler&) = delete;
  ConnectivityWorkHandler(ConnectivityWorkHandler&&)      = delete;
  ConnectivityWorkHandler& operator=(const ConnectivityWorkHandler&) = delete;
  ConnectivityWorkHandler& operator=(ConnectivityWorkHandler&&) = delete;
  ~ConnectivityWorkHandler() override;
  bool sleep(uint32_t _sec);
  bool msleep(uint32_t _msec);
  void abort() override;
  void wake();

  ActionState handleState() override;
  virtual ActionState handle() = 0;

  ParaInfo& getParaInfo() { return getWorkModel()->getParaInfo(); }

  // 自定义函数
  std::string pingWait(const std::vector<std::string>& _ip_vec, uint32_t _msec);
  bool pingWait(const QString& _ip_addr, uint32_t _msec);
  bool connectLidar(const QString& _ip, int _port, int _timeout);

  // 默认等待Aging ip
  bool waitForStartUpComplete(const QString& _ip, const int _port, const int _timeout);
  bool waitForOneMsopPacket(const QString& _ip, const int _port, const int _timeout = 10000);

  void abortAndMaintainEnv(const QString& _msg);
  void setWorkModelFailMsg(const QString& _fail_label, std::string_view _fail_msg);
  void setWorkModelFailMsg(std::string_view _fail_msg);

private:
  std::mutex mutex_;
  std::condition_variable condition_;
};

class CheckMes : public ConnectivityWorkHandler
{
  IMPL_CLASSNAME(CheckMes)

public:
  explicit CheckMes() : ConnectivityWorkHandler(ActionState::STATE_CHECK_ALL_STATE) {}
  ActionState handle() override;
};

class WriteSn : public ConnectivityWorkHandler
{
  IMPL_CLASSNAME(WriteSn)
public:
  explicit WriteSn() : ConnectivityWorkHandler(ActionState::STATE_WRITE_SN) {}
  ActionState handle() override;
};

class Write459Config : public ConnectivityWorkHandler
{
  IMPL_CLASSNAME(Write459Config)

public:
  explicit Write459Config(const ActionState& _state) : ConnectivityWorkHandler(_state) {}
  ActionState handle() override;
};

class ConnectLidar : public ConnectivityWorkHandler
{
  IMPL_CLASSNAME(ConnectLidar)

public:
  explicit ConnectLidar() : ConnectivityWorkHandler(ActionState::STATE_CONNECT_LIDAR) {}
  ActionState handle() override;
};

class InitLidar : public ConnectivityWorkHandler  //打开继电器,连接雷达,修改雷达IP
{

  IMPL_CLASSNAME(InitLidar)

public:
  explicit InitLidar() : ConnectivityWorkHandler(ActionState::STATE_INIT_LIDAR) {}
  ActionState handle() override;
};

class CheckDifop : public ConnectivityWorkHandler
{
  IMPL_CLASSNAME(CheckDifop)

public:
  explicit CheckDifop(const ActionState& _state) : ConnectivityWorkHandler(_state) {}
  ActionState handle() override;
};
class CheckMsop : public ConnectivityWorkHandler
{
  IMPL_CLASSNAME(CheckMsop)

public:
  explicit CheckMsop(const ActionState& _state) : ConnectivityWorkHandler(_state) {}
  ActionState handle() override;
};
class CheckLightError : public ConnectivityWorkHandler
{
  IMPL_CLASSNAME(CheckLightError)
public:
  explicit CheckLightError(const ActionState& _state) : ConnectivityWorkHandler(_state) {}
  ActionState handle() override;
};

class SuccessHandler : public ConnectivityWorkHandler
{
  IMPL_CLASSNAME(SuccessHandler)
public:
  explicit SuccessHandler(const ActionState& _state) : ConnectivityWorkHandler(_state) {}
  ActionState handle() override;
};

class AbortHandler : public ConnectivityWorkHandler
{
  IMPL_CLASSNAME(AbortHandler)

public:
  explicit AbortHandler(const ActionState& _state) : ConnectivityWorkHandler(_state) {}
  ActionState handle() override;
};

class FailHandler : public ConnectivityWorkHandler
{
  IMPL_CLASSNAME(FailHandler)
public:
  explicit FailHandler(const ActionState& _state) : ConnectivityWorkHandler(_state) {}
  ActionState handle() override;
};

class FinalHandler : public ConnectivityWorkHandler
{
  IMPL_CLASSNAME(FinalHandler)
public:
  explicit FinalHandler(const ActionState& _state) : ConnectivityWorkHandler(_state) {}
  ActionState handle() override;
};

}  // namespace lidar
}  // namespace robosense
#endif  // LIDAR_CTR_H
