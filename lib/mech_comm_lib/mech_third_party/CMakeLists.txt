﻿cmake_minimum_required(VERSION 3.10)

project(
  mech_third_party
  VERSION 1.0.0
  LANGUAGES CXX)

# 创建一个接口库，以便其他目标可以轻松使用
add_library(mech_third_party INTERFACE)
add_library(mech::third_party ALIAS mech_third_party)

# 设置包含目录
target_include_directories(mech_third_party SYSTEM INTERFACE $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>)

# 链接到nlohmann_json
# target_link_libraries(mech_third_party INTERFACE nlohmann_json::nlohmann_json)
