﻿# log setting控件使用手册

进行测试数据log设置的窗口，目前主要包括**MES过站查询**、**数据上传MES**、**测试数据标准化输出**、**过程数据自动上传数据服务器**等几个子功能


1. **MES过站查询**：程序向MES系统询问，当前工序之前的工序是否正常完成，必要时可询问MES前面工序上传的数据信息或者bom表中的信息，通过`checkAllState`函数，当使用统一SN时，过站查询会返回雷达项目编号、车型及安装位置信息
2. **数据上传MES**：把当前工序生成的关键参数、测试结果，统称测试数据，上传到MES做车规备份及后面的工序调用
3. **测试数据标准化输出**：为了适应无网络环境和备份需要，会将测试数据保存一份到雷达文件夹；为了统计分析方便（MES不具备或未完善时），将所有的测试数据文件汇总到一个文件夹中，文件夹中的数据可以通过[stats_tool](http://gitlab.robosense.cn/system_release/useful_tool/stats_tool_release)工具进行分析
   ![](img/stats_tool.png)
4. **过程数据自动上传数据服务器**：测试会产生一个雷达文件夹，里面包含测试数据及过程数据，自动上传会通过UDP把该文件夹地址传给`自动上传程序`，由自动上传程序打包上传到数据服务器。以前这部分工作由定时备份脚本和人工上传，不够及时，也容易出错
5. **记录crash状态**:使用Breakpad库进行crash捕捉，出现崩溃异常时会生成`dmp文件`保存在`~/.RoboSense/Program_name_vXX.XX.XX.XX`文件夹中，可使用相关分析工具快速分析，详见`breakpad`模块`README.md`文件
6. **检测软件多开**：设置了`robosense::lidar::WidgetLogSetting::init(argc, argv,_key_name);`后默认生效，可在菜单栏->高级设置中去掉勾选。设置完毕要重新启动才会生效
7. **检测硬盘大小**：目前在`checkAllState`时会默认进行硬盘空间检测，包括存储硬盘及系统盘，默认小于30G时会进行报错，但不影响本次流程。30G这个值可以在界面中修改
8. **用户管理系统**：目前只能每台电脑独立管理，后续会开发联网功能，其中开发者权限的admin用户密码每天一变，其余的每个用户名请用域账号设置，由于后续参数监控等都与这个用户名相关，请认真设置
9.  **参数监控**：`RSFSCQSettings`是一个继承于QSettings的类，每当remove或者setValue函数被调用时，会先检查本地文件有无对应参数，当发现参数被修改时，会记录一条“谁，在什么时候，将什么参数，从什么值，修改为什么值”的log到`~/.RoboSense/setting_log`文件夹中，如下图所示
    ![](img/rsfsc_qsettings.png)
10. **自动修改项目编号及IP、端口等**，如果勾选了`使用IP池`，则会在获取到IP池信息后自动修改WidgetLidarInfo中的IP和port；如果勾选了`使用统一SN`，则会在过站检测时从MES获取项目编号、车型、安装位置等信息更新到WidgetLidarInfo中
11. **产量及良率统计**：目前仅支持单台电脑，所有项目`每天`及`所有时间`的产量及良率进行处理，同一台雷达测试多次，会被算多次，不支持其他统计途径。
12. **自动化设备通信**：当自动化设备准备好后，自动触发WidgetLidarInfo；当测试完成调用finishProcess时，自动通知自动化设备，使得软件可以不做任何修改就适配自动化线
13. **加密狗校验**： 判断是否插了软件要求的加密狗，没有的话报错退出
14. **配置文件校验**：过站前需将配置文件所在文件夹路径通过`addCheckSumPath`函数进行添加(支持多个路径)，软件安装后首次运行或配置文件变更后需使用管理员以上权限在`菜单栏->高级设置`中单击`重置配置文件校验值`按钮进行配置文件校验值重置操作，配置文件校验若不通过无法进行过站操作
    

234点只要在结束时将测试数据通过`addMeasureMessage`函数传递进来，然后调用`finishProcess`就可自动完成操作

## 1 调用方法

### 1.1 整体测试流程调用方法

**目前单台调用和多台调用是一样的了，不再进行区分**

虽然调用能支持lidar_index区分开，但checkAllState和finishProcess函数因为涉及到MES通信，目前考虑到MES负载，仍然只有一个实例，所以**仍然只能单线程顺序执行这两个函数**

1. 在main函数中，WidgetLogSetting构造前，调用init：`robosense::lidar::rsfsc_lib::WidgetLogSetting::init(argc, argv,_key_name);`(_key_name为软件在USB Key管理系统中注册的名字，调试阶段无注册名可传入空字符串，空字符串不做key检测）
2. new构造一个WidgetLogSetting，然后`widget_log_setting_->setWindowIcon(QIcon(":/img/your_icon.png"));`
3. 增加action供用户在正常使用时弹出设置窗口：
   ```cpp
   QMenu* menu_setting           = new QMenu("设置", this);
   QAction* action_log_setting = menu_setting->addAction("log文件参数设置");
   this->menuBar()->addMenu(menu_setting); // this is MainWindow
   QObject::connect(action_log_setting, SIGNAL(triggered()), this, SLOT(slotShowLogSetting()) );
   
   void MainWindow::slotShowLogSetting()
   {
     if (widget_log_setting_->isVisible())
     {
       widget_log_setting_->raise();
       widget_log_setting_->activateWindow();
     }
     else
     {
       widget_log_setting_->show();
     }
   }
   ```
4. 在`MainWindow`构造的最后`QTimer::singleShot(50, this, SLOT(slotShowLogSetting()));`，这样能确保软件打开后弹出在最前面，让用户完成用户名的输入
5. 在构建完WidgetLidarInfo后，按如下步骤去注册WidgetLidarInfo控件，**注意，lidar_index是后续对应控件的唯一索引，请不要设置重复，要求从1开始，且与现场管控一致，一般情况下统一从下到上为1-n，仅需调用一次**：
   ```cpp
   widget_log_setting_->registerWidgetLidarInfo(i + 1, widget_lidar_info_[i])
   widget_log_setting_->setSoftwareVersion(PROGRAM_VERSION_MAJOR, PROGRAM_VERSION_MINOR,
                                           PROGRAM_VERSION_PATCH);
   ```
6. 在每台雷达开始测试前，执行，**请务必在连接雷达前执行，否则IP和端口号是错的**：
    ```cpp
     QString data_path;
     QString result_path;
     // checkAllState等于原来的resetFieldStatus + startProcess + setSerialNumber
     switch (widget_log_setting_->checkAllState(i + 1, data_path, result_path))
     {
     case CHECK_STATE_SUCCESS:
     {
       // all success
       break;
     }
     default:
     {
       // 请注意查看终端和界面报错信息
       return;
     }
     }
    ```
7. 获取log保存路径：`log_path = widget_log_setting_->getLogPath();`，应该将log存储到这个位置，命名为`SN1_SN2_SN3..._SNn_开始年月日时分秒.txt`，等测试完成后，将log拷贝n份到对应的`data_path`中。部分项目的SN会比较长，可以采用保存SN后5位的形式
8. 测试过程中，设置以下信息：
   ```cpp
   widget_log_setting_->setFirmwareRevision(i + 1, ps_version, pl_version); // 从雷达读取
   widget_log_setting_->setPlatform(i + 1, "M1P"); // 可根据自己项目平台进行设置, 不设置则默认为空
   if (!widget_log_setting_->addMeasureMessage(i + 1, limit_info, data, type)) // 设置测试数据
   {
     RSFSCLog::getInstance()->error("数据{0}超出阈值，请检查：", limit_info.name);
   }
   // 设置成功标记
   widget_log_setting_->setTestStatus(LOG_TEST_STATUS_PASS);
   // 设置失败标记
   // Widget_log_setting_->setTestStatus(LOG_TEST_STATUS_FAIL, QString("fail"), str_error_message); // 设置pass还是fail，如果是fail，则fail_label和fail_msg必须设置
   // Widget_log_setting_->setTestStatus(LOG_TEST_STATUS_NOT_READY, QString("fail"), str_error_message); // 转台、导轨或工装治具无法连接，不满足测试要求的情况
   ```
9.  在每台雷达测试结束时执行：
    ```cpp
    // 不用再像之前一样reset然后重新设置一遍
    QString write_err;
    if (!widget_log_setting_->finishProcess(i + 1, write_err, unbound_ip_pool))
    {
      RSFSCLog::getInstance()->error(write_err);
      QMessageBox::warning(this, "error", write_err);
    }
    ```

### 1.2 公共库增加rsfsc_log库增加index索引同步修改

现在有些工位在未来量产线体要支持到一拖一百的程度，比如老化、温补。一百台的标定log特别庞大，并且多台机器信息混合在一起，定位查询不方便(实际上log多的工位一拖几台日志也不方便查询)

公共库增加rsfsc_log库增加index索引同步修改，最新的公共库版本`v1.12.x(rsfsc_log v2.2.x)`以后的版本，已经支持支持多份日志文件写入的功能，公共库为了兼容及方便log存放问题，已经在`checkAllState`中增加修改对应index的路径功能了

```
16:13:45.791 W [p] 59883 WidgetLogSetting::checkAllState -> 未启用过站检测 // 其中p代表default，默认汇总的log
init_log_file_ = /home/<USER>/.RoboSense/normal_log/mes_example_v220_1.log // 修改前log路径
16:13:45.803 I [1] 59883 /home/<USER>/work/common/rsfsc_lib_src/build/0220/rsdata/123_000_20250113_161345/result/123_20250113_161345.log // 修改后的lidar index = 1的log路径
```

**注：为了能查询到统一的某台雷达的数据信息，公共库的lidar_index都是从1开始的，使用公共库的相关log和确保是从1开始索引**

```cpp
// 例如以下的_lidar_index从1开始
RSFSCLog::getInstance(static_cast<int>(_lidar_index))->warn("WidgetLogSetting::requireData -> 未启用获取数据");

// 其他 code ...

RSFSCLog::getInstance(static_cast<int>(_lidar_index))->warn("第一个雷达测试开始");
```

## 2 数据查询

### 2.1 查询工站相关的测试mes数据

目前数据查询只支持RoboSense的MES，如果你想要查询前站的测试信息，或者是BOM表的料号，可以通过`WidgetLogSetting::checkAllState`的`_ask_data`参数来获取。这个参数由`AskData`结构体组成，具体各个变量的定义参考头文件中的注释。

查询工站相关的测试mes数据可以使用如下类型：

- `ASK_TYPE_CALIB_ASK_CALIB_DATA`：根据SN查询对应工序过站信息(包含整机段、振镜段、模具段)
- `ASK_TYPE_CALIB_ASK_MODULE_DATA`：整机段查模组线工站的数据
- `ASK_TYPE_CALIB_ASK_MIRROR_DATA`：整机段查振镜工站的数据
- `ASK_TYPE_CALIB_ASK_RECEIVE_MODULE_DATA`：整机段查接收模组段数据
- `ASK_TYPE_CALIB_ASK_EMITTING_MODULE_DATA`：整机查发射模组段数据
- `ASK_TYPE_CALIB_ASK_MATERIAL_DATA`：根据SN查询料号信息
- `ASK_TYPE_CALIB_ASK_CUSTOMER_SN`：整机段查客户SN信息
- `ASK_TYPE_CALIB_ASK_SUB_SN_TO_DATA`: 通过子件SN查询指定测试信息(未装配的子件SN)

比如B站位，想要知道前面A站位的数据，那就：

```cpp
std::map<rsfsc_lib::AskData::AskType, std::vector<rsfsc_lib::AskData>> ads;

ads[rsfsc_lib::AskData::ASK_TYPE_CALIB_ASK_CALIB_DATA].emplace_back(
  "station_name", std::initializer_list<std::string> { "item_name1", "item_name2" });
widget_log_setting_->checkAllState(i + 1, data_path, result_path, ads)

if (ads[rsfsc_lib::AskData::ASK_TYPE_CALIB_ASK_CALIB_DATA][0].is_ask_success)
{
  RSFSCLog::getInstance()->info("successfully to get");       
}
else
{
  RSFSCLog::getInstance()->error("failed to get");       
}
```

除了`请求item_name`以外，新的修改已经将`请求的返回非空数据(即一些固定数据)都存放到items中`，调用者自行获取使用, 例如如下：
请求振镜工站数据:

```json
{
    "CommandType": "DataRequest",
    "DataList": [
        {
            "ParamList": [
                {
                    "Item1": "slow_axis_zero_position",
                    "Station": "GA260"
                }
            ],
            "Type": "CalibAskMirror"
        }
    ],
    "SN": "3024BDDE0077",
    "Station": "001"
}
```

请求回复数据：

```json
{
    "Error": "",
    "DataList": [
        {
            "Type": "CalibAskMirror",
            "SubItemData": [
                {
                    "SubItemSN": "M1PG241110209F3",
                    "SubItemIndex": null,
                    "ParamList": [
                        {
                            "StopDate": "20241113_151911", // 固定数据存放在items
                            "Station": "GA260", // 固定数据存放在items
                            "Error": "", // 数据为空不存放items中
                            "slow_axis_zero_position": "1.649_1.750_1.550_P", // 请求的数据存放在items
                            "RequestStatus": "P" // 固定数据存放在items
                        }
                    ]
                }
            ],
            "Error": "",
            "RequestStatus": "P"
        }
    ],
    "RequestStatus": "P"
}
```

注： **振镜工站数据返回的StopDate为固定数据，无需在ads中指定`item_name`**

### 2.2 网络秘钥相关mes交互

查询秘钥相关数据可以使用如下类型：

- `ASK_TYPE_KMS_AES_EFUSE_KEY`: 查询AES_EFUSE_KEY密钥信息
- `ASK_TYPE_KMS_ENABLE_SECURE_TAG`: 查询ENABLE_SECURE_TAG密钥信息
- `ASK_TYPE_KMS_SIGN_CERT`: KMS签名，由于签名需要传数据给mes，使用上，最好不要和上述两个合并使用

1 查询ASK_TYPE_KMS_AES_EFUSE_KEY/ASK_TYPE_KMS_ENABLE_SECURE_TAG，以下是查询两条数据合并，也可以查询一条
```cpp
std::map<rsfsc_lib::AskData::AskType, std::vector<rsfsc_lib::AskData>> ads;
// 查询AES_EFUSE_KEY密钥信息，EnableSecureTag需要mes返回的key name
ads[rsfsc_lib::AskData::ASK_TYPE_KMS_ENABLE_SECURE_TAG].emplace_back(
  lineedit_station_ask_for_->text().toStdString(), std::initializer_list<std::string> {});
ads[rsfsc_lib::AskData::ASK_TYPE_KMS_ENABLE_SECURE_TAG].at(0).items["RequestItemName"] = "EnableSecureTag";
// 查询ENABLE_SECURE_TAG密钥信息，AesEfuseKey需要mes返回的key name
ads[rsfsc_lib::AskData::ASK_TYPE_KMS_AES_EFUSE_KEY].emplace_back(lineedit_station_ask_for_->text().toStdString(),
                                                                 std::initializer_list<std::string> {});
ads[rsfsc_lib::AskData::ASK_TYPE_KMS_AES_EFUSE_KEY].at(0).items["RequestItemName"] = "AesEfuseKey";
if (!widget_log_setting_->requireData(1, ads))
{
  RSFSCLog::getInstance()->error("查询数据失败，请查看终端和界面相关报错");
  signalChangeCheckStateButtonEnable(true);
  return;
}
RSFSCLog::getInstance()->info("查询数据成功,数据量：{0}", ads.size());
for (const auto& type_ads : ads)
{
  RSFSCLog::getInstance()->info("------------------------get data type: {0}",
                                rsfsc_lib::G_ASK_TYPE_STR[type_ads.first].toStdString());
  for (const auto& ad : type_ads.second)
  {
    RSFSCLog::getInstance()->info("+++++++this is a new AskData, ask is ask success: {0}",
                                  static_cast<int>(ad.is_ask_success));
    if (!ad.is_ask_success)
    {
      RSFSCLog::getInstance()->error("failed to ask data");
      continue;
    }
    if (!ad.sub_item_index.empty())
    {
      RSFSCLog::getInstance()->info("sub item index is: " + ad.sub_item_index);
      RSFSCLog::getInstance()->info("sub item sn is: " + ad.sub_item_sn);
    }
    for (const auto& item : ad.items)
    {
      RSFSCLog::getInstance()->info("item {0}'s value is: " + item.second, item.first);
    }
  }
}
```

2 签名ASK_TYPE_KMS_SIGN_CERT

```cpp
std::map<rsfsc_lib::AskData::AskType, std::vector<rsfsc_lib::AskData>> ads;
// SignCertificate需要mes返回的key name
ads[rsfsc_lib::AskData::ASK_TYPE_KMS_SIGN_CERT].emplace_back(
        lineedit_station_ask_for_->text().toStdString(), std::initializer_list<std::string> { "SignCertificate" });
// SignText签名的值，传递给mes的
ads[rsfsc_lib::AskData::ASK_TYPE_KMS_SIGN_CERT].at(0).items["SignText"] =
        std::string { "4d4c535447007f8dbc217400ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"
                      "ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"
                      "5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a00000000000003e8000000ff0000000031d812a70c9fe4eaa51306f175cf" };
// 其他代码...
```

3 上传密钥存储内容给mes，使用新函数`uploadKmsEnableResult`

```cpp
/**
 * @brief 上传KMS的使能结果
 * 
 * @param _lidar_index 当前lidar info控件索引,索引从1开始
 * @param _items 输出的数据，key/value形式，e.g. "AesEfuseKeyRes":"7101F039"
 * @return true 
 * @return false 
 */
bool uploadKmsEnableResult(const quint32 _lidar_index, const std::map<std::string, std::string>& _items);
```

```cpp
std::map<std::string, std::string> data { { "AesEfuseKeyRes", "7101F039" }, { "EnableSecureTagRes", "7101F032" } };
widget_log_setting_->uploadKmsEnableResult(1, data);
```

## 3 用户管理

### 3.1 用户管理联动使能操作

WidgetLogSetting提供了一个`void signalAuthorityUpdate(robosense::lidar::rsfsc_lib::UserAuthority *_user_authority);`函数，每当用户权限变更时，该signal就会触发，你只需要注册一个对应的slot，然后在slot里面根据权限去enable不同的控件即可：

```cpp
// 注意：namespace要写全，不写全好像不太行
// 注意：register到WidgetLogSetting的WidgetLidarInfo不需要设置，已经由WidgetLidarInfo进行了设置
// 注意：初始化的时候，widget_log_setting已经构造完毕，无法发送一个null的信号过来，所以需要显式调用slotUpdateAllWidgetState去初始化所有控件状态
class MainWindow : public QMainWindow
{
protected Q_SLOTS:
  void slotUpdateAllWidgetState(robosense::lidar::rsfsc_lib::UserAuthority *_user_authority = nullptr);
};
QObject::connect(widget_log_setting_, SIGNAL(signalAuthorityUpdate(robosense::lidar::rsfsc_lib::UserAuthority*)), this,
                   SLOT(slotUpdateAllWidgetState(robosense::lidar::rsfsc_lib::UserAuthority*)));
void MainWindow::slotUpdateAllWidgetState(robosense::lidar::rsfsc_lib::UserAuthority *_user_authority)
{
  static rsfsc_lib::UserAuthority current_user_authority;
  if (_user_authority != nullptr)
  {
    current_user_authority = *_user_authority;
  }
  bool is_lidar_connected = (pushbutton_connect_lidar_->text() == QString::fromUtf8("断开雷达"));
  bool is_technician_can_change =
    current_user_authority.isNotLessThan(rsfsc_lib::UserAuthority::Level::LEVEL_TECHNICIAN);
  bool is_manager_can_change = current_user_authority.isNotLessThan(rsfsc_lib::UserAuthority::Level::LEVEL_MANAGER);

  lineedit_param_->setEnabled(is_technician_can_change);
  pushbutton_test->setEnable(!is_manager_can_change);
}
```

### 3.2 用户管理类型设置

由于目前在使用的工位包括`项目开发前期阶段`或者`委外`的测试软件，而往往这些工位暂不支持mes的相关操作，为了兼容，故添加支持了调试版本和发布版本两种不同的用户管理类型模式

通过配置函数`robosense::lidar::rsfsc_lib::WidgetLogSetting::init()`的参数`_is_debug_mode`来确定使用模式

```cpp
/**
   * @brief     init of a WidgetLogSetting
   * @note      you must call this function before MainWindow constructor, so it can catch the crash of MainWindow's constructor and check the USB Key result
   * 
   * @param     _argc              argc of int main(argc, argv) function
   * @param     _argv              argv of int main(argc, argv) function
   * @param     _key_name          the key name of checking USB key, empty means dont check. If check failed, program will exit
   * @param     _program_name        name of the main program that call create WidgetLogSetting 
   * @param     _program_unique_code unique code of your program, you can apply one from
   *                                 http://gitlab.robosense.cn/system_knowledge/teamwork/factory_software_rule/blob/master/MEMS导产软件名称及编号规范.md
   * @param     _major              must within [0, 25]
   * @param     _minor              must within [0, 25]
   * @param     _patch              must within [0, 25]
   * @param     _tweak              this usually is the date release
   * @param     _is_debug_mode      true: 管理员以上权限mes的相关勾选才允许被取消; 软件窗体颜色警告软件为调试版本; false: 所有模式下mes相关设置都默认勾选; 警告取消
   * @param     _cable_manage_status 线束管理，参考CableManageStatus，默认为ENABLE
   * @param     _mes_type           数据交互类型，参考MESType类型
   * 
   * @return    bool               check USB Key result
   */
  static void init(int _argc,
                   char** _argv,
                   const std::string& _key_name,
                   const std::string& _program_name,
                   const std::string& _program_unique_code,
                   unsigned int _major,
                   unsigned int _minor,
                   unsigned int _patch,
                   unsigned int _tweak,
                   const bool _is_debug_mode                    = false,
                   const CableManageStatus _cable_manage_status = CableManageStatus::ENABLE,
                   const MESType _mes_type                      = MESType::MES_TYPE_ROBOSENSE);
```

- 调试版本，_is_debug_mode = true;
- 发布版本，_is_debug_mode = false;

以下是一种配置方式供参考，CMakeLists中做如下配置:

```cmake
option(IS_DEBUG_MODE "Is compile use debug mode : " OFF)

target_compile_definitions(${PROJECT_NAME} PRIVATE IS_DEBUG_MODE=$<IF:$<BOOL:${USE_FEATURE}>,1,0>)
```

调用init初始化如下：

```cpp
robosense::lidar::rsfsc_lib::WidgetLogSetting::init(
    _argc, _argv, std::string(""), std::string { PROJECT_NAME }, std::string { PROJECT_UNIQUE_CODE },
    EOS_SU_CHIP_FILTER_TEST_VERSION_MAJOR, EOS_SU_CHIP_FILTER_TEST_VERSION_MINOR, EOS_SU_CHIP_FILTER_TEST_VERSION_PATCH,
    EOS_SU_CHIP_FILTER_TEST_VERSION_TWEAK, IS_DEBUG_MODE, robosense::lidar::rsfsc_lib::MESType::MES_TYPE_ROBOSENSE_EDI);
```

## 4 IP池功能

**当前IP池服务器端配置的IP分配范围为`***********`至`***************`，启用IP池功能前需将电脑子网掩码设置为`***********`，支持启用IP池功能的软件版本必须在更新文档中注明该操作！**   

在界面勾选IP池后，WidgetLogSetting会自动跟IP池server进行沟通，实现以下功能：

### 4.1 IP池绑定

绑定只需要在第一个工序进行绑定，后面的工序只需要查询即可，无需再绑定

启用IP池功能后，`checkAllState`函数会向IP池服务器查询当前SN是否已经绑定IP。如果`checkAllState`没有返回错误，则对应注册的`WidgetLidarInfo`会自动修改为获取到的IP及端口号。第一个工序过站完成后需要判断雷达IP是否为`*************`，如果是代表雷达未绑定IP，需使用`boundLidarIP`获取雷达的IP、MSOP、DIFOP、MAC地址，并将雷达相关属性值修改为获取得到的新值，并通过`confirmBoundIPState`函数向IP池服务器反馈绑定情况

### 4.2 IP池查询

只需要正常调用`checkAllState`，如果没有返回错误，则对应注册的`WidgetLidarInfo`会自动修改为获取到的IP及端口号。

中间工序需要通过`isUseIPPool`判断是否启用IP池，如果启用了IP池，则无需再用继电器修改IP；如果未启用IP池，则一拖多时需用继电器修改IP

### 4.3 IP池解绑

解绑只有在最后一个使用到IP池的工序中使用，其他工序不能提前解绑

解绑时使用`unboundLidarIP`，如果没有返回错误，则表明解绑成功，IP已被正确回收。最后一个程序需要在成功恢复IP后再调用`unboundLidarIP`

## 5 自动化设备通信

当在界面中勾选了`使用自动化设备`，并设置好对应的IP和端口号后，重启软件，就可以自动连上自动化设备，并实时与自动化设备进行通信。

### 5.1 设备上料

当设备完成上料，并通知WidgetLogSetting后，WidgetLogSetting会自动将对应的SN填到对应的WidgetLidarInfo中，并触发`WidgetLidarInfo::signalLidarNameInputFinished`，软件接下来按照手动线正常进行测试即可

### 5.2 完成测试

+ 当过站失败时，WidgetLogSetting会自动通知自动化设备失败情况，测试软件无需进行额外操作
+ 当完成测试调用`finishProcess`时，WidgetLogSetting会自动将结果同步给自动化设备，让自动化设备完成后续下料和重新上料，测试软件无需进行额外操作。

### 5.3 中断测试

这是自动化测试和手动测试不太一样的地方，自动化设备可能会遇到卡机等异常情况，需要紧急停止测试流程。软件可以通过注册`WidgetLogSetting::signalAbort`，当收到这个signal时中断测试

### 5.4 测试中断

自动线时，如果出现了类似连接不上转台、连接不上导轨、连接不上工装治具的情况，此时无法判断产品是PASS还是FAIL，需要`setTestStatus`为`LOG_TEST_STATUS_NOT_READY`，然后调用`finishProcess`去通知自动化设备

### 5.5 心跳包

为了兼容现有的Server，增加对自动化通信的区别

- v1.0.1版本，支持旧版本，不支持心跳包发送，默认使用这个
- v1.0.2版本，支持心跳包发送

在`client`软件启动并建立好和`server`的通讯后，规律性的默认`200ms`的间隔给`server`

代码上，在创建`WidgetLogSetting`类对象的时候，传入区分版本的参数`_auto_equip_version`即可选择是否启动心跳包

```cpp
widget_log_setting_ = new rsfsc_lib::WidgetLogSetting(Q_NULLPTR, rsfsc_lib::AutoEquipVersion::V102);
```

心跳包的间隔时间是可配置的，默认是`200ms`，重启软件生效

![local_img](img/auto_equipment_beat.png)

## 6 软件异常崩溃分析
### 6.1 崩溃捕捉功能使用

- 无需额外调用，`WidgetLogSetting::init`会自动进行崩溃捕捉机制初始化工作，但软件发布时确保`编译模式`使用**RelWithDebInfo**或者**Debug**
- 另外`windows`, 要抓取`可执行文件或者库的崩溃信息`, 程序编译时需设置生成调试信息文件`.pdb`，每次版本发布可将对应的`.pdb`存放到发布仓库中以便分析崩溃信息, `cmake`可加入如下编译即可生成`*.pdb`

```cmake
if(WIN32)
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi")
  set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
  set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
endif(WIN32)
```

### 6.2 崩溃捕捉Dump文件保存
软件发生异常崩溃时自动将Dump文件保存在`~/.RoboSense/Program_name_vXX.XX.XX.XX`文件夹中，文件夹名为`软件工程名_版本`

### 6.3 Dump文件分析
#### 6.3.1 Linux
分析异常时需要将程序的可执行文件放置在`analyze_tools`文件夹根目录下，dmp文件放置`analyze_tools/dmp`文件夹下，并运行`parse_crash.sh`脚本并传入程序可执行文件名作为`参数`，如程序可执行文件名为**example_program**，则运行
```sh
./parse_crash.sh example_program
```
运行完成后`analyze_tools/dmp`文件夹下出生成`"dmp文件名_parse_result.txt"文件`，可查看崩溃时的异常代码

#### 6.3.2 Windows
- 查找对应的`.dmp`文件，拷贝到可执行文件同级目录下（程序编译需生成`.pdb`文件)

![local_img](img/windows_dmp.png)

- 用`vs`打开`dmp`文件，点击调试`使用仅限本机进行调试`

![local_img](img/windows_dmp_use_1.png)

![local_img](img/windows_dmp_use_2.png)

- 另外也可以使用`windbg`进行分析

## 7 设备状态报告功能

## 7.1 设备状态接口使用

- 设备状态新增加上传mes设备信息接口`uploadEquipmentInfo`，拥有上传设备状态到mes中， 使用方法类似其他上传mes接口

- 一般是在`过站之后`，设备连接后进行设备状态上报
```cpp
  // 设备连接
  if (!dev->connect())
  {
    widget_log_setting_->uploadEquipmentInfo(rsfsc_lib::EQUIPMENT_STATUS_ERROR);
    return false;
  }
  else
  {
    widget_log_setting_->uploadEquipmentInfo(rsfsc_lib::EQUIPMENT_STATUS_AUTO_RUNNING);
  }
```

## 7.2 获取安灯Pin接口使用

- 在`MES设置`-->`安灯设置`中可以配置灯的Pin状态，且可以通过信号`signalUpdateLightStatus`和获取安灯继电器端口名信息接口`getAndonRelayPortName()`、获取灯的Pin状态信息接口`getLightPinInfo`进行UI或设备灯的配置
```cpp
// 建立信号槽连接
QObject::connect(widget_log_setting_, &rsfsc_lib::WidgetLogSetting::signalUpdateLightStatus, this,
                   &MainWindow::slotUpdateLightStatus);

// 槽函数处理
void MainWindow::slotUpdateLightStatus(const robosense::lidar::rsfsc_lib::LightType _type)
{
  RSFSCLog::getInstance()->info("void MainWindow::slotUpdateLightStatus() -> Open light pin: {0}",
                                widget_log_setting_->getLightPinInfo().at(_type));

  // 处理设备控制及UI显示
  displayLight(label_red_light_, false);
  displayLight(label_yellow_light_, false);
  displayLight(label_green_light_, false);

  switch (_type)
  {
  case rsfsc_lib::LIGHT_TYPE_RED: displayLight(label_red_light_, true); break;
  case rsfsc_lib::LIGHT_TYPE_YELLOW: displayLight(label_yellow_light_, true); break;
  case rsfsc_lib::LIGHT_TYPE_GREEN: displayLight(label_green_light_, true); break;
  default: break;
  }
}
```

## 8 线束管理配置

**线束寿命管理**：根据`质量需求`，修改使用线束管理使用代码配置方式进行设置，`使用线束管理`则会在`checkAllState`函数中进行线束使用次数统计及检测，达到使用寿命后进行返回`CHECK_STATE_CABLE_MANAGER_ERROR`，否则会执行使用次数加1操作

通过`WidgetLogSetting::init`参数进行配置，默认采用`CableManageStatus::ENABLE`

```cpp
enum class CableManageStatus
{
  DISABLE,  // 不启动进行线束管理，零部件(组件)使用
  ENABLE    // 启动进行线束管理，整机使用
};
```

使用方式如下：

```cpp
robosense::lidar::rsfsc_lib::WidgetLogSetting::init(
    _argc, _argv, "", PROJECT_NAME, "000", WIDGET_LOG_SETTING_VERSION_MAJOR, WIDGET_LOG_SETTING_VERSION_MINOR,
    WIDGET_LOG_SETTING_VERSION_PATCH, WIDGET_LOG_SETTING_VERSION_TWEAK, true,
    robosense::lidar::rsfsc_lib::CableManageStatus::DISABLE, robosense::lidar::rsfsc_lib::MES_TYPE_ROBOSENSE);
```

**NOTE: 启动线束管理，需要将WidgetLidarInfo的线线束控件布局出来，以便进行SN的扫描输入，如下：**

```cpp
widget_lidar_info_ = new rsfsc_lib::WidgetLidarInfo(PROJECT_NAME, 1, this);
widget_lidar_info_->setLidarSNPos(0, 0, 1, 2);  // 显示布局雷达SN控件
widget_lidar_info_->setCableSNPos(1, 0, 1, 2);  // 显示布局线束SN控件
```

## 9 软件注意事项

前期我们趟了很多坑总结出来的经验：[导产软件checklist](http://gitlab.robosense.cn/system_knowledge/teamwork/factory_software_rule/blob/master/%E5%AF%BC%E4%BA%A7%E8%BD%AF%E4%BB%B6checklist.md)，请严格、仔细检查你的软件是否符合里面的每一项要求。

**注：** 规范中说的“建议”一般指强制要求
