﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "work_model/connectivity_work_model_airy_pld.h"
#include "app_event.h"
#include "mech_communication/protocol/data_struct/airy_pld.h"

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
ConnectivityWorkModelAiryPld::ConnectivityWorkModelAiryPld(rsfsc_lib::WidgetLidarInfo* _lidar_info) :
  ConnectivityWorkModel(_lidar_info)
{
  auto limit_csv = app()->getCsvUtils("airy_pld_limit");
  if (limit_csv == nullptr)
  {
    LOG_INDEX_ERROR("airy_pld_limit csv解析器失败");
    return;
  }
  setLimitCsvUtils(limit_csv);
}

bool ConnectivityWorkModelAiryPld::checkDifopData()
{
  auto difop_pkt = getDifopPacket<airy_pld::DifopPacket>();
  if (!difop_pkt.has_value())
  {
    addMeasureMessage("fsm_difop_obtain", false);
    // setWorkModelFailMsg("DIFOP数据异常");
    setFailLabel("check_difop");
    setFailMsg("获取difop数据失败");
    LOG_INDEX_ERROR("获取difop数据失败");
    return false;
  }

  if (!addDifopMeasureMessage(*difop_pkt))
  {
    setFailLabel("check_difop");
    setFailMsg("DIFOP数据异常");
    return false;
  }

  return true;
}

}  // namespace lidar
}  // namespace robosense