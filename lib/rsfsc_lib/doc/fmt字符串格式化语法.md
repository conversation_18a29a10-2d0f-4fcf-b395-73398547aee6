﻿﻿﻿

# Fmt格式化字符串语法

`fmt::format`与`fmt::print`函数使用相同的字符串格式化语法



格式字符串包含用大括号 `{}` 包围的“替换字段”。任何不包含在大括号中的内容都被视为文字文本，会原封不动地复制到输出中。如果文本内容需要输出大括号`{}`，则只需通过双写来实现转义功能``{{}}``。



替换字段的语法如下：

```bash
replacement_field ::= "{" [arg_id] [":" (format_spec | chrono_format_spec)] "}"
arg_id            ::= integer | identifier
integer           ::= digit+
digit             ::= "0"..."9"
identifier        ::= id_start id_continue*
id_start          ::= "a"..."z" | "A"..."Z" | "_"
id_continue       ::= id_start | digit
```

替换字段可以以一个 `arg_id` 开始，指定要格式化并插入到输出中的参数值。`arg_id` 后可以选择性地跟一个 `format_spec`，它前面有一个冒号 `:`。这些指定了替换值的非默认格式。

如果格式字符串中的数字 `arg_id` 按顺序为 0、1、2 等等，则可以全部省略（不能只省略部分），并且数字 0、1、2 等等会按顺序自动插入。



简单的格式字符串示例：

```c++
"First, thou shalt count to {0}" // References the first argument
"Bring me a {}"                  // Implicitly references the first argument
"From {} to {}"                  // Same as "From {0} to {1}"
```



`format_spec` 字段包含了如何呈现值的规范，包括字段宽度、对齐方式、填充、小数精度等细节。每种值类型可以定义自己的“格式化迷你语言”或对 `format_spec` 的解释。

大多数内置类型支持一种通用的格式化迷你语言，这在接下来的部分中描述。

`format_spec` 字段还可以在特定位置包含嵌套的替换字段。这些嵌套的替换字段只能包含一个参数标识符；不允许使用格式说明符。这允许动态指定值的格式化方式。



# 格式规范语言

"格式规范语言"用于定义格式字符串中替换字段的格式化方式，指定单个值的呈现方式。每种可格式化类型可以定义如何解释格式规范。

大多数内置类型实现了以下标准格式规范选项，尽管某些格式选项仅由数值类型支持。

标准格式说明符的一般形式如下：

```c++
format_spec ::= [[fill]align][sign]["#"]["0"][width]["." precision]["L"][type]
fill        ::= <a character other than '{' or '}'>
align       ::= "<" | ">" | "^"
sign        ::= "+" | "-" | " "
width       ::= integer | "{" [arg_id] "}"
precision   ::= integer | "{" [arg_id] "}"
type        ::= "a" | "A" | "b" | "B" | "c" | "d" | "e" | "E" | "f" | "F" |
                "g" | "G" | "o" | "p" | "s" | "x" | "X" | "?"
```

填充字符可以是除 '{' 或 '}' 外的任意 Unicode 码点。填充字符的存在由其后面的字符表示，后面的字符必须是对齐选项之一。如果 `format_spec` 的第二个字符不是有效的对齐选项，则假定填充字符和对齐选项均不存在。



| Option | Meaning                                                      |
| ------ | ------------------------------------------------------------ |
| `<`    | 强制字段在可用空间内左对齐（对大多数对象而言，默认行为）。   |
| `>`    | 强制字段在可用空间内右对齐（对数字而言，默认行为）。         |
| `^`    | 强制字段在可用空间内居中对齐。                               |
|        | 请注意，除非定义了最小字段宽度，否则字段宽度将始终与填充它的数据大小相同，因此在这种情况下对齐选项没有意义。 |



| Option | Meaning                                        |
| ------ | ---------------------------------------------- |
| `+`    | 表示非负数和负数都应使用符号。                 |
| `-`    | 表示仅对负数使用符号（这是默认行为）。         |
| `空格` | 表示在非负数上使用前导空格，在负数上使用减号。 |

这些选项用于定义如何在格式规范中对字段进行对齐和符号显示。



| Option      | Meaning                                                      |
| ----------- | ------------------------------------------------------------ |
| `#`         | 该选项在整数和浮点数类型中使用时，启用“替代形式”。对于整数，当使用二进制、八进制或十六进制输出时，该选项会在输出值前添加相应的前缀：`"0b"`（`"0B"`）、`"0"` 或` "0x"`（`"0X"`）。前缀的大小写由类型指示符的大小写决定，例如，对于类型 `'x'` 使用前缀 `"0x"`，对于类型` 'X'` 使用前缀 `"0X"`。对于浮点数，替代形式会导致转换结果始终包含小数点字符，即使后面没有数字。通常情况下，这些转换的结果只有在小数点后面有数字时才会显示小数点。此外，对于 `'g' `和 `'G'` 转换，结果中的尾随零不会被移除。 |
| `width`     | 十进制整数，定义最小字段宽度。如果未指定，则字段宽度将由内容决定。在宽度字段之前加上零（'0'）字符可以启用带符号的零填充，用于数字类型。这会强制填充位于符号或基数（如果有）之后但在数字之前。这仅对数字类型有效，并且在存在任何对齐说明符时会被忽略。 |
| `precision` | 十进制数，指示浮点数值使用 'f' 和 'F' 格式化时小数点后应显示的位数，或者对于使用 'g' 和 'G' 格式化的浮点数值，指示小数点前后应显示的位数。对于非数字类型，该字段指示最大字段大小，即从字段内容中将使用多少个字符。整数、字符、布尔值和指针值不允许使用精度。注意，即使指定了精度，C 字符串必须以空字符结尾。 |
| `L`         | 此选项使用当前的区域设置插入适当的数字分隔符字符。仅对数字类型有效。 |
| `type`      | 确定数据应如何呈现的类型。                                   |

这些选项用于定义如何在格式规范中对字段进行格式化，以及数据的呈现方式。



### String Presentation Types

| Type  | Meaning                                      |
| ----- | -------------------------------------------- |
| `'s'` | 字符串格式。这是字符串的默认类型，可以省略。 |
| `'?'` | 调试格式。字符串被引用，并且特殊字符被转义。 |
| none  | 同 `'s'`。                                   |

### Character Presentation Types

| Type  | Meaning                                    |
| ----- | ------------------------------------------ |
| `'c'` | 字符格式。这是字符的默认类型，可以省略。   |
| `'?'` | 调试格式。字符被引用，并且特殊字符被转义。 |
| none  | 同 `'c'`。                                 |

### Integer Presentation Types

| Type  | Meaning                                                      |
| ----- | ------------------------------------------------------------ |
| `'b'` | 二进制格式。以二进制（base 2）输出数字。使用 '#' 选项会在输出值前添加前缀 "0b"。 |
| `'B'` | 同 `'b'`，但使用大写前缀 "0B"。                              |
| `'c'` | 字符格式。将数字输出为字符。                                 |
| `'d'` | 十进制整数。以十进制（base 10）输出数字。                    |
| `'o'` | 八进制格式。以八进制（base 8）输出数字。                     |
| `'x'` | 十六进制格式。以十六进制（base 16）输出数字，使用小写字母表示大于9的数字。使用 '#' 选项会在输出值前添加前缀 "0x"。 |
| `'X'` | 同 `'x'`，但使用大写字母表示大于9的数字。使用 '#' 选项会在输出值前添加前缀 "0X"。 |
| none  | 同 `'d'`。                                                   |

整数类型的格式化选项也可以用于字符和布尔值，唯一的例外是 `'c'` 不能用于布尔值。布尔值按文本表示，未指定格式时为 true 或 false。

### Floating-Point Presentation Types

| Type  | Meaning                                                      |
| ----- | ------------------------------------------------------------ |
| `'a'` | 十六进制浮点格式。以十六进制（base 16）打印数字，使用小写字母表示大于9的数字，并使用 'p' 表示指数。 |
| `'A'` | 同 `'a'`，但使用大写字母表示大于9的数字和指数。              |
| `'e'` | 指数表示法。使用 'e' 表示指数的科学计数法打印数字。          |
| `'E'` | 同 `'e'`，但使用大写 'E' 作为分隔符。                        |
| `'f'` | 固定点表示法。将数字显示为固定点数。                         |
| `'F'` | 同 `'f'`，但将 nan 转换为 NAN，将 inf 转换为 INF。           |
| `'g'` | 通用格式。对于给定精度 p >= 1，将数字四舍五入为 p 个有效数字，然后根据其大小在固定点格式或科学计数法中格式化结果。精度为 0 相当于精度为 1。 |
| `'G'` | 同 `'g'`，但如果数字过大，则切换到 'E'。无穷大和 NaN 的表示也是大写的。 |
| none  | 类似 `'g'`，但默认精度足以表示特定值。                       |

### Pointer Presentation Types

| Type  | Meaning                                  |
| ----- | ---------------------------------------- |
| `'p'` | 指针格式。这是指针的默认类型，可以省略。 |
| none  | 同 `'p'`。                               |

这些是用于在格式字符串中指定不同类型数据呈现方式的选项。



# 计时格式化

计时持续时间和时间点类型以及`std::tm`的格式规范具有以下语法：

```c++
chrono_format_spec ::= [[fill]align][width]["." precision][chrono_specs]
chrono_specs       ::= conversion_spec |
                       chrono_specs (conversion_spec | literal_char)
conversion_spec    ::= "%" [padding_modifier] [locale_modifier] chrono_type
literal_char       ::= <a character other than '{', '}' or '%'>
padding_modifier   ::= "-" | "_"  | "0"
locale_modifier    ::= "E" | "O"
chrono_type        ::= "a" | "A" | "b" | "B" | "c" | "C" | "d" | "D" | "e" |
                       "F" | "g" | "G" | "h" | "H" | "I" | "j" | "m" | "M" |
                       "n" | "p" | "q" | "Q" | "r" | "R" | "S" | "t" | "T" |
                       "u" | "U" | "V" | "w" | "W" | "x" | "X" | "y" | "Y" |
                       "z" | "Z" | "%"
```

字面字符将不经修改地复制到输出。精度仅适用于具有浮点表示类型的 `std::chrono::duration` 类型。

可用的表现类型（`chrono_type`）包括：

| Type | Meaning                                                      |
| ---- | ------------------------------------------------------------ |
| 'a'  | 缩写的星期名称，例如 "Sat"。如果值不包含有效的星期名称，将抛出 format_error 异常。 |
| 'A'  | 完整的星期名称，例如 "Saturday"。如果值不包含有效的星期名称，将抛出 format_error 异常。 |
| 'b'  | 缩写的月份名称，例如 "Nov"。如果值不包含有效的月份，将抛出 format_error 异常。 |
| 'B'  | 完整的月份名称，例如 "November"。如果值不包含有效的月份，将抛出 format_error 异常。 |
| 'c'  | 日期和时间表示，例如 "Sat Nov 12 22:04:00 1955"。修改命令 %Ec 生成区域设置的替代日期和时间表示。 |
| 'C'  | 用整百年份表示的年份，例如 "19"。如果结果是单个十进制数字，则前面加 0。修改命令 %EC 生成区域设置的世纪替代表示。 |
| 'd'  | 月份中的日期作为十进制数表示。如果结果是单个十进制数字，则前面加 0。修改命令 %Od 生成区域设置的日期替代表示。 |
| 'D'  | 等效于 %m/%d/%y，例如 "11/12/55"。                           |
| 'e'  | 月份中的日期作为十进制数表示。如果结果是单个十进制数字，则前面加一个空格。修改命令 %Oe 生成区域设置的日期替代表示。 |
| 'F'  | 等效于 %Y-%m-%d，例如 "1955-11-12"。                         |
| 'g'  | ISO 基于周的年份的最后两个十进制数字。如果结果是单个数字，则前面加 0。 |
| 'G'  | ISO 基于周的年份作为十进制数表示。如果结果少于四位数，则左侧填充 0 到四位数。 |
| 'h'  | 等效于 %b，例如 "Nov"。                                      |
| 'H'  | 小时（24小时制）作为十进制数表示。如果结果是单个数字，则前面加 0。修改命令 %OH 生成区域设置的小时替代表示。 |
| 'I'  | 小时（12小时制）作为十进制数表示。如果结果是单个数字，则前面加 0。修改命令 %OI 生成区域设置的小时替代表示。 |
| 'j'  | 如果被格式化的类型是 duration 的特化，则表示天数的十进制数，没有填充。否则，表示年份中的天数作为十进制数。1 月 1 日为 001。如果结果少于三位数，则左侧填充 0 到三位数。 |
| 'm'  | 月份作为十进制数表示。1 月为 01。如果结果是单个数字，则前面加 0。修改命令 %Om 生成区域设置的月份替代表示。 |
| 'M'  | 分钟作为十进制数表示。如果结果是单个数字，则前面加 0。修改命令 %OM 生成区域设置的分钟替代表示。 |
| 'n'  | 换行符。                                                     |
| 'p'  | 与 12 小时制钟关联的上午/下午标识。                          |
| 'q'  | duration 的单位后缀。                                        |
| 'Q'  | duration 的数值（如通过 .count() 提取的数值）。              |
| 'r'  | 12 小时制时间，例如 "10:04:00 PM"。                          |
| 'R'  | 等效于 %H:%M，例如 "22:04"。                                 |
| 'S'  | 秒数作为十进制数表示。如果秒数少于 10，则结果前面加 0。如果输入的精度无法准确表示为秒数，则格式为固定格式的十进制浮点数，精度与输入的精度相匹配（如果转换为浮点十进制秒数时无法在 18 个小数位内完成，则精度为微秒）。小数点的字符根据区域设置进行本地化。修改命令 %OS 生成区域设置的秒数替代表示。 |
| 't'  | 水平制表符。                                                 |
| 'T'  | 等效于 %H:%M:%S。                                            |
| 'u'  | ISO 星期几作为十进制数表示（1-7），其中星期一为 1。修改命令 %Ou 生成区域设置的星期几替代表示。 |
| 'U'  | 一年中的周数作为十进制数表示。一年中的第一个星期日是第 01 周的第一天。该年同一天在之前是第 00 周。如果结果是单个数字，则前面加 0。修改命令 %OU 生成区域设置的周数替代表示。 |
| 'V'  | ISO 基于周的周数作为十进制数表示。如果结果是单个数字，则前面加 0。修改命令 %OV 生成区域设置的周数替代表示。 |
| 'w'  | 星期几作为十进制数表示（0-6），其中星期日为 0。修改命令 %Ow 生成区域设置的星期几替代表示。 |
| 'W'  | 一年中的周数作为十进制数表示。一年中的第一个星期一是第 01 周的第一天。该年同一天在之前是第 00 周。如果结果是单个数字，则前面加 0。修改命令 %OW 生成区域设置的周数替代表示。 |
| 'x'  | 日期表示，例如 "11/12/55"。修改命令 %Ex 生成区域设置的日期替代表示。 |
| 'X'  | 时间表示，例如 "10:04:00"。修改命令 %EX 生成区域设置的时间替代表示。 |
| 'y'  | 年份的最后两位十进制数表示。如果结果是单个数字，则前面加 0。修改命令 %Oy 生成区域设置的年份替代表示。修改命令 %Ey 生成 %EC 的偏移的区域设置替代表示（仅年份）。 |
| 'Y'  | 年份作为四位数的十进制数表示。如果结果少于四位数，则左侧填充 0 到四位数。修改命令 %EY 生成区域设置的完整年份替代表示。 |
| 'z'  | UTC 偏移量的 ISO 8601:2004 格式。例如 -0430 表示比 UTC 晚 4 小时 30 分钟。如果偏移量为零，则使用 +0000。修改命令 %Ez 和 %Oz 在小时和分钟之间插入冒号：-04:30。如果偏移信息不可用，则抛出 format_error 异常。 |
| 'Z'  | 时区缩写。如果时区缩写不可用，则抛出 format_error 异常。     |
| '%'  | 百分号字符。                                                 |

具有日历组成部分的格式说明符（例如 'd'，表示月份中的日期）仅适用于 `std::tm`和时间点（time points），而不适用于持续时间（durations）。

可用的填充修饰符（padding_modifier）包括：

| Type | Meaning                  |
| ---- | ------------------------ |
| '-'  | 用空格填充数值结果。     |
| '_'  | 不对数值结果进行填充。   |
| '0'  | 用零填充数值结果字符串。 |



# Range格式化规范

格式规范具有以下语法：

```shell
range_format_spec ::= ["n"][range_type][range_underlying_spec]
```

选项 `'n'` 在不包括开放和关闭括号的情况下格式化范围。

`'n'`选项将范围格式化为不包括开头和结尾括号的形式。

range类型（range_type）的可用表现类型如下：

| 类型   | 意义                               |
| ------ | ---------------------------------- |
| none   | 默认格式。                         |
| `'s'`  | 字符串格式。范围将按字符串格式化。 |
| `'?s'` | 调试格式。范围按转义字符串格式化。 |

如果`range`类型为 `'s'` 或 `'?s'`，则`range`元素类型必须为字符类型。`'n'`选项和`range_underlying_spec`与`'s'`和`'?s'`是互斥的。

`range`底层规范（`range_underlying_spec`）根据范围元素类型的格式化器进行解析。

默认情况下，字符或字符串range会被转义并带引号打印。但是，如果提供了任何`range_underlying_spec`（即使为空），则根据提供的规范打印字符或字符串。

```c++
fmt::print("{}", std::vector{10, 20, 30});
// Output: [10, 20, 30]
fmt::print("{::#x}", std::vector{10, 20, 30});
// Output: [0xa, 0x14, 0x1e]
fmt::print("{}", std::vector{'h', 'e', 'l', 'l', 'o'});
// Output: ['h', 'e', 'l', 'l', 'o']
fmt::print("{:n}", std::vector{'h', 'e', 'l', 'l', 'o'});
// Output: 'h', 'e', 'l', 'l', 'o'
fmt::print("{:s}", std::vector{'h', 'e', 'l', 'l', 'o'});
// Output: "hello"
fmt::print("{:?s}", std::vector{'h', 'e', 'l', 'l', 'o', '\n'});
// Output: "hello\n"
fmt::print("{::}", std::vector{'h', 'e', 'l', 'l', 'o'});
// Output: [h, e, l, l, o]
fmt::print("{::d}", std::vector{'h', 'e', 'l', 'l', 'o'});
// Output: [104, 101, 108, 108, 111]
```

