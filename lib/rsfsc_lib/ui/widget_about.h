﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   widget_about.h
 * <AUTHOR> Chen (<EMAIL>)
 * @brief  a widget to show about for RoboSense software 
 **/
#ifndef ROBOSENSE_LIDAR_WIDGET_ABOUT_H
#define ROBOSENSE_LIDAR_WIDGET_ABOUT_H
#include <QtCore/QString>
#include <QtGui/QShowEvent>
#include <QtWidgets/QDialog>

class QTabWidget;
class QWidget;
class QLabel;

namespace robosense
{
namespace lidar
{
class WidgetAbout : public QDialog
{
  Q_OBJECT
public:
  explicit WidgetAbout(QWidget* _parent);
  explicit WidgetAbout(WidgetAbout&&)      = delete;
  explicit WidgetAbout(const WidgetAbout&) = delete;
  WidgetAbout& operator=(WidgetAbout&&) = delete;
  WidgetAbout& operator=(const WidgetAbout&) = delete;
  ~WidgetAbout() override                    = default;
  void setYearStartCopyright(const unsigned int _year);
  void setContactEmail(const QString& _email);
  void setBuildCommit(const QString& _build_commit);
  void setBuildTime(const QString& _build_time);
  void setVersionStr(const QString& _version_str);
  void setBrief(const QString& _brief);
  void setName(const QString& _name);

protected:
  void showEvent(QShowEvent* _event) override;

private:
  QTabWidget* tab_widget_;
  unsigned int year_start_copyright_;
  QString contact_email_;
  QString project_build_commit_;
  QString project_build_time_;
  QString project_version_str_;
  QString project_brief_;
  QString project_name_;

  QLabel* label_contact_email_;
  QLabel* label_build_commit_;
  QLabel* label_build_time_;
  QLabel* label_version_;
  QLabel* label_brief_;

  QLabel* label_robosense_license_;
  QLabel* label_robosense_license_title_;

  static void logError(const QString& _msg);
  static void logInfo(const QString& _msg);
  QWidget* setupLayoutInfo();
};  // WidgetAbout
}  // namespace lidar
}  // namespace robosense

#endif  // ROBOSENSE_LIDAR_WIDGET_ABOUT_H
