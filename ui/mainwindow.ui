<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="1" column="1">
     <widget class="robosense::lidar::rsfsc_lib::MessageBrowser" name="message_browser" native="true"/>
    </item>
    <item row="0" column="0" rowspan="2">
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="QLabel" name="label_logo">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="pixmap">
         <pixmap resource="../resource/resource.qrc">:/img/logo.png</pixmap>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_title">
        <property name="text">
         <string>TextLabel</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="lidar_info_groupbox">
        <property name="maximumSize">
         <size>
          <width>300</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="title">
         <string>雷达信息</string>
        </property>
        <layout class="QGridLayout" name="gridLayout_3"/>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>100</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="button_one_key_run">
          <property name="text">
           <string>一键运行</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="verticalSpacer_2">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>120</height>
         </size>
        </property>
        <property name="title">
         <string>盒子</string>
        </property>
        <layout class="QGridLayout" name="gridLayout_2">
         <item row="2" column="1">
          <widget class="QPushButton" name="button_get_current">
           <property name="text">
            <string>获取电流</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QComboBox" name="combobox_box_port_name"/>
         </item>
         <item row="0" column="0">
          <widget class="QPushButton" name="button_refresh_port_name">
           <property name="text">
            <string>刷新串口</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label">
           <property name="maximumSize">
            <size>
             <width>90</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>盒子串口名:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <widget class="QPushButton" name="button_get_sync">
           <property name="maximumSize">
            <size>
             <width>100</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>获取sync</string>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QPushButton" name="button_get_firmware">
           <property name="maximumSize">
            <size>
             <width>100</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>获取固件</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QCheckBox" name="checkbox_switch_power">
           <property name="text">
            <string>上下电</string>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QComboBox" name="combobox_box_lidar_index"/>
         </item>
         <item row="0" column="1">
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>雷达序号:</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>27</height>
    </rect>
   </property>
   <widget class="QMenu" name="menu_setting">
    <property name="title">
     <string>设置</string>
    </property>
    <addaction name="action_para_setting"/>
    <addaction name="action_mes_setting"/>
   </widget>
   <widget class="QMenu" name="menu_help">
    <property name="title">
     <string>帮助</string>
    </property>
    <addaction name="action_about"/>
   </widget>
   <addaction name="menu_setting"/>
   <addaction name="menu_help"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="action_para_setting">
   <property name="text">
    <string>参数设置</string>
   </property>
  </action>
  <action name="action_mes_setting">
   <property name="text">
    <string>mes设置</string>
   </property>
  </action>
  <action name="action_about">
   <property name="text">
    <string>关于</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>robosense::lidar::rsfsc_lib::MessageBrowser</class>
   <extends>QWidget</extends>
   <header>widgets/message_browser.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../resource/resource.qrc"/>
 </resources>
 <connections/>
</ui>
