﻿cmake_minimum_required(VERSION 3.14.0)

string(TIMESTAMP PROJECT_COMPILE_DATE %Y%m%d)
project(mech_comm_widget VERSION 0.0.1.${PROJECT_COMPILE_DATE})

cmake_policy(SET CMP0048 NEW)
if(WIN32)
  cmake_policy(SET CMP0074 NEW)
endif(WIN32)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

find_package(
  Qt5
  COMPONENTS Widgets
  REQUIRED)

add_library(${PROJECT_NAME} STATIC mech_comm_widget.cpp include/mech_communication/widgets/mech_comm_widget.h)

target_link_libraries(${PROJECT_NAME} PRIVATE Qt5::Widgets mech_communication)

target_include_directories(${PROJECT_NAME} PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/include)
# autouic automoc
set_target_properties(
  ${PROJECT_NAME}
  PROPERTIES AUTOMOC ON
             AUTORCC ON
             AUTOUIC ON)
