﻿set(CMAKE_INSTALL_RPATH ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME})
set(CMAKE_EXE_LINKER_FLAGS "-Wl,--disable-new-dtags")
set(CMAKE_BUILD_TYPE RelWithDebInfo)
set(EXAMPLE_NAME lidar_communication_box_example)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++14")
set(CMAKE_INSTALL_RPATH ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME})

find_package(
  Qt5
  COMPONENTS Widgets Network SerialPort Core
  REQUIRED)

find_package(RSFSCLog REQUIRED)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

add_executable(${EXAMPLE_NAME})

target_include_directories(${EXAMPLE_NAME} SYSTEM PRIVATE ${PROJECT_SOURCE_DIR} ${Qt5Widgets_INCLUDE_DIRS}
                                                          ${Qt5Network_INCLUDE_DIRS})

if(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
  target_compile_options(${EXAMPLE_NAME} PUBLIC /O2 /utf-8)
  target_link_libraries(${EXAMPLE_NAME} PRIVATE ${PROJECT_NAME}_shared Qt5::Core Qt5::Widgets Qt5::Network
                                                Qt5::SerialPort RSFSCLog::RSFSCLog)
else()
  target_compile_options(${EXAMPLE_NAME} PUBLIC -fPIC -Wall -O3)
  target_link_libraries(
    ${EXAMPLE_NAME}
    PRIVATE ${PROJECT_NAME}
            Qt5::Core
            Qt5::Widgets
            Qt5::Network
            Qt5::SerialPort
            pthread
            RSFSCLog::RSFSCLog)
endif()

target_sources(${EXAMPLE_NAME} PRIVATE example.cpp main_window.cpp main_window.h)

set_target_properties(
  ${EXAMPLE_NAME}
  PROPERTIES CXX_STANDARD 14
             CXX_STANDARD_REQUIRED YES
             CXX_EXTENSIONS NO)
