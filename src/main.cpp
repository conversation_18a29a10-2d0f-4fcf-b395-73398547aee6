﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "app_controller.h"
#include "config.h"
#include "mainwindow.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "widget_log_setting.h"

#include <cmath>
#include <csignal>

#include <QApplication>
#include <QThread>

void sigHandle(int _sig)
{
  LOG_INFO("Signal {0} received, exit...", _sig);
  QApplication::exit();  //会调用~MainWindow()
}

int main(int _argc, char** _argv)
{
  if (signal(SIGINT, sigHandle) == SIG_ERR)
  {
    LOG_ERROR("Error: Unable to set signal handler");
    return EXIT_FAILURE;
  }

  robosense::lidar::rsfsc_lib::WidgetLogSetting::init(
    _argc, _argv, "", PROJECT_NAME, PROJECT_CODE, PROJECT_VERSION_MAJOR, PROJECT_VERSION_MINOR, PROJECT_VERSION_PATCH,
    PROJECT_VERSION_TWEAK, std::string(CMAKE_BUILD_TYPE) == "Debug",
    robosense::lidar::rsfsc_lib::CableManageStatus::ENABLE);  // 改为 ENABLE

  QApplication q_app(_argc, _argv);
  // Q_INIT_RESOURCE(resource);  // https://doc.qt.io/qt-5/resources.html
  MainWindow window(nullptr);  // 修改这里
  window.show();
  QApplication::connect(&q_app, &QApplication::lastWindowClosed, &q_app, &QApplication::quit);

  // controller
  robosense::lidar::AppController app_controller(&window);
  QThread controller_thread;
  app_controller.moveToThread(&controller_thread);

  controller_thread.start();
  int result = QApplication::exec();

  controller_thread.quit();
  controller_thread.wait();

  return result;
}
