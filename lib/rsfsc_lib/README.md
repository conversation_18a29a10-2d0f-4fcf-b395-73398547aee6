# 工厂软件通用库

rsfsc_lib->RS Factory Software Common Library

本仓库包含一些测试软件几乎必须要用到的功能，主要包含三部分：

+ **widget_log_setting.h**：跟MES、测试数据log相关的功能
+ **widget_lidar_info.h**：跟雷达信息相关的，例如SN、IP、项目编号等
+ **csv_parser.h**：跟配置文件相关的，例如寄存器配置表格、limit配置表格
+ **rsfsc_common.hpp**：部分软件需要用到的通用功能，例如窗口片折射因子计算
+ **rsfsc_qsettings.h**：与用户管理系统绑定的QSetting
+ **rsfsc_log.h**：通用log模块
+ **rsfsc_msg.h**：一些通用消息类

## 1 使用办法

本次更新增加了rsfsc_lib自己的`CMakeList.txt`,使用时只需要在自己工程的`CMakeList.txt`中增加以下两行代码即可：
 ```cmake
 add_subdirectory(lib/rsfsc_lib)

 target_link_libraries(${PROJECT_NAME} rsfsc_lib)
 ```



然后你需要使用什么功能，就参考`doc/xxx使用手册.md`，里面有每个模块的详细用法

**注**：之前使用的相关功能的源代码可以删除了

## 注意事项

+ limit和register文件务必设置成`UTF-8 with BOM`格式且行尾序列为`CRLF`，否则其他人在Windows下打开可能中文乱码或无法通过配置文件格式校验