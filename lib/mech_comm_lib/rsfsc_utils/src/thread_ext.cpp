﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "rsfsc_utils/thread_ext.h"
#include <chrono>
#include <climits>
#include <ctime>
#include <iostream>
#include <memory>
#include <utility>

#ifdef _WIN32
#  include <windows.h>
#else
#  include <sys/time.h>
#endif

namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
constexpr uint32_t MAX_TIME = 50000;

Thread::Thread(Thread&& _other) noexcept :
  run_flag_(_other.run_flag_),
  thread_status_(_other.thread_status_),
  ptr_runnable_(std::move(_other.ptr_runnable_)),
  ptr_thread_(std::move(_other.ptr_thread_))
{}

Thread& Thread::operator=(Thread&& _other) noexcept
{
  ptr_thread_    = std::move(_other.ptr_thread_);
  thread_status_ = _other.thread_status_;
  run_flag_      = _other.run_flag_;
  ptr_runnable_  = _other.ptr_runnable_;
  return *this;
}

Thread::~Thread()
{
  if (ptr_thread_)
  {
    stop();
  }
  else
  {
    return;
  }
  if (ptr_thread_->joinable())
  {
    ptr_thread_->join();
  }
}

std::unique_ptr<Thread> Thread::clone() { return std::make_unique<Thread>(std::move(*this)); }

bool Thread::start(std::shared_ptr<Runnable> _ptr_runnable)
{
  thread_status_ = STATE_INIT;
  run_flag_      = false;
  ptr_runnable_  = std::move(_ptr_runnable);
  if (ptr_thread_ && ptr_thread_->joinable())
  {
    stop(500);
    ptr_thread_->join();
  }
  ptr_thread_ = std::make_unique<std::thread>(&Thread::threadMain, this);
  if (ptr_thread_ == nullptr)
  {
    std::cout << "Thread create error " << ptr_runnable_->className() << std::endl;
    return false;
  }
  std::cout << "Thread create finish: " << ptr_runnable_->className() << std::endl;
  return true;
}

bool Thread::stop() { return stop(-1); }

bool Thread::stop(int _ms_timeout)
{
  if (!ptr_thread_)
  {
    return true;
  }
  if (!run_flag_)
  {
    return true;
  }

  std::cout << "try to stop thread, id: " << ptr_thread_->get_id() << std::endl;
  std::cout << "msTimeout: " << _ms_timeout << std::endl;
  run_flag_ = false;
  if (_ms_timeout < 0)
  {
    _ms_timeout = MAX_TIME;
  }
#if defined(_WIN32)
  _ms_timeout = (std::max)(10, _ms_timeout);
#else
  _ms_timeout = std::max(10, _ms_timeout);
#endif
  while (_ms_timeout > 0)
  {
    if (thread_status_ == STATE_EXIT)
    {
      if (ptr_thread_->joinable())
      {
        ptr_thread_->join();
      }
      ptr_thread_ = nullptr;
      return true;
    }
    msleep(10);
    _ms_timeout -= 10;
  }
  std::cout << "Thread stop timeout: " << ptr_runnable_->className() << std::endl;
  return false;
}

bool Thread::isRunning()
{
  if (!ptr_thread_)
  {
    return false;
  }
  if ((thread_status_ == STATE_RUNNING) && run_flag_)
  {
    return true;
  }
  return false;
}

void Thread::usleep(int _us)
{
  _us = (_us <= 0) ? 1 : _us;
  std::this_thread::sleep_for(std::chrono::microseconds(_us));
}

void Thread::msleep(int _ms)
{
  _ms = (_ms <= 0) ? 1 : _ms;
  std::this_thread::sleep_for(std::chrono::milliseconds(_ms));
}

int Thread::threadID()
{
  std::thread::id tid = std::this_thread::get_id();
  size_t id_hash      = std::hash<std::thread::id> {}(tid);
  return static_cast<int>(id_hash);
}

void Thread::threadMain()
{
  while (!ptr_thread_)
  {
    Thread::msleep(10);
  }
  thread_status_ = STATE_RUNNING;
  if (ptr_runnable_ != nullptr)
  {
    run_flag_ = true;
    std::cout << static_cast<uint32_t>(threadID()) << " Thread start. " << std::endl;
    ptr_runnable_->run(*this);
    std::cout << static_cast<uint32_t>(threadID()) << " Thread stopped" << std::endl;
    ptr_runnable_ = nullptr;
    run_flag_     = false;
  }
  thread_status_ = STATE_EXIT;
}

ThreadPool::ThreadPool(int _thread_num) : is_running_(true), threads_num_(_thread_num) { createThreads(); }

ThreadPool::~ThreadPool() { stop(); }

int ThreadPool::createThreads()
{
  for (int i = 0; i < threads_num_; i++)
  {
    threads_.push_back(std::make_unique<std::thread>(&ThreadPool::threadFunc, this));
  }

  return 0;
}

std::size_t ThreadPool::addTask(const TaskPtr& _task)
{
  std::size_t size = 0;

  {
    std::lock_guard<std::mutex> lock(mutex_);
    task_queue_.push_back(_task);
    size = task_queue_.size();
  }
  condition_.notify_one();

  return size;
}

void ThreadPool::stop()
{
  if (!is_running_)
  {
    return;
  }

  is_running_ = false;
  condition_.notify_all();

  for (int i = 0; i < threads_num_; i++)
  {
    threads_[i]->join();
  }

  threads_.clear();
}

std::size_t ThreadPool::size()
{
  std::lock_guard<std::mutex> lock(mutex_);
  std::size_t size = task_queue_.size();
  return size;
}

ThreadPool::TaskPtr ThreadPool::take()
{
  std::unique_lock<std::mutex> lock(mutex_);
  condition_.wait(lock, [this] { return !task_queue_.empty() || !is_running_; });
  if (!is_running_)
  {
    return nullptr;
  }

  TaskPtr task = task_queue_.front();
  task_queue_.pop_front();
  return task;
}

/*寻找满足条件的任务执行*/
void* ThreadPool::threadFunc(void* _arg)
{
  std::thread::id this_id = std::this_thread::get_id();

  ThreadPool* pool = static_cast<ThreadPool*>(_arg);
  while (pool->is_running_)
  {
    ThreadPool::TaskPtr task = pool->take();
    if (task)
    {
      task->run();
      //   task();
    }
    else
    {
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      std::cout << "threads:" << this_id << std::endl;
    }
  }

  return nullptr;
}

/*将此函数放在线程主函数的最后，便于线程之间的同步结束*/
void Barrier::waitFinish()
{
  std::unique_lock<std::mutex> lock(mutex_);
  count_++;
  if (count_ == thread_count_)
  {
    count_ = 0;
    release_.store(true);
    condition_.notify_all();
  }
  else
  {
    condition_.wait(lock, [&] { return release_.load(); });
  }
}

void Barrier::finish()
{
  count_++;
  if (count_ == thread_count_)
  {
    count_ = 0;
    release_.store(true);
    condition_.notify_all();
  }
}

void Barrier::abort()
{
  release_.store(true);
  condition_.notify_all();
}

}  // namespace lidar
}  // namespace robosense
