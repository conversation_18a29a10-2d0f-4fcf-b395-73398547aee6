/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef CONNECTIVITY_WORK_MODEL_H
#define CONNECTIVITY_WORK_MODEL_H

#include "data_struct.h"
#include "lidar_communication_box/src/lidar_communication_box.h"
#include "rsfsc_fsm/finite_state_machine.h"
#include "rsfsc_fsm/mech/mech_work_model.h"
#include "widgets/widget_para_setting.h"
#include <QDir>
#include <widget_log_setting.h>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

namespace rsfsc_lib
{
class WidgetLidarInfo;
}  // namespace rsfsc_lib

class ConnectivityWorkModel : public MechWorkModel
{
public:
  struct Path
  {
    QDir data_dir;
    QDir result_dir;
    QDir temp_dir;
  };

  explicit ConnectivityWorkModel(rsfsc_lib::WidgetLidarInfo* _lidar_info);
  ConnectivityWorkModel(const ConnectivityWorkModel&) = delete;
  ConnectivityWorkModel(ConnectivityWorkModel&&)      = delete;
  ConnectivityWorkModel& operator=(const ConnectivityWorkModel&) = delete;
  ConnectivityWorkModel& operator=(ConnectivityWorkModel&&) = delete;
  ~ConnectivityWorkModel() override                         = default;

  [[nodiscard]] rsfsc_lib::WidgetLogSetting* getWidgetLogSetting() override;

  virtual std::shared_ptr<FiniteStateMachine<ConnectivityWorkModel>> createFsm();

  void setParaInfo(const ParaInfo& _para_info) { para_info_.clone(_para_info); }
  void setParaInfo(const std::shared_ptr<ParaInfo>& _para_info) { para_info_.clone(_para_info); }
  ParaInfo& getParaInfo() { return para_info_; };

  void setTestState(const TestState _test_state)
  {
    test_state_ = _test_state;
    if (update_test_state_cb_)
    {
      update_test_state_cb_(test_state_);
    }
  }
  void updateTestState()
  {
    if (update_test_state_cb_)
    {
      update_test_state_cb_(test_state_);
    }
  }
  [[nodiscard]] TestState getTestState() const { return test_state_; }

  void setUpdateTestStateCb(const std::function<void(const TestState)>& _update_test_state_cb)
  {
    update_test_state_cb_ = _update_test_state_cb;
  }

  void setFailLabel(const QString& _fail_label) { fail_label_ = _fail_label; }
  [[nodiscard]] const QString& getFailLabel() const { return fail_label_; }

  void setFailMsg(const QString& _fail_msg) { fail_msg_ = _fail_msg; }
  [[nodiscard]] const QString& getFailMsg() const { return fail_msg_; }

  void setLidarCommunicationBoxPtr(std::shared_ptr<LidarCommunicationBox>& _lidar_com)
  {
    lidar_comm_box_ptr_ = _lidar_com;
  }
  std::shared_ptr<LidarCommunicationBox> getBoxPtr() { return lidar_comm_box_ptr_; }

  bool turnOnRelay();
  bool turnOffRelay();
  bool turnRelay(const bool _is_open);
  bool toggleRelay(const int _interval = 3000);

  bool initPath(const Path& _path);
  [[nodiscard]] const Path& getPath() const { return path_; }

  template <typename T>
  bool addDifopMeasureMessage(const T& _difop_pkt);
  bool addMeasureMessage(const QString& _name, const bool _data);
  bool addMeasureMessage(const QString& _name, const double _data, const rsfsc_lib::MeasureDataType _data_type);
  bool addMeasureMessage(const QString& _name, const std::string& _data);

  // mes
  bool requireVbd();

  bool setTimeSyncModeGps();
  bool restoreTimeSyncMode();

  bool checkMsopData();
  virtual bool checkDifopData();

  template <typename T>
  std::optional<T> getDifopPacket(const int _timeout_ms = 10000);

  std::vector<mech::MsopPacket> getMsopData(const int _count, const int _timeout = 60000);
  bool checkMsopData(std::vector<mech::MsopPacket>& _msop_pkt_vec);
  bool powerOn();
  bool powerOff();
  QDateTime getLastPowerOnTime();

  bool writeSn();

private:
  std::function<void(const TestState)> update_test_state_cb_;
  Path path_;
  ParaInfo para_info_;
  QString fail_label_;
  QString fail_msg_;
  TestState test_state_;
  bool is_need_restore_time_sync_mode_ = false;
  std::shared_ptr<LidarCommunicationBox> lidar_comm_box_ptr_;

  mech::DifopPacket difop_pkt_;
  QDateTime last_difop_time_;

  float vbd_voltage_ = NAN;  // VBD电压

  QDateTime last_power_on_time_;
};

}  // namespace lidar
}  // namespace robosense
#endif  // CONNECTIVITY_WORK_MODEL_H