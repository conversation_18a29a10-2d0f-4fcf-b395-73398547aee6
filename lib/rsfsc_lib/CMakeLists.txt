﻿cmake_minimum_required(VERSION 3.16)
# add_library(rsfsc_lib SHARED IMPORTED GLOBAL)
project(rsfsc_lib)
add_library(rsfsc_lib INTERFACE)

find_package(
  Qt5
  COMPONENTS Widgets
  REQUIRED)

# =========================
# Get System Name
# =========================
if(CMAKE_SYSTEM_NAME MATCHES "Windows")
  set(LSB_CODENAME msvc2017_64)
elseif(CMAKE_SYSTEM_NAME MATCHES "Linux")
  find_program(LSB_EXEC lsb_release)

  if(LSB_EXEC MATCHES "NOTFOUND")
    message(
      "\n lsb_release not found, please install using: \n\t sudo apt install lsb_release\n"
    )
  endif()

  execute_process(
    COMMAND ${LSB_EXEC} -cs
    OUTPUT_VARIABLE LSB_CODENAME
    OUTPUT_STRIP_TRAILING_WHITESPACE)
else()
  message(FATAL_ERROR "unsupported system")
endif()

if(CMAKE_SYSTEM_NAME MATCHES "Windows")
  set(RSFSCLIB_PATH ${CMAKE_CURRENT_LIST_DIR}/${LSB_CODENAME}/rsfsc_lib.lib)
  # add to output pdb file
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi")
  set(CMAKE_EXE_LINKER_FLAGS_RELEASE
      "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
  set(CMAKE_SHARED_LINKER_FLAGS_RELEASE
      "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
elseif(CMAKE_SYSTEM_NAME MATCHES "Linux")
  set(RSFSCLIB_PATH ${CMAKE_CURRENT_LIST_DIR}/${LSB_CODENAME}/librsfsc_lib.so)
else()
  message(FATAL_ERROR "unsupported system")
endif()

list(APPEND CMAKE_PREFIX_PATH
     ${CMAKE_CURRENT_LIST_DIR}/${LSB_CODENAME}/lib/cmake)
find_package(RSFSCLog REQUIRED)

message(STATUS "RSFSCLIB_PATH : " ${RSFSCLIB_PATH})
get_target_property(RSFSCLOG_PATH RSFSCLog::RSFSCLog LOCATION)
message(STATUS "RSFSCLOG_PATH : " ${RSFSCLOG_PATH})

target_link_libraries(rsfsc_lib INTERFACE RSFSCLog::RSFSCLog ${RSFSCLIB_PATH})
target_include_directories(
  rsfsc_lib SYSTEM INTERFACE ${CMAKE_CURRENT_LIST_DIR}
                             ${CMAKE_CURRENT_LIST_DIR}/include)

# set_target_properties(rsfsc_lib PROPERTIES IMPORTED_LOCATION ${RSFSCLIB_PATH}
# INTERFACE_INCLUDE_DIRECTORIES ${CMAKE_CURRENT_LIST_DIR} )

if(${CMAKE_PROJECT_NAME} EQUAL ${PROJECT_NAME})
  set(INSTALL_SUB_DIR
      ${PROJECT_NAME}
      CACHE STRING "${PROJECT_NAME} or ${CMAKE_PROJECT_NAME}")
else()
  set(INSTALL_SUB_DIR
      ${CMAKE_PROJECT_NAME}
      CACHE STRING "${PROJECT_NAME} or ${CMAKE_PROJECT_NAME}")
endif()

message(STATUS "rsfsc_lib, install sub dir : " ${INSTALL_SUB_DIR})

install(
  DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/${LSB_CODENAME}/
  DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${INSTALL_SUB_DIR}
  USE_SOURCE_PERMISSIONS
  DIRECTORY_PERMISSIONS
    OWNER_EXECUTE
    OWNER_WRITE
    OWNER_READ
    GROUP_EXECUTE
    GROUP_READ
    WORLD_EXECUTE
    WORLD_READ)
