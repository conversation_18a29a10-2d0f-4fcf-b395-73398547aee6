﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

/******************************************************************************
Attention:
This file is used to test the function readability-identifier-naming of clang-tidy.
It contains all kinds of identifier-naming problems and it's total a NEGATIVE example.
DO NOT imitate it please.
******************************************************************************/
constexpr int global_const_variable = 0;
namespace identifierNamingTest
{

#define macro_identifierNamingTest

int GlobalVariable                     = 0;
constexpr int ns_global_const_variable = 0;

class identifier_naming_test_class
{
public:
  identifier_naming_test_class();
  ~identifier_naming_test_class();

protected:
  void func_abc(int parameter) const {}
  template <class type>
  void func_temp(const type& parameter) {};

private:
  bool variable;
  const int const_value                = 0;
  static constexpr int constexpr_value = 0;
  static int static_value;
};

int identifier_naming_test_class::static_value = 0;

enum identifier_naming_test_enum
{
  value0,
  value1,
  value2
};

enum class identifier_naming_test_enum_class
{
  value0,
  value1,
  value2
};

union identifier_naming_test_union
{
  int a;
  bool b;
  char c;
};

struct MyStruct
{
  int StructMember;
  char struct_member_;
};

using alias_of_abc = identifier_naming_test_class;
typedef identifier_naming_test_class alias_of_def;

}  // namespace identifierNamingTest
