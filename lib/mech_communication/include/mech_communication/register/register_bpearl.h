﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * Definitions of Register.
 *
 * @defgroup MOD_register
 * 
 * Copyright ((C) 2014 - 2020 RoboSense, Co., Ltd.  All rights reserved.
 *
 * <AUTHOR> Liu
 *
 * @attention 20210806 Create register definitions file.
 *
 */

/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef BPEARL_REGISTER_H
#define BPEARL_REGISTER_H

#include <cstdint>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
namespace bpearl
{
/* ------------------------ Include ------------------------- */

/* ------------------------ Defines ------------------------- */
/* 0x83C00600 ~ Application level custom registers */
/* Bot board peripheral registers */
constexpr uint32_t BOT_VERSION  = 0x83C00000; /* FPGA bot version information (in hex), e.g. B02R03_V01S00. */
constexpr uint32_t BOARD_ID     = 0x83C00004; /* FPGA bot board id */
constexpr uint32_t BOT_VER_DATE = 0x83C00008; /* FPGA bot build date */

constexpr uint32_t TOP_KERNEL_TEMPERATURE =
  0x83C18028; /* Low 16 bit: Top FPGA kernel temperature, calculation formula temp=(503.975*(Data/4096))-273.15 */
constexpr uint32_t BOT_KERNEL_TEMPERATURE =
  0x83C00128; /* Low 16 bit: Bot FPGA kernel temperature, calculation formula temp=(503.975*(Data/4096))-273.15 */
constexpr uint32_t TOP_UNDER_TEMPERATURE =
  0x83C1800C; /* top_fpga_rx_temp:Top receiver board temperature. calculation formula: (200 * data/4096 - 50) */
constexpr uint32_t TOP_APD_TEMPERATURE = 0x83C18004;

constexpr uint32_t BOT_PWM_FREQ = 0x83C20050;

/* Lidar current and voltage status register */
constexpr uint32_t VOL_BUS =
  0x83C00110; /* Whole machine voltage VBUS, voltage calculation formula: 41.299*(Data/4096) */
constexpr uint32_t CURRENT_BUS =
  0x83C0010C; /* Whole machine current CBUS , current calculation formula: 5.5*(Data/4096) */
constexpr uint32_t VOL_12V_BOT     = 0x83C00114; /* Bot 12v voltage, current calculation formula: 24.5*(Data/4096) */
constexpr uint32_t VOL_5V_BOT      = 0x83C00118; /* Bot 5v voltage, current calculation formula: 10*(Data/4096) */
constexpr uint32_t CURRENT_12V_BOT = 0x83C00108; /* Bot 12v current, current calculation formula: 5*(Data/4096) */
constexpr uint32_t VOL_KERNEL_1V_BOT =
  0x83C00120; /* [bit11:0]Bot FPGA kernel 1v voltage, calculation formula: 3*(Data/4096) */

constexpr uint32_t BOT_CURRENT_1         = 0x83C001F0;
constexpr uint32_t BOT_SHUNT_VOL         = 0x83C03204;
constexpr uint32_t BOT_BUS_VOL           = BOT_SHUNT_VOL;
constexpr uint32_t BOT_POWER_CONSUMPTION = 0x83C03208;
constexpr uint32_t BOT_CURRENT           = BOT_POWER_CONSUMPTION;
constexpr uint32_t BOT_28V_CURRENT       = 0x83C00144;

/* Motor mode register */
constexpr uint32_t TOP_POWER_REG = 0x83C20004; /* TOP board power-on control register. 1: power-on */
constexpr uint32_t MOTOR_SPEED_SET =
  0x83C01000; /* Set the motor speed. Motor speed can be set to 300, 600, 1200rpm/min */
constexpr uint32_t MOTOR_SPEED_REAL   = 0x83C00014; /* Real-time motor speed */
constexpr uint32_t MOTOR_PHASE_SET    = 0x83C01004; /* Motor phase-locked. 0~360 can be set */
constexpr uint32_t MOTOR_PHASE_REAL   = 0x83C00224; /* PPS trigger moment, real-time turn to angle value */
constexpr uint32_t MOTOR_PHASE_STATUS = 0x83C00230;

constexpr uint32_t BOT_REG_OPTICAL_TEST               = 0x83c03200; /* optical test, error rate */
constexpr uint32_t BOT_REG_OPTICAL_TEST_UNKOWN        = 0x83c03210; /* optical test, error rate */
constexpr uint32_t BOT_REG_OPTICAL_UPLOAD_NUM         = 0x83c03204;
constexpr uint32_t BOT_REG_OPTICAL_UPLOAD_ERROR_NUM   = 0x83c03208;
constexpr uint32_t BOT_REG_OPTICAL_DOWNLOAD_NUM       = 0x83c03214;
constexpr uint32_t BOT_REG_OPTICAL_DOWNLOAD_ERROR_NUM = 0x83c03218;

/*
TOP_COMMUNICATION_STATUS
bit0: 1:The downlink optical communication link is normal, 
      0:The downlink optical communication link is abnormal 
bit1: 1:CDR clock phase lock success, 
*/
/* Motor mode register continued */
constexpr uint32_t TOP_COMMUNICATION_STATUS = 0x83C01050; /* light status */
constexpr uint32_t MOTOR_PHASE_LOCK_STATUS  = 0x83C01090;
constexpr uint32_t MOTOR_REBOOT_FLAG        = 0x83C01088;

constexpr uint32_t SCAN_ANGLE_EN = 0x83C00300; /* Enable equal angle scan. 0x01: Enable equal angle scan */
constexpr uint32_t ANGLE_TEST_EN =
  0x83C00304; /* Analog code wheel angle enable. 0x01: Generate angle output by itself according to the current speed */

constexpr uint32_t ZERO_ANGLE_CORRECTION =
  0x83C0102C; /* Zero angle calibration. Software configuration zero angle calibration. Unit 0.01 degrees, range 0-36000, if it is negative-2 degrees, so that the conversion is 358 degrees */
constexpr uint32_t FOV_START = 0x83C01014; /* Fov start angle. No firing outside the angle. Data is also not uploaded */
constexpr uint32_t FOV_END   = 0x83C01018; /* Fov end angle. Support intermediate range across zero. */

/*
LIGHT_DEBUG
bit[15: 0] : Number of downlink exceptions
bit[23: 16] : The number of times the optical link is flipped when the downlink is abnormal
*/
constexpr uint32_t LIGHT_DEBUG = 0x83C00418;

constexpr uint32_t PULSE_CHANNEL_EN_REG =
  0x83C01058; /* 3 angle pulse output enable, corresponding bit is 1: enable, 0: not enable */
constexpr uint32_t PULSE_START_WIDTH_EN_REG =
  0x83C01080; /* Angle pulse output calculation enable, 1: enable, 0: do not enable */
constexpr uint32_t PULSE_START_ANGLE0_CPU = 0x83C0105C; /* Angle pulse start angle 1 */
constexpr uint32_t PULSE_START_ANGLE1_CPU = 0x83C01060; /* Angle pulse start angle 2 */
constexpr uint32_t PULSE_START_ANGLE2_CPU = 0x83C01064; /* Angle pulse start angle 3 */
constexpr uint32_t PULSE_WIDTH0_CPU       = 0x83C01068; /* Angle pulse width 1 */
constexpr uint32_t PULSE_WIDTH1_CPU       = 0x83C0106C; /* Angle pulse width 2 */
constexpr uint32_t PULSE_WIDTH2_CPU       = 0x83C01070; /* Angle pulse width 3 */
constexpr uint32_t PULSE_STEP0_CPU        = 0x83C01074; /* Angle pulse step 1 */
constexpr uint32_t PULSE_STEP1_CPU        = 0x83C01078; /* Angle pulse step 2 */
constexpr uint32_t PULSE_STEP2_CPU        = 0x83C0107C; /* Angle pulse step 3 */

constexpr uint32_t TIME_SYNC_MODE_REG =
  0x83C0307C; /* 100: ptp mode (default), 010: GPS mode, 000: no synchronization */
constexpr uint32_t SYNC_GPS_LOCKED_STATUS_REG = 0x83C00160; /* GPS sync status */
constexpr uint32_t PTP_SYNC_EN = 0x83C03070; /* PTP time sync enable, once a second, effective on rising edge */

constexpr uint32_t PTP_SYNC_STATUS_REG = 0x83C00600; /* Just set this value on APP side, read it from web CGI */

constexpr uint32_t GET_GPS_REQ_UTC  = 0x83C03098; /* get pl Gps time */
constexpr uint32_t SET_GPS_SECH_REG = 0x83C0308C; /* Set Gps time seconds high byte to pl */
constexpr uint32_t SET_GPS_SECL_REG = 0x83C03090; /* Set Gps time Seconds low byte to pl */
constexpr uint32_t SET_GPS_NSEC_REG = 0x83C03094; /* Set Gps time nanosecond to pl */

constexpr uint32_t SET_PTP_SECH_REG = 0x83C030A8; /* Set Ptp mode second time high to pl */
constexpr uint32_t SET_PTP_SECL_REG = 0x83C030AC; /* Set Ptp mode second time low to pl */
constexpr uint32_t SET_PTP_NSEC_REG = 0x83C030B0; /* Set Ptp mode nanosecond time to pl */

constexpr uint32_t PL_TIME_SECH_REG = 0x83C030C0; /* Difop get pl second time high */
constexpr uint32_t PL_TIME_SECL_REG = 0x83C030C4; /* Difop get pl second time low  */
constexpr uint32_t PL_TIME_NSEC_REG = 0x83C030C8; /* Difop get pl nanosecond time  */

constexpr uint32_t PC_SYNC_EN     = 0x83C03024; /* Jump edge from 0 to 1, logical take configuration time */
constexpr uint32_t PC_SYNC_YEAR   = 0x83C03000; /* Set date: Year */
constexpr uint32_t PC_SYNC_MONTH  = 0x83C03004; /* Set date: Month */
constexpr uint32_t PC_SYNC_DAY    = 0x83C03008; /* Set date: Day */
constexpr uint32_t PC_SYNC_HOUR   = 0x83C0300C; /* Set date: Hour */
constexpr uint32_t PC_SYNC_MIN    = 0x83C03010; /* Set date: Minutes */
constexpr uint32_t PC_SYNC_SEC    = 0x83C03018; /* Set date: Seconds */
constexpr uint32_t PC_SYNC_MS     = 0x83C0301C; /* Set date: Milliseconds */
constexpr uint32_t PC_SYNC_US     = 0x83C03020; /* Set date: Microsecond */
constexpr uint32_t GPS_SYNC_YEAR  = 0x83C03040; /* Time from GPS module, date: Year */
constexpr uint32_t GPS_SYNC_MONTH = 0x83C03044; /* Time from GPS module, date: Month */
constexpr uint32_t GPS_SYNC_DAY   = 0x83C03048; /* Time from GPS module, date: Day */
constexpr uint32_t GPS_SYNC_HOUR  = 0x83C0304C; /* Time from GPS module, date: Hour */
constexpr uint32_t GPS_SYNC_MIN   = 0x83C03050; /* Time from GPS module, date: Minutes */
constexpr uint32_t GPS_SYNC_SEC   = 0x83C03054; /* Time from GPS module, date: Seconds */

constexpr uint32_t PL_DATE_YEAR  = 0x83C03058;
constexpr uint32_t PL_DATE_MONTH = 0x83C0305C;
constexpr uint32_t PL_DATE_DAY   = 0x83C03060;
constexpr uint32_t PL_DATE_HOUR  = 0x83C03064;
constexpr uint32_t PL_DATE_MIN   = 0x83C03068;
constexpr uint32_t PL_DATE_SEC   = 0x83C0306C;
constexpr uint32_t PL_DATE_MS    = 0x83C03074;
constexpr uint32_t PL_DATE_US    = 0x83C03078;

constexpr uint32_t PPS_RX_NUM   = 0x83C03150;
constexpr uint32_t GPRMC_RX_NUM = 0x83C03154;
constexpr uint32_t PTP_EN_NUM   = 0x83C03158;

constexpr uint32_t DIFOP_PACKETS_GPRMC = 0x83C08000;
constexpr uint32_t GPS_BAUD_REG        = 0x83C030BC;
constexpr uint32_t PAGE_TABLE_REG      = 0x83C09000;

/* Top info to Bot */
constexpr uint32_t TOP_FPGA_VERSION = 0x83C18000; /* Top FPGA version information */
constexpr uint32_t WAVE_MODE_REG    = 0x83C1000C; /* Wave mode selection. 0x01:1 wave, 0x03:2 wave */
constexpr uint32_t WAVE_EN = 0x83C10008; /* Wave enable signal. 0x01:enable waveform, 0x00:no enable waveform */
constexpr uint32_t OVERCLOCK_MODE_SET = 0x00000000;
constexpr uint32_t TOP_FPGA_BOARD_ID  = 0x83C18034; /* Top FPGA board id */

constexpr uint32_t TOP_FPGA_RX =
  0x83C18004; /* Low 16 bit: op receives negative, calculation formula: 200*(data/4096)-50 */
constexpr uint32_t TOP_FPGA_HV =
  0x83C18008; /* High 16 bit:Top negative high voltage. calculation formula: (1.19605-(data/4096))/0.006579 */
constexpr uint32_t TOP_FPGA_AVCC_2V5 = 0x83C18008; /* Low 16 bit: Top 2.5V. calculation formula: 10*(data/4096) */
constexpr uint32_t TOP_FPGA_BOT = 0x83C1800C; /* High 16 bit: Bottom voltage. calculation formula: 200*(data/4096)-50 */
constexpr uint32_t TOP_FPGA_VBUS =
  0x83C1800C; /* Low 16 bit: Top totally input voltage. calculation formula: 24.5*(data/4096) */
constexpr uint32_t TOP_FPGA_TX_3V8 =
  0x83C18010; /* High 16 bit: Top voltage charged 3.8V. calculation formula: 10*(data/4096) */
constexpr uint32_t TOP_FPGA_N3V3 =
  0x83C18010; /* Low 16 bit: Top voltage negative 3.3V. calculation formula: (0.908696-(data/4096))/0.072464 */

constexpr uint32_t PKT_CNT =
  0x83C10080; /* The number of point cloud packets received by the bot board, obtained by sending a write register command followed by a read command */
constexpr uint32_t PKT_ERROR_CNT =
  0x83C10084; /* The number of packets of point cloud data packet crc check error received by the bot board, get the way to send the write register command first and then send the read command */
constexpr uint32_t PKT_PL2PS_CNT =
  0x83C1008C; /* The number of point cloud packets transmitted to the ps by pl is obtained by sending a write register command followed by a read command */

/* Bot Communication */
constexpr uint32_t UPDATE_CTRL = 0x83C20000;
constexpr uint32_t UPDATE_INTERRUPT_ENABLE =
  0x83C20008; /* Top upgrade command return status update interrupt enable: (not used). 0: interrupt not enabled. 1: interrupt enable */
constexpr uint32_t UPDATE_RAM_START_ADDR = 0x83C21000; /* RAM data update start address */

/* 
TOP upgrade command returns status update instructions.
1: Top upgrade command has a new answer status update.
0: TOP command did not receive an answer status 
*/
constexpr uint32_t UPDATE_INTERRUPT_STATUS = 0x83C2000C;
constexpr uint32_t UPDATE_STATUS           = 0x83C20010;
constexpr uint32_t UPDATE_INTERRUPT_CLEAR  = 0x83C20014;
constexpr uint32_t COUNT_REG               = 0x83C20024; /* Counting register */
constexpr uint32_t TOP_AVALON_CRL          = 0x83C20030; /* Top control register */
constexpr uint32_t AVALON_INTERFACE        = 0x83C20034; /* Avalon interface */

constexpr uint32_t CBT_SCALE_COEFF_START_REG   = 0x83C05000; /* Code plate collection of calibration data */
constexpr uint32_t ANGLE_INSERT_STEP_START_REG = 0x83C05200; /* Code disk interpolation step */

constexpr uint32_t TOP_CURRENT_PKT_CNT = 0x83C10040; /* TOP current send msop count */
constexpr uint32_t TOP_ADC_STABLE_STATE =
  0x83C100D0; /* TOP adc stable state, wait the register 1, then set wave mode */

/* TOP register offset */
constexpr uint32_t TOP_FPGA_TX_CTRL            = 0x3300;
constexpr uint32_t TOP_BOARD_ID                = 0x3009;
constexpr uint32_t TOP_WAVE_MODE_REG           = 0x200D;
constexpr uint32_t TOP_PERFORMANCE_MODE_REG    = 0x0000;
constexpr uint32_t TOP_NOISE_FILTER_SWITCH_REG = 0x0000;
constexpr uint32_t NONLINEAR_MAPPING_REG       = 0x256D;
constexpr uint32_t MOTOR_DIRECTION_REG         = 0x29A7;
constexpr uint32_t TOP_SWITCH_MODE_REG         = 0x2608;

constexpr uint32_t TOP_DYNAMIC_CALIBRATION_REG = 0x2231;

constexpr uint32_t TOP_REG_TX_EN_1_8         = 0x1103;
constexpr uint32_t TOP_REG_TX_EN_9_16        = 0x1104;
constexpr uint32_t TOP_REG_TX_EN_17_24       = 0x1105;
constexpr uint32_t TOP_REG_TX_EN_25_32       = 0x1106;
constexpr uint32_t TOP_REG_TX_DUAL_EN        = 0x1100;
constexpr uint32_t TOP_REG_CHARGE_ADDR_H     = 0x1220;
constexpr uint32_t TOP_REG_CHARGE_ADDR_MIN_L = 0x1221;
constexpr uint32_t TOP_REG_CHARGE_ADDR_MAX_L = 0x1222;
constexpr uint32_t TOP_REG_TX_CODE_FIX_EN    = 0x102a;
constexpr uint32_t TOP_REG_TX_CODE_VALUE     = 0x102b;
constexpr uint32_t TOP_REG_DATA_COLLECT_SWH  = 0X2602;
constexpr uint32_t TOP_REG_DATA_LOCK         = 0X2601;
constexpr uint32_t TOP_REG_CH_SEL            = 0X2600;
constexpr uint32_t TOP_REG_ECHO_AMP          = 0X2610;
constexpr uint32_t TOP_REG_SATU_NUM          = 0X2611;
constexpr uint32_t TOP_REG_AREA_GDI_H        = 0X2612;
constexpr uint32_t TOP_REG_AREA_GDI_L        = 0X2613;
constexpr uint32_t TOP_REG_DIST_GDI_H        = 0X2614;
constexpr uint32_t TOP_REG_DIST_GDI_L        = 0X2615;
constexpr uint32_t TOP_REG_CHARGE_FIX_EN     = 0x1208;
constexpr uint32_t TOP_REG_CHARGE_FIX_VALUE  = 0x1209;
constexpr uint32_t TOP_REG_GAIN_ADDR_MIN     = 0X1221;
constexpr uint32_t TOP_REG_GAIN_ADDR_MAX     = 0X1222;
constexpr uint32_t TOP_REG_AMP_MIN_H         = 0X1223;
constexpr uint32_t TOP_REG_AMP_MIN_L         = 0X1224;
constexpr uint32_t TOP_REG_AMP_MAX_H         = 0X1225;
constexpr uint32_t TOP_REG_AMP_MAX_L         = 0X1226;
constexpr uint32_t TOP_REG_SAT_NUM_THRE      = 0X1227;
constexpr uint32_t TOP_REG_REF_AMP_CFG       = 0X1228;
constexpr uint32_t TOP_REG_GAIN_ERR_CFG      = 0X1229;
constexpr uint32_t TOP_REG_AD_BASE_VALUE     = 0X122a;
// constexpr uint32_t TOP_REG_VBR_ADJ_EN           = 0X3007; // This is commented out, so it's not included in the conversion.
constexpr uint32_t TOP_REG_VBR_ADJ_EN         = 0X3000;
constexpr uint32_t TOP_REG_TMPDIF             = 0X3001;
constexpr uint32_t TOP_REG_VBR_C              = 0X3002;
constexpr uint32_t TOP_REG_TMPC               = 0X3003;
constexpr uint32_t TOP_REG_TMPCOE             = 0X3004;
constexpr uint32_t TOP_REG_WORKCOE            = 0X3005;
constexpr uint32_t TOP_REG_VBR_LIMIT_EN       = 0X3006;
constexpr uint32_t TOP_REG_VBR_H              = 0X3007;
constexpr uint32_t TOP_REG_VBR_L              = 0X3008;
constexpr uint32_t TOP_REG_DAC1_CFG_DATA_H    = 0X3090;
constexpr uint32_t TOP_REG_DAC1_CFG_DATA_L    = 0X3091;
constexpr uint32_t TOP_REG_DAC2_CFG_DATA_H    = 0X3092;
constexpr uint32_t TOP_REG_DAC2_CFG_DATA_L    = 0X3093;
constexpr uint32_t TOP_REG_DAC3_CFG_DATA_H    = 0X3094;
constexpr uint32_t TOP_REG_DAC3_CFG_DATA_L    = 0X3095;
constexpr uint32_t TOP_REG_DAC4_CFG_DATA_H    = 0X3096;
constexpr uint32_t TOP_REG_DAC4_CFG_DATA_L    = 0X3097;
constexpr uint32_t TOP_REG_DAC_CFG_EN         = 0X3098;
constexpr uint32_t TOP_REG_CHNL_SWH           = 0X2603;
constexpr uint32_t TOP_REG_MODE_SWH           = 0X2604;
constexpr uint32_t TOP_REG_ADC_START_POINT_H  = 0X2605;
constexpr uint32_t TOP_REG_ADC_START_POINT_L  = 0X2606;
constexpr uint32_t TOP_REG_CYCLE_CNT          = 0X2607;
constexpr uint32_t TOP_REG_PWR_DEBUG_SEL      = 0X260A;
constexpr uint32_t TOP_REG_VIO_LD             = 0X3200;
constexpr uint32_t TOP_REG_VIO_INC            = 0X3201;
constexpr uint32_t TOP_REG_VIO_DEC            = 0X3202;
constexpr uint32_t TOP_REG_VIO_REGRST         = 0X3203;
constexpr uint32_t TOP_REG_SATU_CMP_EN        = 0X2231;
constexpr uint32_t TOP_REG_SATU_RAM_WR_EN     = 0X2232;
constexpr uint32_t TOP_REG_SATU_RAM_WR_DATA   = 0X2233;
constexpr uint32_t TOP_REG_SATU_RAM_WR_ADDR_H = 0X2234;
constexpr uint32_t TOP_REG_SATU_RAM_WR_ADDR_L = 0X2235;
constexpr uint32_t TOP_REG_SATU_RAM_RD_DATA   = 0X2236;
constexpr uint32_t TOP_REG_PATH_CHOOSE        = 0x200d;
constexpr uint32_t TOP_REG_AREA_THRE          = 0x201a;
constexpr uint32_t TOP_REG_DIS_CMP_SAT_EN     = 0X2018;
constexpr uint32_t TOP_REG_REF_DAS_MUX        = 0X2100;
constexpr uint32_t TOP_REG_STA_CMP_SWH        = 0x2200;

}  // namespace bpearl

}  // namespace lidar
}  // namespace robosense

#endif  //BPEARL_REGISTER_H
