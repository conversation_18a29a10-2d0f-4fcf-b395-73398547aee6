# - Config file for the project RS<PERSON><PERSON><PERSON>
# It defines the following variables
#  RSFSCLog_FOUND - true if <PERSON>FSCLog is found
#  RSFSCLog_INCLUDE_DIRS - include directories for RSFSCLog
#  RSFSCLog_LIBRARIES - libraries to link against for RS<PERSON>CLog


####### Expanded from @PACKAGE_INIT@ by configure_package_config_file() #######
####### Any changes to this file will be overwritten by the next CMake run ####
####### The input file was RSFSCLogConfig.cmake.in                            ########

get_filename_component(PACKAGE_PREFIX_DIR "${CMAKE_CURRENT_LIST_DIR}/../../../" ABSOLUTE)

macro(set_and_check _var _file)
  set(${_var} "${_file}")
  if(NOT EXISTS "${_file}")
    message(FATAL_ERROR "File or directory ${_file} referenced by variable ${_var} does not exist !")
  endif()
endmacro()

macro(check_required_components _NAME)
  foreach(comp ${${_NAME}_FIND_COMPONENTS})
    if(NOT ${_NAME}_${comp}_FOUND)
      if(${_NAME}_FIND_REQUIRED_${comp})
        set(${_NAME}_FOUND FALSE)
      endif()
    endif()
  endforeach()
endmacro()

####################################################################################

include(CMakeFindDependencyMacro)

# Handle dependencies


# Ensure that the targets file is loaded
include("${CMAKE_CURRENT_LIST_DIR}/RSFSCLogTargets.cmake")

# Define the package variables
get_target_property(RSFSCLog_INCLUDE_DIRS RSFSCLog::RSFSCLog INTERFACE_INCLUDE_DIRECTORIES)
set(RSFSCLog_LIBRARIES "RSFSCLog::RSFSCLog")

# Mark the package as found
set(RSFSCLog_FOUND TRUE)

# Export the targets for the find_package to consume
if(NOT TARGET RSFSCLog::RSFSCLog)
    add_library(RSFSCLog::RSFSCLog UNKNOWN IMPORTED)
    set_target_properties(RSFSCLog::RSFSCLog PROPERTIES
        INTERFACE_INCLUDE_DIRECTORIES "${RSFSCLog_INCLUDE_DIRS}"
        IMPORTED_LOCATION "${RSFSCLog_LIBRARIES}"
    )
endif()

check_required_components(RSFSCLog)

set(RSFSCLOG_TAG "v${RSFSCLog_VERSION}")
include_directories(SYSTEM "${RSFSCLog_INCLUDE_DIRS}")
