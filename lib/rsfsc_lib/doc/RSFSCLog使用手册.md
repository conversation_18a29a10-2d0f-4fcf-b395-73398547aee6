﻿﻿﻿﻿﻿# RSFSCLog v2.1 使用方法

**RSFSCLog -> RoboSense Factory Software Common Log**基于spdlog实现，其将spdlog源码封装成了一个.h和一个.so文件，提供trace、debug、info、warn、error 5种level的log。

RSFSCLog以单例的形式存在，所有的库（目前添加了RSFSCLog的库有MEMSTCP、RSFSCLib），以及你的程序的log日志都将统一输出到同一个地方。

## 1 调用方式

日志库从`v2.0`版本后支持了`find_package`和`fetchContent`的功能，大大方便各项目、各子模块使用日志库，具体有

+ 所有项目，所有子模块实现统一，只需要简单`#include "rsfsc_log/rsfsc_log.h"，而不需要任何宏判断头文件是否存在
+ 可自适应与公共库同步使用的日志库版本，不会造成版本冲突，当`add`过公共库后，所有子模块将自动使用公共库的`RSFSCLog`而不会再次下载
+ 日志库的更新方便快速，不再每次更新都需要修改所有项目，所有模块下的本地拷贝的`rsfsc_log.h`头文件
+ 自动下载，可简单通过一个`tag`自动下载到对应版本，所有项目共用一个下载路径。一次下载，所有可用。
+ 支持`find_package`后可获取到`target`的头文件路径，链接库路径等，使得调用变得非常方便
+ 支持设置日志库路径，当无法访问网络时候，可将日志库指向一个已下载的路径，即可直接使用`find_package`等功能
+ 支持了`header-only`的头文件`fmt`格式化库，此无需链接任何第三方库，即可使用`fmt`格式化，实现子模块与主工程格式化方式维持统一
+ 日志库的模块修改为更为规范的结构，可支持直接安装到本地通过`find_package`使用

`RSFSCLog` `v2.1`版本提供一个`rsfsc_log.cmake`文件，只需要在`CMakeLists.txt`内直接`include`，实现开箱即用

## 1.1 主工程使用 RSFSCLog v2.1

由于`rsfsc_lib`公共库在编译的阶段就已经链接了`rsfsc_log`日志库了，所以最好应保持公共库与主工程使用同一个版本的日志库

需要进行5步

1. 拷贝文件。 在工程的根目录下创建一个文件夹`cmake`，拷贝日志库中`example/rsfsc_log.cmake`文件到本工程工目录的`./cmake`文件夹中

2. 导入日志库。在`CMakeLists.txt`中导入`rsfsc_log.cmake`，添加以下语句

   ```cmake
   add_definitions(-DRSFSCLOG_USE_SPDLOG -DRSFSCLOG_USE_QT)  # 为了让RSFSCLog 使用spdlog输出
   add_subdirectory(lib/rsfsc_lib)				 # 要先add公共库，这样导入`rsfsc_log.cmake`时候会标记好所有模块使用公共库的RSFSCLog
   include(cmake/rsfsc_log.cmake)	             # include 导入日志库
   ```

3. `git`避免上传。当无法找到日志库时候，`.cmake`模块会自动下载到根目录的`_deps`文件夹，因此需要修改`.gitignore`添加文件夹避免上传

   ```
   _deps
   ```

4. 统一`#include `。删除所有关于日志库的`#if __has_include(`宏判断。所有`include`修改为单一的一句

   ```
   #include "rsfsc_log/rsfsc_log.h"
   ```

5. 通过`CI`。由于导入日志库可能会通过绝对路径下载日志库，这在`CI`中会出现`clone`失败的问题，因此需要添加替换路径为`ci-token`来得到`clone`权限。打开`.gitlab-ci.yml`，将`variables`和`before_script`修改为

   ```yaml
   variables:
     GIT_SUBMODULE_STRATEGY: none
     GIT_STRATEGY: clone
     # GITLAB_USER_EMAIL: <EMAIL>
     # GITLAB_USER_NAME: HaoQChen
     # git clone path should be in ci builds dir and runner should be at least v11.10
     GIT_CLONE_PATH: $CI_BUILDS_DIR/$CI_PROJECT_NAME
     # CI_BUILDS_DIR: /home/<USER>/ this should be set in config.toml
     # CI_PROJECT_DIR: /opt
     GIT_DEPTH: 1
   
   before_script:
     - git config --global url."http://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.robosense.cn/".insteadOf "***********************:"
     - git config --global url."http://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.robosense.cn/".insteadOf "http://gitlab.robosense.cn/" --add
     - git config --global --add safe.directory '*'
     - git submodule sync --recursive  # 同步子模块信息
     - git submodule update --init --recursive  # 初始化并更新子模块
   ```

   注意：其中的`GIT_SUBMODULE_STRATEGY`已修改为`none`

## 1.2 子模块使用 RSFSCLog v2.1

子模块在`v2.1`更新之后也可使用日志库绑定的`fmt`进行格式化了，使用的方法大致与主工程相似，主要区别是在第二步和第四步

需要完成以下步骤: 

1. 拷贝文件。参考主工程

2. 导入日志库。在`CMakeLists.txt`中导入`rsfsc_log.cmake`，添加以下语句即可

   ```cmake
   include(cmake/rsfsc_log.cmake)	   
   ```

3. `git`避免上传。参考主工程

4. 统一`#include `，删除文件。由于子模块拷贝了一个`rsfsc_log.h`文件，现在不再使用了，

   - [ ] 需要删除掉所有本地拷贝的`rsfsc_log.h`文件
   - [ ] 删除所有关于日志库的`#if __has_include(`宏判断
   - [ ] 修改为统一的`include`方法`#include "rsfsc_log/rsfsc_log.h"`

5. 通过`CI`。参考主工程

### 1.3 其他设置

#### 1.3.1 修改统一的下载路径

```cmake
# 在include .cmake之前设置下载路径，假设修改为${CMAKE_SOURCE_DIR}/dep
set(FETCH_BASE_DIR_SET "${CMAKE_SOURCE_DIR}/dep")
include(cmake/rsfsc_log.cmake)
```

#### 1.3.2 修改日志库版本

如果当前没有`add`公共库，可指定日志库版本。如果已经`add`公共库，指定版本无效，所有模块都会使用与公共库一直的版本

```cmake
set(RSFSCLOG_TAG v2.0.9)
include(cmake/rsfsc_log.cmake)
```

#### 1.3.3 指定下载好的日志库

可指定已经下载好的库来使用，而无需联网下载

```cmake
# 假设已经下载好在根目录rsfsc_log文件夹内时候
set(RSFSCLOG_SOURCE_DIR "rsfsc_log")
include(cmake/rsfsc_log.cmake)
```

此时会优先使用该指定的文件夹，但如果已经`add`了公共库，则该`dir`将失效



**这种模式下有个特别的好处，就是可以直接通过MES设置界面设置终端、文件、Qt界面的输出level**

## 2 保存路径

**由于C++无法保证某段程序必然运行在整个程序的最前面**：main函数前面有全局变量的构造函数，而每个文件的全局构造函数的执行顺序是无法保证的。所以单例情况下，也就无法保证众多使用了这个单例的库哪个先执行，可能是MEMSTCP、可能是RSFSCLib，也可能是你的程序，所以无法给RSFSCLog传一个初始化的文件保存路径。

默认保存路径：`~/.RoboSense/normal_log`

当你的程序启动起来之后，可以通过`bool changeFileSavePath(const std::string& _file_path);`、`bool resetFileSavePath();`函数去修改保存的路径，但是需要注意的是，由于多线程情况下实现切换文件句柄，为了保证安全，在切换后约100ms内，所有的log都会被丢弃。

**注：**

+ 如果你是从RSFSCLib中调用的，对于单台雷达，应该在checkState之后将这个路径修改为data路径；对于多台雷达，应该在checkState一台雷达之后将这个路径修改为widget_log_setting_->getLogPath();的路径

## 3 log level

一共提供了trace、debug、info、warn、error 5种level的log。我们提倡：

+ **trace：** 只做代码级别的调试用，也就是说开发者以外的人不应该看到这个log。一般情况下，代码调试完毕，也就保证了代码肯定不会出错，我们建议您直接删除相关语句
+ **debug：** 提供给高级用户，在出现BUG时进行debug用的信息。对普通用户，也不应该显示这些消息
+ **info：** 普通用户接触到的第一个level的log信息，用于记录关键流程和关键结果，方便出现异常时进行初步排查定位
+ **warn：** 警告用户，如果不注意可能会出现危险、影响软件正常功能的信息
+ **error：** 警告用户，已经出现了严重的，影响到软件正常功能，或者软件结果出现意想不到的异常信息

**一切的终端输出、界面输出、文件输出，对计算机而言都是非常低效的。所以应该怜惜每一句log，非必要不log**

可以通过调用下面的函数来调整输出的最小level：

```cpp
void setFileLogLevel(const LogLevel);
void setTerminalLogLevel(const LogLevel);
void setQMessageBrowserLogLevel(const LogLevel);
```

## 4 使用方法

### 4.1 log用法

spdlog是模板类，而且都是可变参数长度的函数，他可以支持任意个数的参数。

对应的用法有：

```cpp
  RSFSCLog::getInstance()->info(u8"示例如下: ");
  RSFSCLog::getInstance()->debug("Easy padding in numbers like {:08d}", 12);
  // output: Easy padding in numbers like 00000012
  RSFSCLog::getInstance()->info("Support for int: {0:d};  hex: {0:#x};  oct: {0:o}; bin: {0:b}", 42);
  // output: Support for int: 42;  hex: 0x2a;  oct: 052; bin: 101010
  RSFSCLog::getInstance()->warn("Support for floats {:0.3f}", 11.23456);
  // output: Support for floats 11.235
  RSFSCLog::getInstance()->error("Support for floats {}", 1.23456);
  // output: Support for floats 1.23456
  RSFSCLog::getInstance()->info("Positional args are {1} {0}..", "too", "supported");
  // output: Positional args are supported too..

  // Replacing %+f, %-f, and % f and specifying a sign:
  RSFSCLog::getInstance()->info("{:+f}; {:+f}", 3.14, -3.14);  // show it always
  // output: "+3.140000; -3.140000"
  RSFSCLog::getInstance()->info("{: f}; {: f}", 3.14, -3.14);  // show a space for positive numbers
  // output: " 3.140000; -3.140000"
  RSFSCLog::getInstance()->info("{:-f}; {:-f}", 3.14, -3.14);  // show only the minus -- same as '{:f}; {:f}'
                                                               // output: "3.140000; -3.140000"

  std::vector<uint32_t> vec_uint         = { 1, 10, 100, 1000, 0xffffffff };
  std::vector<uint8_t> vec_uint8         = { 1, 10, 0x64, 200, 0xff };
  std::map<int, std::string> map_int_str = { { 3, "three" }, { 2, "two" }, { 1, "one" } };
  RSFSCLog::getInstance()->info("vec_int: {:#x}", fmt::join(vec_uint, ","));
  // output: vec_int: 0x1,0xa,0x64,0x3e8,0xffffffff
  RSFSCLog::getInstance()->info("vec_uint8: {:02x}", fmt::join(vec_uint8.begin(), vec_uint8.begin() + 4, " "));

  std::vector<char> vec_char = { 1, 5, 23, static_cast<char>(0xfe) };
  // output: vec_uint8: 01 0a 64 c8
  RSFSCLog::getInstance()->info("{}", vec_uint);
  // output: [1, 10, 100, 1000, 4294967295]
  RSFSCLog::getInstance()->info("{}", map_int_str);
  // output: [(1, one), (2, two), (3, three)]
  RSFSCLog::getInstance()->info("{}",
                                std::tuple<int, float, char, std::array<uint8_t, 2>>(1, 2.5, 'A', { 0xbb, 0xff }));
  // output: (1, 2.5, A, [187, 255])

  RSFSCLog::getInstance()->info("{:<30}", std::string("left aligned"));
  // output: left aligned
  RSFSCLog::getInstance()->info("{:^30}", std::string("centered"));
  // output:          centered
  RSFSCLog::getInstance()->info("{:>30}", std::string("right aligned"));
  // output:                 right aligned
  RSFSCLog::getInstance()->info("\n┌{0:─^{2}}┐\n"
                                "│{1: ^{2}}│\n"
                                "└{0:─^{2}}┘\n",
                                "", "Hello, world!", 20);
  // output:
  // ┌────────────────────┐
  // │   Hello, world!    │
  // └────────────────────┘
```

更具体的格式化文档可到网上查阅`fmt`格式化相关资料

或者参考 [fmt格式化](doc/fmt字符串格式化语法.md)文档

### 4.2 Qt控件用法

提供了一个配套的`MessageBrowser`的控件，只需要像普通Qt控件一样，在CMakeLists.txt里解析头文件、添加cpp即可使用：

```cpp
// NOTE must new first cause it define many show event
browser_message_ = new MessageBrowser(QString::fromUtf8(PROJECT_NAME), this);  
RSFSCLog::getInstance()->setQtLogWidget(browser_message_, "slotShowMessage");
```

然后就可以将他放到你放到的任何位置去了，需要注意的是这个需要在程序比较早的地方去构造，不然很多输出信息就看不到了。

注意，要在析构`MessageBrowser`之前，先解除绑定

```cpp
RSFSCLog::getInstance()->setQtLogWidget(Q_NULLPTR);  // delete sink
delete browser_message_;
```

