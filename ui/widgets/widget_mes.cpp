﻿/******************************************************************************
* Copyright 2020 RoboSense All rights reserved.
* Suteng Innovation Technology Co., Ltd. www.robosense.ai

* This software is provided to you directly by RoboSense and might
* only be used to access RoboSense LiDAR. Any compilation,
* modification, exploration, reproduction and redistribution are
* restricted without RoboSense's prior consent.

* THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
* WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
* DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.
*****************************************************************************/
#include "widget_mes.h"
#include "app_event.h"
#include "config.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QCloseEvent>

// NOLINTNEXTLINE
using namespace robosense::lidar;

WidgetMes::WidgetMes(QWidget* _parent) :
  WidgetLogSetting(_parent), limit_csv_utils_ptr_(app()->getCsvUtils("airy_limit"))
{
  addCheckSumDir(std::string(INSTALL_PREFIX_SHARE) + "/config");
  if (limit_csv_utils_ptr_ == nullptr)
  {
    LOG_ERROR("airy_limit csv解析器失败");
    return;
  }
}

WidgetMes::~WidgetMes() = default;

void WidgetMes::closeEvent(QCloseEvent* _event)
{
  hide();
  _event->ignore();
}

bool WidgetMes::checkMesIsOK(int _index, QString& _data_path, QString& _result_path, QString& _temp_path)
{
  bool rtn = false;

  switch (checkAllState(_index, _data_path, _temp_path, _result_path))
  {
  case robosense::lidar::rsfsc_lib::CHECK_STATE_ITEM_EMPTY:
  {
    robosense::lidar::RSFSCLog::getInstance()->error("MES参数设置不完整，退出检测流程");
    break;
  }
  case robosense::lidar::rsfsc_lib::CHECK_STATE_PROCEDURE_ERROR:
  {
    robosense::lidar::RSFSCLog::getInstance()->error("MES返回产品状态错误，退出检测流程");
    break;
  }
  case robosense::lidar::rsfsc_lib::CHECK_STATE_PATH_NOT_SATISFY:
  {
    robosense::lidar::RSFSCLog::getInstance()->error("Log配置路径失败，退出检测流程");
    break;
  }
  case robosense::lidar::rsfsc_lib::CHECK_STATE_SUCCESS:
  {
    rtn = true;
    robosense::lidar::RSFSCLog::getInstance()->info("检查MES状态成功");
    break;
  }
  default: rtn = false; break;
  }

  return rtn;
}

void WidgetMes::finishProcess(const int _index,
                              const LogTestStatus& _log_status,
                              const QString& _fail_label,
                              const QString& _fail_msg)
{
  setTestStatus(_index, _log_status, _fail_label, _fail_msg);
  QString write_err;
  if (WidgetLogSetting::finishProcess(_index, write_err))
  {
    if (!write_err.isEmpty())
    {
      robosense::lidar::RSFSCLog::getInstance()->error(write_err.toStdString());
    }
  }
}
void WidgetMes::showWidget()
{
  if (isVisible())
  {
    raise();
    activateWindow();
  }
  else
  {
    show();
  }
}

bool WidgetMes::addMeasureMessage(const int _lidar_index,
                                  const QString& _name,
                                  const double _data,
                                  const robosense::lidar::rsfsc_lib::MeasureDataType _data_type)
{
  auto limit_info = limit_csv_utils_ptr_->getLimitInfo(_name.toStdString());

  // limit_info.setNameSuffix(fmt::format("unkown_{}", _name));
  if (!limit_info.is_ok)
  {
    LOG_ERROR("未找到该limit信息 name: {}", _name);
    // rsfsc_lib::WidgetLogSetting::addMeasureMessage(_lidar_index, limit_info, _data, _data_type);
    return false;
  }
  return rsfsc_lib::WidgetLogSetting::addMeasureMessage(_lidar_index, limit_info, _data, _data_type);
}
bool WidgetMes::addMeasureMessage(const int _lidar_index, const QString& _name, const std::string& _data)
{
  auto limit_info = limit_csv_utils_ptr_->getLimitInfo(_name.toStdString());

  if (!limit_info.is_ok)
  {
    LOG_ERROR("未找到该limit信息 name: {}", _name);
    return false;
  }
  return rsfsc_lib::WidgetLogSetting::addMeasureMessage(_lidar_index, limit_info, _data);
}
bool WidgetMes::addMeasureMessage(const int _lidar_index,
                                  const robosense::lidar::LimitInfo& _limit_info,
                                  const QVariant& _value)
{
  if (!_limit_info.is_ok)
  {
    LOG_ERROR("limit信息错误 key: {}", _limit_info.getName());
    return false;
  }
  if (!_value.isValid())
  {
    LOG_ERROR("QVariant value key 无效: {}", _limit_info.getName());
    rsfsc_lib::WidgetLogSetting::addMeasureMessage(_lidar_index, _limit_info, NAN);
    return false;
  }

  switch (static_cast<QMetaType::Type>(_value.type()))
  {
  case QMetaType::Int:
  {
    return rsfsc_lib::WidgetLogSetting::addMeasureMessage(_lidar_index, _limit_info, _value.toInt(),
                                                          rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    break;
  }
  case QMetaType::UInt:
  {
    return rsfsc_lib::WidgetLogSetting::addMeasureMessage(_lidar_index, _limit_info, _value.toUInt(),
                                                          rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_HEX);
    break;
  }
  case QMetaType::Double:
  case QMetaType::Float:
  {
    return rsfsc_lib::WidgetLogSetting::addMeasureMessage(_lidar_index, _limit_info, _value.toDouble(),
                                                          rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_FLOAT);
    break;
  }
  case QMetaType::QString:
  {
    return rsfsc_lib::WidgetLogSetting::addMeasureMessage(_lidar_index, _limit_info, _value.toString().toStdString());
    break;
  }
  default:
  {
    LOG_ERROR("未知的数据类型name: {}, type name: {},", _limit_info.getName(), _value.typeName());
    return false;
  }
  break;
  }

  return true;
}

bool WidgetMes::checkWithLimit(const robosense::lidar::LimitInfo& _limit_info, const QVariant& _value)
{
  if (!_limit_info.is_ok)
  {
    LOG_ERROR("limit信息错误 key: {}", _limit_info.getName());
    return false;
  }
  if (!_value.isValid())
  {
    LOG_ERROR("QVariant value key 无效: {}", _limit_info.getName());
    return false;
  }

  switch (static_cast<QMetaType::Type>(_value.type()))
  {
  case QMetaType::Int:
  {
    return rsfsc_lib::WidgetLogSetting::checkWithinLimit(_limit_info, _value.toInt(),
                                                         rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    break;
  }
  case QMetaType::UInt:
  {
    return rsfsc_lib::WidgetLogSetting::checkWithinLimit(_limit_info, _value.toUInt(),
                                                         rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_HEX);
    break;
  }
  case QMetaType::Double:
  case QMetaType::Float:
  {
    return rsfsc_lib::WidgetLogSetting::checkWithinLimit(_limit_info, _value.toDouble(),
                                                         rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_FLOAT);
    break;
  }
  case QMetaType::QString:
  {
    std::string data = _value.toString().toStdString();
    if (data.empty())
    {
      return false;
    }

    if (data == "nan")
    {
      return false;
    }

    if (_limit_info.limit_text == "nan")
    {
      return true;
    }

    return _limit_info.limit_text == data;
    break;
  }
  default:
  {
    LOG_ERROR("未知的数据类型name: {}, type name: {},", _limit_info.getName(), _value.typeName());
    return false;
  }
  break;
  }
  return true;
}
QString WidgetMes::dataToString(const QVariant& _value) { return dataToString(_value, _value.type()); }
QString WidgetMes::dataToString(const QVariant& _value, const QVariant::Type& _type)
{
  bool is_ok = false;
  _value.toDouble(&is_ok);
  if (is_ok && std::isnan(_value.toDouble()))
  {
    return "nan";
  }
  switch (static_cast<QMetaType::Type>(_type))
  {
  case QMetaType::Int:
  {
    return _value.toString();
    break;
  }
  case QMetaType::UInt:
  {
    return QString::fromStdString(fmt::format("{:#x}", _value.toUInt()));
    break;
  }
  case QMetaType::Double:
  case QMetaType::Float:
  {
    return QString::number(_value.toDouble(), 'f', 3);
    break;
  }
  case QMetaType::QString:
  {
    return _value.toString();
    break;
  }
  default:
  {
    return _value.toString();
  }
  break;
  }

  return "nan";
}
