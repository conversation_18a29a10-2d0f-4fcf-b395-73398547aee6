﻿MES信息设置界面操作手册
=================

[ToC]

这个界面主要用于设置跟MES查询、上传有关的信息，一般是本工站相关的信息。界面效果如下：

![](img/ui_1_4_0.png)

## CHANGELOG

| 版本号 | 修改内容                            | 修改时间  | 备注 |
| :----: | :---------------------------------- | :-------: | :--- |
| v1.4.0 | 修改账号管理机制说明                    | 20230323  |      |
| v1.3.6 | 增加相关更新说明                    | 20220928  |      |
| v1.3.5 | 增加相关更新说明                    | 20220928  |      |
| v1.3.1 | 增加异常说明                        | 20220819  |      |
| v1.3.0 | 版本升级                            | 202208016 |      |
| v1.2.0 | 增加高级功能及修改data location说明 | 20220407  |      |
| v1.0.2 | 增加高级功能设置                    | 20220208  |      |
| v1.0.1 | 增加MES设置                         | 20211021  |      |
| v1.0.0 | 初版                                | 20211021  |      |

## 1 基本设置

**注意：**
+ 以下填写信息不要出现中文、空格、除下划线以外的任何特殊字符，仅支持0-9、a-z、A-Z以及下划线
+ Process跟station是一样的意思，只是不同人的叫法不一样


**设置：**

+ **当前登陆者** 详见第2章，退出时会自动将数据保存到电脑，下次重启自动加载
+ **Data Location（保存路径）** 所有数据的保存路径，软件自动逐层创建以下文件夹：`数据路径文件夹->项目编号文件夹、log两个文件夹->mes、rsdata、summary三个文件夹`。
+ **数据收集路径** 仅在Jabil的MES下生效，测试结束时，会将测试结果csv拷贝一份到`数据收集路径->项目编号文件夹->年月文件夹`下，方便Jabil收集测试信息
+ **MES Type（MES类型）** 目前仅支持RoboSense和Jabil的MES，如果这两种都没有，就随便选一个吧
+ **Procedure Check（MES过站）** 是否连MES进行过站查询
+ **数据请求** 从MES中请求数据（目前仅RoboSense MES支持）
+ **Upload Data（MES上传）** 是否连MES上传数据
+ **不校验SN项目**：默认会以最严格的标准去要求SN编号，如果不符合要求不能进行相应操作，防止A项目被当成B项目生产。如果遇到部分项目SN规则发生了变更，不满足要求，可以设置不对特定项目进行校验，例如：`0216;0210`
+ **Customer（客户）** 给代工厂做标识，填RoboSense即可
+ **Board Style（板式）** 目前暂未用到，填no_log即可
+ **Tester Name（电脑名）** 在第一次启动软件时，会自动读取本地电脑的HOST名称。后续如更改了HOST名称，需要手动修改这里
+ **Test Process（工站名）** 本工站的名称，听从产线安排，需要与MES上的名称保持一致
+ **Assembly Number（组件编号）** 未使用，写no_log即可
+ **Assembly Revision（组件版本）** 未使用，写no_log即可
+ **Lot（产品批次）** 产品批次，听从产线安排
+ **Fixture（治具编号）** 治具编号，一般填写主要治具的编号或者版本号即可。如果有多个治具，建议统一到一个工程，统一编码进行管控
+ **Line（线体名称）** 拉线名称
+ **Site（厂区编号）** 厂区编号，由RoboSense统一规划，目前石岩为7001，Jabil 1期为7011，HHL 1期为7021
+ **Operator ID（操作员ID）** 操作员的工号，需要扫码输入。登录管理员后可键盘输入

## 2 账号管理

![](img/user_manager.png)

目前等级从高到低依次分为**开发者**、**超级管理员**、**技术员**、**操作员**四种权限，高级别权限可以添加、删除低级别权限的用户（开发者、超级管理员为单一账户，不可增减），每个人可以修改自己账户的密码。目前每台电脑的账户单独管理，互不联通（后续版本会增加功能让多台电脑联通）。**当前机制为仅超级管理员以上级别账户拥有MES相关设置修改权限**。

+ **开发者**权限最高，且密码每天一变。原则上开发者不能对其他任何人透露密码规则。对于一些版本、硬件强相关信息，会严重影响到软件效果的参数，需要开发者才能修改。例如雷达通道数、温补温度范围、评估项目、保存路径等
+ **超级管理员**属于一线最高权限者，密码每天一变，且登录10分钟后自动退出，确保不会由于账户忘退出被无授权人员进行非法操作。一些跟产线强相关，且一般只需要修改一次，对软件效果产生关键作用的参数，需要超级管理员以上才能修改。软件安装、调试、修改参数需要超级管理员密码时需由工艺组人员向工艺组负责人请求授权，超级管理员可变更参数例如相机类型、MES类型、导轨长度等。
+ **技术员**账户登录10分钟后自动退出。主要负责修改一些需要频繁变动，且对软件效果不会产生太大影响的参数。例如单步运行一些硬件单步运行操作、硬件调试等
+ **操作员**目前暂无权限，可以勾选**管理员认证**功能，则操作员也需要登录才能操作软件

**注意：** 
+ 登录账号进行任何参数修改都会被记录下来用于追溯，请不要随意将个人账号密码透露出去，否则后果自负。
+ 登录开发者账号时，需要保证电脑日期是准确的

## 3 高级设置

在界面菜单栏（部分系统菜单栏在界面上，另一部分可能在显示器的左上角），登录密码后，可以进入高级设置界面，里面会有一些选项：

![](img/advance_setting.png)

1. **检查软件多开**：检查同一个软件是否启动了多次，避免一些不必要的麻烦
2. **SN连接项目编号过站**：不勾选的话，软件的过站检测、结果上传都只上传`SN`；如果勾选了，则会`SN-项目编号(车型、安装位置)`进行过站。目前Jabil和RS的MES都支持这个功能
3. **使用统一SN**：不勾选的话，需要SN编码以`SN-项目编号`的形式扫码，勾选后，使用RoboSense内部统一SN进行扫码，软件会与RoboSense的MES进行通信，获取具体的项目编号、车型、雷达安装位置等信息
4. **使用线束管理**：如果线束使用次数超过阈值要求，则不能开始测试（需要主软件支持）
5. **操作员认证**：勾选后，操作员也必须登录才能进行测试
6. **线束使用寿命默认值**：这是次数默认值，具体到每个工位每个线束，还可以根据线束的实际情况再设置一个增量
7. **log等级**：正常情况下，设置info即可，如果出了BUG或者调试软件，可以设置成debug或者trace。设置debug或者trace可能会导致软件变慢或者输出log文件变大，但是能获得更详细的信息
8. **最小空间要求**：数据保存硬盘及系统盘的最小限制，开始测试时，如果硬盘空间小于这个值则会报错
9. **重置配置文件校验值**：软件过站时会检查配置文件的校验值，如当前文件与上一次重置的校验值不符则会报错，故每次修改配置文件后需要点击改按钮进行校验值重置
10. **重置产量计数**：在界面最下面的状态栏，会显示总产量、总良率、当天产量、当天良率等数据，这个产量和良率是按照次数来的，如果同一台雷达测试多次也会被累计。仅做简单统计用，如需要其他方式的良率，请利用RoboSense提供的`统计小工具`进行统计
   ![](img/stats_tool.png)
11. **配置文件校验**：过站前需将配置文件所在文件夹路径通过`addCheckSumPath`函数进行添加(支持多个路径)，软件安装后首次运行或配置文件变更后需使用管理员以上权限在`菜单栏->高级设置`中单击`重置配置文件校验值`按钮进行配置文件校验值重置操作，配置文件校验若不通过无法进行过站操作


## 4 网络设置

**RoboSense MES设置**
![](img/network_setting_robosense.png)

**Jabil MES设置**

![](img/network_setting_jabil.png)

+ **MES超时等待**：对所有网络参数适用，包括IP池延时、MES通讯延时等，默认1秒，对于部分网络延迟比较大的产线，可以适当设置大一些，但设置过大无益
+ **使用IP池**：当IT布置了IP池服务器后，勾选该选项，并填写IP池地址和端口号，启用后无需使用继电器修改雷达IP和端口号，目前修改IP端口号并重启预计会浪费1分钟每次。
+ **使用自动化设备**：仅试用与自动化产线，测试软件需要与自动化设备通信时勾选，并按要求设置好相关IP及端口号。此设置需要重启后才能生效
+ **剩下的MES地址和账号设置**：注意，新版本的软件（MES设置界面v1.3.0以后）已经支持界面设置，不再需要设置复杂的配置文件，在菜单栏的网络设置即可进行对应参数设置。具体参数值，每个产线不一样，需要咨询对应IT的同事

如果你使用的是比较旧版的软件，需要按照下面的内容设置配置文件，或者更新软件到最新版：

### 4.1 一般性设置说明 

软件会有一个默认的值，如果MES的IP或者账号密码等有更新，需要到`~/.config/RoboSense/软件名/`目录下创建或修改配置文件。如下图所示，如果这个目录下有一个`log_setting.conf`则对应创建一个`log_setting_xxx_mes.conf`的文件

**注意：**  
+ 这里需要注意的是log_sett**i**ng才是正确的写法，但前期因为手误等，有人写成了log_sett**o**ng。所以文件夹下有啥，新创建的文件就写啥
+ 如果在home目录没看到`.config`目录，则`Ctrl+H`显示隐藏文件即可查看

![](img/mes_setting.png)

### 4.2 Jabil的MES（Ubuntu）

创建一个名为`log_setting_jabil_mes.conf`的文件，在里面写上以下内容：

```
jabil_ftp_host=**************
jabil_ftp_user_name=whoareyou
jabil_ftp_password=test
jabil_procedure_check_host=http://huajafv01/biz/tis/tis.asp
```

**具体信息请与IT核对，涉及信息安全，这里写的并不正确**

**重启软件**并正确设置基本信息即可完成MES的过站及上传功能。

需要注意的是，Jabil这边IP地址经常修改，最好能直接写HOST地址，然后由DNS实现HOST到IP的解析。具体参考之前出现问题的修改方法：[Jabil的MES修改IP方法](Jabil的MES修改IP方法.md)

### 4.3 RoboSense的MES（Ubuntu）

创建一个名为`log_setting_robosense_mes.conf`的文件，在里面写上以下内容：

```
robosense_token_url=http://***********:9999/sys/externalLogin
robosense_data_url=http://***********:17001/master/test/list
robosense_upload_url=http://***********:17001/master/test/add
robosense_token_username=whoareyou
robosense_token_password=test
```

**具体信息请与IT核对，涉及信息安全，这里写的并不正确**

**重启软件**并正确设置基本信息即可完成MES的过站及上传功能。

### 4.4 Windows下的修改

目前仅少数软件在Windows下，Windows下的修改与Ubuntu类似，唯一的不同在于，Windows的配置保存在注册表中，而不是`.conf`文件

1. 在任务栏上的输入框，键入`regedit`即可打开注册表，注意要登录管理员账号。
2. 在`计算机\HKEY_CURRENT_USER\SOFTWARE\RoboSense\软件名/log_setting_jabil_mes`下面（可以搜索关键字RoboSense，并按F3查找下一个，RoboSense的MES注意修改不同的名字）：
右键->新建->字符串值
3. 添加名称和值，如下图所示
   ![](img/regedit__mes_host.png)

### 4.5 注意事项

+ 多层`log_setting`目录，`log_settong`错别字等，都是历史原因，在1.3.0版本之后就已经彻底修复了，如果不想麻烦，直接更新最新版本即可
+ RoboSense的设置，1.3.0之前只有3个URL设置，1.3.0及之后增加了一个设置，并且修改了`robosense_data_url`设置的含义，映射关系如下：

|       1.3.0之前        |        1.3.0之后        |
| :--------------------: | :---------------------: |
|  robosense_token_url   |   robosense_token_url   |
| **robosense_data_url** | **robosense_check_url** |
|  robosense_upload_url  |  robosense_upload_url   |
|                        |   robosense_data_url    |

如果只是从低版本升级到高版本，软件会自动做好适配；但是此时再从高版本降级回去低版本，就需要自己手动设置了。

## 5 工具

![](img/tool_setting.png)

+ **串口绑定**：一个脚本，用于解决Ubuntu下`ttyUSB*`每次重启电脑都会变更的问题。设置后，重启后也不会变动，具体使用请参考脚本提示。此脚本需要主软件支持，可以要求主软件负责人更新软件
