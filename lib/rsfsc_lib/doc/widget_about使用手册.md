﻿﻿# WidgetAbout使用手册

`WidgetAbout`主要显示了软件信息、版权声明、第三方类库版权，这三方面的内容

要求所有产线软件必须使用此控件格式进行版权声明

## 1 使用方式

跟一个普通的Qt控件一样，只需要new一个即可使用：

1. 根据用到的类库，在CMakeLists里定宏，用了哪些类库就定义几个。示例：`add_compile_definitions(ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_BOOST=1`  

|库|宏|备注|  
|:---:|:---|:---|  
|Boost|ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_BOOST||  
|OpenCV|ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_OPENCV||  
|PCL|ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_PCL||  
|VTK|ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_VTK||  
|Eigen|ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_EIGEN||  
|yaml-cpp|ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_YAMLCPP||  
|Python|ROBOSENSE_LIDAR_WIDGET_ABOUT_SHOW_PYTHON|需要`target_include_directories(your_project PRIVATE /usr/include/python3.8)`，否则找不到头文件|
   
2. 在CMakeLists里：`qt5_wrap_cpp(MOC_HEAD_FILES rsfsc_lib/ui/widget_about.h)`，然后`add_executable(xxx ${MOC_HEAD_FILES} rsfsc_lib/ui/widget_about.cpp)`
3. 在MainWindow的构造中，构造并设置自己软件的信息：  
   ```cpp
   QMenu* menu_help      = new QMenu(QString::fromUtf8("帮助"), this);
   QAction* action_about = menu_help->addAction(QString::fromUtf8("关于"));
   widget_about_         = new WidgetAbout(this);
   widget_about_->setName(QString::fromUtf8(你的软件名));
   widget_about_->setBrief(QString::fromUtf8(你的软件功能简介));
   widget_about_->setVersionStr(QString("v") + QString::fromUtf8(WIDGET_LOG_SETTING_VERSION_STR));
   widget_about_->setBuildTime(QString::fromUtf8(PROJECT_COMPILE_TIME));
   widget_about_->setBuildCommit(QString::fromUtf8(PROJECT_COMPILE_COMMIT));
   widget_about_->setYearStartCopyright(第一次发布软件的年份);
   widget_about_->setContactEmail(你的公司邮箱);
   this->menuBar()->addMenu(menu_help);
   QObject::connect(action_about, SIGNAL(triggered()), SLOT(slotShowAbout()));
   ```
   
4. 在回调函数中：
   ```cpp
    void MainWindow::slotShowAbout()
    {
     if (widget_about_->isVisible())
     {
       widget_about_->raise();
       widget_about_->activateWindow();
     }
     else
     {
       widget_about_->show();
     }
   }
   ```
   
5. 在closeEvent中，保证在界面弹出的情况下关闭主界面也能正常关闭
   ```cpp
   void MainWindow::closeEvent(QCloseEvent* event)
   {
     QMessageBox::StandardButton rb = QMessageBox::warning(this, "警告", "<font color='red'>确定退出?</font>",
                                                           QMessageBox::Yes | QMessageBox::No, QMessageBox::No);
     if (rb == QMessageBox::No)
     {
       event->ignore();
       return;
     }

     if (widget_log_setting_->isVisible())
     {
       widget_log_setting_->close();
     }

     if (widget_about_->isVisible())
     {
       widget_about_->close();
     }

     QMainWindow::closeEvent(event);
   }
   ```

完成以上3步后，点击菜单栏的`帮助`->`关于`中就会弹出像下面一样的界面:

![](img/about1.png)

![](img/about2.png)

![](img/about3.png)

**注意：**

+ 某些开源协议要求每份分发都需要拷贝一份协议文本，这些协议文本已经放到了rsfsc_lib对应的动态库文件夹，安装时记得把整个文件夹一起安装过去