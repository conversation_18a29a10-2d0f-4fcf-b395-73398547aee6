﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef APP_CONTROLLER_H
#define APP_CONTROLLER_H

#include "../ui/mainwindow.h"
#include "lidar_communication_box/src/lidar_communication_box.h"
#include "rsfsc_fsm/finite_state_machine.h"
#include "work_model/connectivity_work_model.h"
#include <QObject>
// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

class AppController : public QObject
{
  Q_OBJECT
public:
  friend class MainWindow;
  explicit AppController(MainWindow* _main_window, QObject* _parent = nullptr);
  AppController(const AppController&) = delete;
  AppController(AppController&&)      = delete;
  AppController& operator=(const AppController&) = delete;
  AppController& operator=(AppController&&) = delete;
  ~AppController() override;

  void initFsm();
  void initSignalSlot();
  bool checkAndInitBox();

  bool checkBeforeRun();

  void updatePara();

public Q_SLOTS:
  void slotOneKeyRunClicked();
  // void slotMainwindowInitFinished();

  void slotButtonGetCurrentClicked();
  void slotButtonGetSyncClicked();
  void slotButtonGetFirmwareClicked();
  void slotCheckboxSwitchPowerStateChanged();

  void slotButtonTestClicked();

private:
  MainWindow* main_window_;
  std::map<int, std::shared_ptr<FiniteStateMachine<ConnectivityWorkModel>>> fsm_map_;
  std::map<int, std::shared_ptr<ConnectivityWorkModel>> work_model_map_;
  std::shared_ptr<LidarCommunicationBox> lidar_communication_box_;
};

}  // namespace lidar
}  // namespace robosense
#endif  // APP_CONTROLLER_H