﻿set(OUTPUT_PREFIX "${CMAKE_CURRENT_SOURCE_DIR}/release/ubuntu_2004")

install(
  TARGETS ${PROJECT_NAME}
  PERMISSIONS
    OWNER_EXECUTE
    OWNER_WRITE
    OWNER_READ
    GROUP_EXECUTE
    GROUP_READ
    WORLD_EXECUTE
    WORLD_READ
  ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}
  LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}
  RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/${PROJECT_NAME})

install(
  DIRECTORY resource config
  DESTINATION ${CMAKE_INSTALL_PREFIX}/share/${PROJECT_NAME}
  USE_SOURCE_PERMISSIONS
  DIRECTORY_PERMISSIONS
    OWNER_EXECUTE
    OWNER_WRITE
    OWNER_READ
    GROUP_EXECUTE
    GROUP_READ
    WORLD_EXECUTE
    WORLD_READ)

install(FILES $<TARGET_FILE:Qt5::Concurrent> $<TARGET_FILE:Qt5::SerialPort>
        DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME})

configure_file(script/script.desktop.in ${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}.desktop @ONLY)
install(FILES ${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}.desktop DESTINATION /usr/share/applications)

configure_file(script/cpack_files/_install.sh ${CMAKE_CURRENT_BINARY_DIR}/install.sh @ONLY)

set(CPACK_GENERATOR "DEB")
set(CPACK_DEBIAN_PACKAGE_NAME ${PROJECT_NAME})
set(CPACK_DEBIAN_PACKAGE_MAINTAINER "DavidDai") # required
set(CPACK_DEBIAN_PACKAGE_ARCHITECTURE "amd64")
set(CPACK_PACKAGE_VERSION_MAJOR ${PROJECT_VERSION_MAJOR})
set(CPACK_PACKAGE_VERSION_MINOR ${PROJECT_VERSION_MINOR})
set(CPACK_PACKAGE_VERSION_PATCH ${PROJECT_VERSION_PATCH})
set(CPACK_DEBIAN_PACKAGE_DEPENDS "libqt5core5a, libqt5gui5, libqt5widgets5")

set(CPACK_PACKAGE_ICON resource/img/icon.png)
set(CPACK_OUTPUT_FILE_PREFIX ${OUTPUT_PREFIX})

set(CPACK_PACKAGE_FILE_NAME
    "${PROJECT_NAME}_v${PROJECT_VERSION}_${PROJECT_COMPILE_TIME}_${PROJECT_COMPILE_COMMIT}_${CMAKE_SYSTEM_PROCESSOR}")

set(RELEASE_DIR ${OUTPUT_PREFIX})
configure_file(${CMAKE_CURRENT_SOURCE_DIR}/cmake/post_cpack.cmake.in ${CMAKE_CURRENT_BINARY_DIR}/post_cpack.cmake @ONLY)
configure_file(${CMAKE_CURRENT_SOURCE_DIR}/cmake/pre_cpack.cmake.in ${CMAKE_CURRENT_BINARY_DIR}/pre_cpack.cmake @ONLY)

set(CPACK_PRE_BUILD_SCRIPTS "${CMAKE_CURRENT_BINARY_DIR}/pre_cpack.cmake")
set(CPACK_POST_BUILD_SCRIPTS "${CMAKE_CURRENT_BINARY_DIR}/post_cpack.cmake")

include(CPack) # must after the setting
