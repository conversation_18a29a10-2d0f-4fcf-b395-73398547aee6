﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   rsfsc_common.hpp
 * <AUTHOR> Zhang (<EMAIL>), Antoine Chen (<EMAIL>)
 * @brief
 * @version 1.0.0
 * @date 2022-11-01
 *
 **/
#ifndef RSFSCLIB_COMMON_HPP
#define RSFSCLIB_COMMON_HPP
#include <array>
#include <cmath>
#include <functional>

#include "rsfsc_log/rsfsc_log.h"
#include "rsfsc_msg.h"

#if __has_include(<QtWidgets/QWidget>)
#  include <QtCore/QEventLoop>
#  include <QtCore/QString>
#  include <QtCore/QTimer>
#  include <QtWidgets/QDoubleSpinBox>
#  include <QtWidgets/QSpinBox>
#endif  //__has_include(<QtWidgets>)

namespace robosense
{
namespace lidar
{
namespace rsfsc_lib
{
class WindowSliceRefractionTransform
{
public:
  /**
   * @brief     根据项目编号及输入原始角度，输出折射后的角度；或者输入折射角度，反解算获得原始角度。
   *
   * @param     _pc                           项目编号
   * @param     _car_type                     车型，M1P通过不同的车型来区分窗口片类型
   * @param     _cal_refraction_not_theory    输入理论角计算折射角，还是输入折射角反解算理论角，默认计算折射角
   * @param     _type_reg_value               window type register's value，代表窗口片类型的寄存器值，
   *                                          只需要将寄存器值读出来传进这个函数即可，由函数来进行判断
   *                                          对于M1的机器，可以读寄存器0x(83c400fc)：
   *                                          如果值为0x55则为玻璃窗口片，如果是其它比如0xAA、0x00都是塑料窗口片
   *                                          对于M1P的机器，可以读取0x(83c60700)，低8bit代表窗口片类型：
   *                                          0x00表示1.8mmPC曲面；0x01表示1.8mm PC平面；
   *                                          0x02表示2mm PC曲面；0x03表示2mm PC平面
   *                                          0x04表示2mm玻璃曲面；0x05表示2mm玻璃平面
   *                                          0x06表示5mm玻璃140.68曲面；0x07表示5mm玻璃137.68曲面；0x08表示5mm玻璃924.4平面?
   * @param     _cal_pc                       塑料窗口片情况下，是否计算折射因子，拼接时需要设置成false
   */
  explicit WindowSliceRefractionTransform(ProjectCode _pc,
                                          const std::string& _car_type    = std::string(""),
                                          bool _cal_refraction_not_theory = true,
                                          uint32_t _type_reg_value        = 0x0,
                                          bool _cal_pc                    = true)
  {
    RSFSCLog::getInstance()->info("WindowSliceRefractionTransform construct with pc: " +
                                  memsProjectCode2QString(_pc).toStdString() + ", car type: " + _car_type +
                                  ", cal refraction?: " + std::to_string(static_cast<int>(_cal_refraction_not_theory)) +
                                  ", type reg value: " + std::to_string(_type_reg_value) +
                                  ", cal pc?: " + std::to_string(static_cast<int>(_cal_pc)));
    bool is_pc_not_glass =
      (_pc == ProjectCode::PROJECT_0210 || _pc == ProjectCode::PROJECT_0211 || _pc == ProjectCode::PROJECT_0212 ||
       _pc == ProjectCode::PROJECT_0214 || _pc == ProjectCode::PROJECT_0216 || _pc == ProjectCode::PROJECT_021D) &&
      (_type_reg_value != 0x55);
    if (is_pc_not_glass && !_cal_pc)  // 历史原因，拼接在M1的PC窗口片不进行矫正
    {
      calFunc = [](double _angle) -> double { return _angle; };
      return;
    }
    static std::array<std::array<std::function<double(double, double, double, double, double)>, 2>, 3> vec_cal_method {
      { { &WindowSliceRefractionTransform::calMethod1Inv, &WindowSliceRefractionTransform::calMethod1 },
        { &WindowSliceRefractionTransform::calMethod2Inv, &WindowSliceRefractionTransform::calMethod2 },
        { &WindowSliceRefractionTransform::calMethod3Inv, &WindowSliceRefractionTransform::calMethod3 } }
    };
    switch (_pc)
    {
    case ProjectCode::PROJECT_0217:
    {
      // C D VA B2 car is 5mm, A and B is 2mm
      if (_car_type.find('C') != std::string::npos || _car_type.find('D') != std::string::npos ||
          _car_type.find("VA") != std::string::npos || _car_type.find("B2") != std::string::npos)
      {
        calFunc = [=](double _angle) -> double {
          return vec_cal_method[METHOD_TYPE_1][static_cast<int>(_cal_refraction_not_theory)](_angle, -7.656, 7.654,
                                                                                             388.6, 0.00255);
        };
      }
      else
      {
        calFunc = [=](double _angle) -> double {
          return vec_cal_method[METHOD_TYPE_2][static_cast<int>(_cal_refraction_not_theory)](_angle, 530.5, 0.001877,
                                                                                             -1.565e-08, 0.0);
        };
      }
      break;
    }
    case ProjectCode::PROJECT_0215:
    {
      calFunc = [=](double _angle) -> double {
        return vec_cal_method[METHOD_TYPE_2][static_cast<int>(_cal_refraction_not_theory)](_angle, 530.5, 0.001877,
                                                                                           -1.565e-08, 0.0);
      };
      break;
    }
    case ProjectCode::PROJECT_0222:
    case ProjectCode::PROJECT_0216:
    {
      // 0216是直窗口片，进入窗口片和出去窗口片的折射率一样，不会产生整体上的角度偏转
      calFunc = [](double _angle) -> double { return _angle; };
      break;
    }
    case ProjectCode::PROJECT_0252:
    case ProjectCode::PROJECT_0219:
    case ProjectCode::PROJECT_0228:
    case ProjectCode::PROJECT_022A:
    {
      calFunc = [=](double _angle) -> double {
        return vec_cal_method[METHOD_TYPE_1][static_cast<int>(_cal_refraction_not_theory)](_angle, -13.79, 13.78, 460.2,
                                                                                           0.002154);
      };
      break;
    }
    case ProjectCode::PROJECT_021B:
    {
      if (_car_type == "UX")
      {
        // calFunc =
        // std::bind(vec_cal_method[METHOD_TYPE_3][static_cast<int>(_cal_refraction_not_theory)],
        // std::placeholders::_1,
        //                     -7.314e-5, 1, -0.0134,
        //                     0.0);//暂时不启用新的折射公式 20220818
        calFunc = [=](double _angle) -> double {
          return vec_cal_method[METHOD_TYPE_1][static_cast<int>(_cal_refraction_not_theory)](_angle, -7.656, 7.654,
                                                                                             388.6, 0.00255);
        };
      }
      else
      {
        calFunc = [](double _angle) -> double { return _angle; };
      }
      break;
    }
    case ProjectCode::PROJECT_0226:
    {
      //0226 DX车型窗口片与0218一致(即默认)，E1车型与0219一致
      if (_car_type == "E1")
      {
        calFunc = [=](double _angle) -> double {
          return vec_cal_method[METHOD_TYPE_1][static_cast<int>(_cal_refraction_not_theory)](_angle, -13.79, 13.78,
                                                                                             460.2, 0.002154);
        };
        break;
      }
    }
    case ProjectCode::PROJECT_0210:
    case ProjectCode::PROJECT_0210_NA:
    case ProjectCode::PROJECT_0211:
    case ProjectCode::PROJECT_0212:
    // NOTE above 3 project do not support glass yet
    case ProjectCode::PROJECT_0214:
    case ProjectCode::PROJECT_0218:
    case ProjectCode::PROJECT_021A:
    default:  // default is same as 0214 and other 5mm
    {
      if (!is_pc_not_glass)
      {
        calFunc = [=](double _angle) -> double {
          return vec_cal_method[METHOD_TYPE_1][static_cast<int>(_cal_refraction_not_theory)](_angle, -7.656, 7.654,
                                                                                             388.6, 0.00255);
        };
      }
      else
      {
        calFunc = [=](double _angle) -> double {
          return vec_cal_method[METHOD_TYPE_1][static_cast<int>(_cal_refraction_not_theory)](_angle, -6.937, 6.937,
                                                                                             616.6, 0.001616);
        };
      }
      break;
    }
    }
  }
  /**
   * @brief     构造对象后，可直接调用此函数计算窗口片折射
   *
   * @param     _angle_degree    以中间通道出射角度为0度，俯视向左为负，俯视向右为正，单位degree
   * @return    _angle_degree经过折射后的角度，单位degree
   */
  // NOLINTNEXTLINE(misc-non-private-member-variables-in-classes, readability-identifier-naming)
  std::function<double(double)> calFunc;

private:
  enum MethodType
  {
    METHOD_TYPE_1 = 0,
    METHOD_TYPE_2,
    METHOD_TYPE_3
  };
  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  static inline double calMethod1(double _angle_degree, double _a0, double _a1, double _b1, double _w)
  {
    if (std::fabs(_angle_degree) < 10e-5)
    {
      return 0.0;
    }
    double positive_angle            = std::fabs(_angle_degree);
    double positive_refraction_angle = _a0 + _a1 * std::cos(positive_angle * _w) + _b1 * std::sin(positive_angle * _w);

    return _angle_degree > 0.0 ? positive_refraction_angle : -positive_refraction_angle;
  }
  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  static inline double calMethod2(double _angle_degree, double _a, double _b, double _c, double /*_place_holder*/)
  {
    if (std::fabs(_angle_degree) < 10e-5)
    {
      return 0.0;
    }
    return _a * std::sin(_b * _angle_degree + _c);
  }
  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  static inline double calMethod3(double _angle_degree, double _a, double _b, double _c, double /*_place_holder*/)
  {
    if (std::fabs(_angle_degree) < 10e-5)
    {
      return 0.0;
    }

    double positive_angle            = std::fabs(_angle_degree);
    double positive_refraction_angle = _a * positive_angle * positive_angle + _b * positive_angle + _c;

    return _angle_degree > 0.0 ? positive_refraction_angle : -positive_refraction_angle;
  }
  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  static inline double calMethod1Inv(double _angle_degree, double _a0, double _a1, double _b1, double _w)
  {
    if (std::fabs(_angle_degree) < 10e-5)
    {
      return 0.0;
    }
    double positive_refraction_angle = std::fabs(_angle_degree);

    double c                     = positive_refraction_angle - _a0;
    double sqrt_a2_b2            = std::sqrt(_a1 * _a1 + _b1 * _b1);
    double sin_y                 = _a1 / sqrt_a2_b2;
    double angle_y               = std::asin(sin_y);
    double sin_x_y               = c / sqrt_a2_b2;
    double angle_x_y             = std::asin(sin_x_y);
    double angle_x               = angle_x_y - angle_y;
    double positive_theory_angle = angle_x / _w;

    return _angle_degree > 0.0 ? positive_theory_angle : -positive_theory_angle;
  }
  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  static inline double calMethod2Inv(double _angle_degree, double _a, double _b, double _c, double /*_place_holder*/)
  {
    if (std::fabs(_angle_degree) < 10e-5)
    {
      return 0.0;
    }
    double positive_refraction_angle = std::fabs(_angle_degree);
    double positive_theory_angle     = (std::asin(positive_refraction_angle / _a) - _c) / _b;

    return _angle_degree > 0.0 ? positive_theory_angle : -positive_theory_angle;
  }
  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  static inline double calMethod3Inv(double _angle_degree, double _a, double _b, double _c, double /*_place_holder*/)
  {
    if (std::fabs(_angle_degree) < 10e-5)
    {
      return 0.0;
    }
    double positive_refraction_angle = std::fabs(_angle_degree);

    double delta   = std::sqrt(_b * _b - 4 * _a * _c);
    double angle_1 = (-_b + delta) * 0.5 / _a;
    double angle_2 = (-_b - delta) * 0.5 / _a;

    double positive_theory_angle = angle_1;

    if (std::fabs(angle_1 - positive_refraction_angle) > std::fabs(angle_2 - positive_refraction_angle))
    {
      positive_theory_angle = angle_2;
    }

    return _angle_degree > 0.0 ? positive_theory_angle : -positive_theory_angle;
  }
};

#if __has_include(<QtWidgets/QWidget>)

/**
 * @brief     set QDoubleSpinBox
 *
 * @param     dspinbox           the pointer of QDoubleSpinBox to be set
 * @param     range_min
 * @param     range_max
 * @param     default_value
 * @param     single_step        for example 0.01
 * @param     decimals           for example 2
 * @param     suffix             for example QString("m")
 */
inline void setQDoubleSpinBox(QDoubleSpinBox* _dspinbox,      // NOLINT(bugprone-easily-swappable-parameters)
                              double _range_min,              // NOLINT(bugprone-easily-swappable-parameters)
                              double _range_max,              // NOLINT(bugprone-easily-swappable-parameters)
                              double _default_value,          // NOLINT(bugprone-easily-swappable-parameters)
                              double _single_step    = 0.01,  // NOLINT(bugprone-easily-swappable-parameters)
                              int _decimals          = 2,     // NOLINT(bugprone-easily-swappable-parameters)
                              const QString& _suffix = QString(""))

{
  _dspinbox->setRange(_range_min, _range_max);
  _dspinbox->setValue(_default_value);
  _dspinbox->setSingleStep(_single_step);
  _dspinbox->setDecimals(_decimals);
  _dspinbox->setSuffix(_suffix);
}
/**
 * @brief
 *
 * @param     spinbox            the pointer of QSpinBox to be set
 * @param     range_min
 * @param     range_max
 * @param     default_value
 * @param     single_step        for example 1
 * @param     suffix             for example QString("m")
 */
inline void setQSpinBox(QSpinBox* _dspinbox,  // NOLINT(bugprone-easily-swappable-parameters)
                        int _range_min,       // NOLINT(bugprone-easily-swappable-parameters)
                        int _range_max,       // NOLINT(bugprone-easily-swappable-parameters)
                        int _default_value,   // NOLINT(bugprone-easily-swappable-parameters)
                        int _single_step,     // NOLINT(bugprone-easily-swappable-parameters)
                        const QString& _suffix)
{
  _dspinbox->setRange(_range_min, _range_max);
  _dspinbox->setValue(_default_value);
  _dspinbox->setSingleStep(_single_step);
  _dspinbox->setSuffix(_suffix);
}

inline void sleepWithEventLoop(std::size_t _msec)
{
  QEventLoop event_loop;
  QTimer::singleShot(_msec, &event_loop, &QEventLoop::quit);
  event_loop.exec();
}

#endif  // if include QtWidgets/QWidget

}  // namespace rsfsc_lib
}  // namespace lidar
}  // namespace robosense

#endif  // RSFSCLIB_COMMON_HPP
