﻿# WidgetResultTable使用手册

## 1 控件显示

`WidgetResultTable`就是继承于QTabWidget，如果你使用代码方式编写UI，则直接new一个即可，效果类似如下：

![widget_result_table](img/widget_result_table.png)

其中

- 检测项
  - `LimitInfo`对应`extra_str_info.at(0)`字符串数据 + `(unit)`
  - `string` 对应`WidgetResultTable::addMeasureMessage`的`_describe`
- 最小值
  - `LimitInfo`对应`min_th`
  - `string` 对应`WidgetResultTable::addMeasureMessage`的`_limit`
- 最大值
  - `LimitInfo`对应`max_th`
  - `string` 对应`WidgetResultTable::addMeasureMessage`的`_limit`
- 测试值
  - 对应`WidgetResultTable::addMeasureMessage`的`_data`

## 2 控件使用

### 2.1 正常使用顺序

使用方法与之前的`WidgetLidarInfo`类似，控件配合`WidgetLogSetting`的`registerWidgetResultTable`来使用，只要注册到`WidgetLogSetting`里，就默认将控制权交出，然后用户自己布局即可

- new一个控件，按照第1章内容布局好子控件
- 注册`registerWidgetResultTable`进行注册
- 在调用`WidgetLogSetting::finishProcess`之前进行`WidgetLogSetting::addMeasureMessage`的添加，这里`不需要使用WidgetResultTable::addMeasureMessage`，`WidgetLogSetting::addMeasureMessage`会调用本控件提供的`WidgetResultTable::addMeasureMessage`
- 在调用`WidgetLogSetting::checkAllState`会将数据清空
- 显式析构

头文件

```cpp
rsfsc_lib::WidgetResultTable* widget_result_table_ { nullptr };
```

源文件

```cpp
widget_result_table_ = new rsfsc_lib::WidgetResultTable(1, this); // 索引从1开始
layout_main->addWidget(widget_result_table_); // 布局
widget_log_setting_->registerWidgetResultTable(widget_result_table_); // 注册
```

**注：**

- `WidgetResultTable`类提供的函数都在WidgetLogSetting中使用了，目前使用者可不必调用，避免使用混乱
- 一托多同理可注册多个即可
