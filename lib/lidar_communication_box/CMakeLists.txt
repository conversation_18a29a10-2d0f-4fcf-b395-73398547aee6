﻿cmake_minimum_required(VERSION 3.15.0)
cmake_policy(SET CMP0048 NEW)
if(WIN32)
  cmake_policy(SET CMP0074 NEW)
endif(WIN32)

string(TIMESTAMP PROJECT_COMPILE_DATE %Y%m%d)
project(
  lidar_communication_box
  VERSION 1.0.2.${PROJECT_COMPILE_DATE}
  LANGUAGES CXX)

# compile options
set(CMAKE_EXE_LINKER_FLAGS "-Wl,--disable-new-dtags")
add_compile_options(-std=c++14)
add_definitions(-Wall)

find_package(Threads REQUIRED)
find_package(
  Qt5
  COMPONENTS Core Widgets
  REQUIRED)

include(cmake/FindRSFSCLog.cmake)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g")
set(CMAKE_BUILD_TYPE RelWithDebInfo)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)
set(CMA<PERSON>_EXPORT_COMPILE_COMMANDS ON)

option(BUILD_EXAMPLE "build example or not" ON)

find_package(Git QUIET)
execute_process(
  COMMAND ${GIT_EXECUTABLE} rev-parse --short HEAD
  WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
  OUTPUT_VARIABLE PROJECT_COMPILE_COMMIT
  ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)
execute_process(COMMAND ${GIT_EXECUTABLE} config core.hooksPath .githooks
                WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}")

if(WIN32)
  if(CMAKE_SIZEOF_VOID_P EQUAL 8) # 64-bit
    set(Boost_ARCHITECTURE "-x64")
  elseif(CMAKE_SIZEOF_VOID_P EQUAL 4) # 32-bit
    set(Boost_ARCHITECTURE "-x32")
  endif()
  set(Boost_USE_STATIC_LIBS ON)
  set(Boost_USE_MULTITHREADED ON)
  set(Boost_USE_STATIC_RUNTIME OFF)
endif(WIN32)

# 设置源文件
set(PROJECT_HEADER src/lidar_communication_box.h src/serial_port_driver.h)
set(PROJECT_SOURCE src/lidar_communication_box.cpp src/serial_port_driver.cpp)

# 添加静态库目标，依赖于相同的对象文件
add_library(${PROJECT_NAME} STATIC ${PROJECT_HEADER} ${PROJECT_SOURCE})
target_include_directories(${PROJECT_NAME} PUBLIC include)
target_include_directories(${PROJECT_NAME} PRIVATE src)
target_include_directories(${PROJECT_NAME} SYSTEM PRIVATE ${Boost_INCLUDE_DIRS})
target_link_libraries(${PROJECT_NAME} PRIVATE ${Boost_LIBRARIES} Qt5::Core Threads::Threads)
set_target_properties(${PROJECT_NAME} PROPERTIES POSITION_INDEPENDENT_CODE ON)

if(BUILD_EXAMPLE)
  add_subdirectory(example)
endif()
