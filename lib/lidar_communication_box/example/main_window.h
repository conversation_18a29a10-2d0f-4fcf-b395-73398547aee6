﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef MAIN_WINDOW_H
#define MAIN_WINDOW_H

#include "../src/lidar_communication_box.h"
#include <QtWidgets>
#include <memory>

namespace robosense
{
namespace lidar
{
class MainWindow : public QMainWindow
{
  Q_OBJECT
public:
  explicit MainWindow(int _argc, char** _argv, QWidget* _parent = nullptr);
  MainWindow(MainWindow&&)      = delete;
  MainWindow(const MainWindow&) = delete;
  MainWindow& operator=(MainWindow&&) = delete;
  MainWindow& operator=(const MainWindow&) = delete;
  ~MainWindow() override;

protected Q_SLOTS:
  void slotConnect();
  void slotReadFirmware();
  void slotReadElectricity();
  void slotReadVoltage();
  void slotReadLidarIsExist();
  void slotSwitchLidarPower(bool _is_open);
  void slotSwitchWakeSignal(bool _is_open);
  void slotSetNetworkStandby(bool _is_standby);
  void slotSetLidarInputVoltage();

private:
  void setupLayout();

private:
  std::unique_ptr<LidarCommunicationBox> ptr_communication_box_ { nullptr };

  QComboBox* combox_lidar_index_;

  QComboBox* combox_serial_port_   = nullptr;
  QLineEdit* lineedit_password_    = nullptr;
  QPushButton* pushbutton_connect_ = nullptr;

  QPushButton* pushbutton_read_firmware_version_ = nullptr;
  QLineEdit* lineedit_firmware_version_          = nullptr;

  QPushButton* pushbutton_read_electricity_ = nullptr;
  QLineEdit* lineedit_electricity_          = nullptr;

  QPushButton* pushbutton_read_voltage_ = nullptr;
  QLineEdit* lineedit_voltage_          = nullptr;

  QPushButton* pushbutton_get_lidar_is_exist_ = nullptr;
  QLineEdit* lineedit_get_lidar_is_exist_     = nullptr;

  QPushButton* pushbutton_set_voltage_ = nullptr;
  QSpinBox* spinbox_voltage_           = nullptr;

  QCheckBox* checkbox_switch_lidar_power_ = nullptr;
  QCheckBox* checkbox_switch_wake_signal_ = nullptr;

  QCheckBox* checkbox_set_network_standby_ = nullptr;
};

}  // namespace lidar
}  // namespace robosense

#endif  // MAIN_WINDOW_H
