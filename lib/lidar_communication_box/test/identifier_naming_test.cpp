﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

/******************************************************************************
Attention:
This file is used to test the function readability-identifier-naming of clang-tidy.
It contains all kinds of identifier-naming problems and it's total a NEGATIVE example.
DO NOT imitate it please.
******************************************************************************/
constexpr int GLOBAL_CONST_VARIABLE = 0;
namespace macro_identifier_naming_test
{

#define MACRO_IDENTIFIER_NAMING_TEST

int g_global_variable                  = 0;
constexpr int NS_GLOBAL_CONST_VARIABLE = 0;

class IdentifierNamingTestClass
{
public:
  IdentifierNamingTestClass();
  ~IdentifierNamingTestClass();

protected:
  void funcAbc(int _parameter) const {}
  template <class Type>
  void funcTemp(const Type& _parameter) {};

private:
  bool variable_;
  const int CONST_VALUE                = 0;
  static constexpr int CONSTEXPR_VALUE = 0;
  static int static_value_;
};

int IdentifierNamingTestClass::static_value_ = 0;

enum IdentifierNamingTestEnum
{
  VALUE0,
  VALUE1,
  VALUE2
};

enum class IdentifierNamingTestEnumClass
{
  VALUE0,
  VALUE1,
  VALUE2
};

union IdentifierNamingTestUnion
{
  int a;
  bool b;
  char c;
};

struct MyStruct
{
  int int_struct_member;
  char char_struct_member;
};

using AliasOfAbc = IdentifierNamingTestClass;
typedef IdentifierNamingTestClass AliasOfDef;

}  // namespace macro_identifier_naming_test
