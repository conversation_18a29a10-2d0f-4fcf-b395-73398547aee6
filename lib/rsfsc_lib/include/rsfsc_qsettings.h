﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   rsfsc_qsettings.h
 * <AUTHOR> Chen (<EMAIL>)
 * @brief  check if there is already data before remove function, and if current key value is different from new value 
 *         before setValue function, if so, log who remove or who change the value, log file will be place to 
 *         ~/.RoboSense/setting_log folder
 **/
#ifndef RSFSCLIB_QSETTINGS_H
#define RSFSCLIB_QSETTINGS_H
#include <QtCore/QSettings>
#include <QtCore/QString>

namespace robosense
{
namespace lidar
{
namespace rsfsc_lib
{
class RSFSCQSettings : public QSettings
{
  Q_OBJECT
public:
  explicit RSFSCQSettings(const QString& _organization = QString("RoboSense"),
                          const QString& _application  = QString()) :
    QSettings(_organization, _application) {};
  explicit RSFSCQSettings(const QString& _file_name, QSettings::Format _format, QObject* _parent = Q_NULLPTR) :
    QSettings(_file_name, _format, _parent) {};
  explicit RSFSCQSettings(RSFSCQSettings&&)      = delete;
  explicit RSFSCQSettings(const RSFSCQSettings&) = delete;
  RSFSCQSettings& operator=(RSFSCQSettings&&) = delete;
  RSFSCQSettings& operator=(const RSFSCQSettings&) = delete;
  ~RSFSCQSettings() override                       = default;

  void remove(const QString& _key);
  void setValue(const QString& _key, const QVariant& _value);
  friend class WidgetLogSetting;

private:
  static void setUserInfo(const QString& _user_info);
  static QString user_info_;  // NOLINT(cppcoreguidelines-avoid-non-const-global-variables)
};
}  // namespace rsfsc_lib
}  // namespace lidar
}  // namespace robosense
#endif  //RSFSCLIB_QSETTINGS_H
