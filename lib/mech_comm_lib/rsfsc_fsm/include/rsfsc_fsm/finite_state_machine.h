﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef FINITE_STATE_MACHINE_H
#define FINITE_STATE_MACHINE_H

#include "finite_state_handler.h"
#include "finite_state_listener.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "rsfsc_utils/decl_name.h"
#include "rsfsc_utils/thread_ext.h"
#include <atomic>
#include <memory>
#include <unordered_map>
#include <utility>

namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

enum class ActionState;

/**
 * @class FiniteStateMachine 
 * @brief 状态机
 */
template <typename WorkModelT>
class FiniteStateMachine : public Runnable
{
  IMPL_CLASSNAME(FiniteStateMachine)
public:
  FiniteStateMachine() = default;
  ~FiniteStateMachine() override
  {
    if (static_cast<int>(current_state_) != -1)
    {
      quit();
    }
    LOG_INDEX_INFO("destructed");
  }
  FiniteStateMachine(const FiniteStateMachine&) = delete;
  FiniteStateMachine& operator=(const FiniteStateMachine&) = delete;
  FiniteStateMachine(FiniteStateMachine&&)                 = delete;
  FiniteStateMachine& operator=(FiniteStateMachine&&) = delete;

  /**
    * @brief 启动状态机
    * @param _state_id 初始状态,默认为-1(非法状态)
    */
  bool startup(ActionState _state_id = static_cast<ActionState>(-1))
  {
    // AppEvent::getInstance()->signalFsmStarting(index_);
    current_state_ = _state_id;
    return thread_.start(shared_from_this());
  }
  /**
    * @brief 退出状态机
    */
  void quit()
  {
    // AppEvent::getInstance()->signalFsmStopping(index_);
    if (state_handler_map_.find(current_state_) != state_handler_map_.end())
    {
      state_handler_map_[current_state_]->abort();
    }
    bool result = thread_.stop(4000);
    state_handler_map_.clear();
    state_listener_map_.clear();
    LOG_INDEX_DEBUG("FiniteStateMachine::thread_.stop result is {}", result);
    // AppEvent::getInstance()->signalFsmStopped(index_);
  }
  /**
    * @brief 注册状态处理器
    * @param _state_id,必须大于等于0，负数为非法状态
    * @param stateHandler 
    * @return true 
    * @return false 
    */

  /**
   * @brief     Get the State Handler
   * 
   * @param     _state_id          
   * @return    std::shared_ptr<FiniteStateHandler> 
  **/
  std::shared_ptr<WorkHandler<WorkModelT>> getStateHandler(ActionState _state_id)
  {
    if (state_handler_map_.find(_state_id) != state_handler_map_.end())
    {
      return state_handler_map_[_state_id];
    }
    LOG_INDEX_ERROR("stateID {0} not exist", _state_id);
    return nullptr;
  }

  /**
   * @brief     Get the Curr State Handler
   * 
   * @return    std::shared_ptr<WorkHandler<WorkModelT>> 
  **/
  std::shared_ptr<WorkHandler<WorkModelT>> getCurrStateHandler()
  {
    auto curr_state = currentState();
    if (state_handler_map_.find(curr_state) != state_handler_map_.end())
    {
      return state_handler_map_[curr_state];
    }
    return nullptr;
  }

  bool registerHandler(std::shared_ptr<WorkHandler<WorkModelT>> _state_handler)
  {
    auto state_id = _state_handler->getStateId();
    if (static_cast<int>(state_id) == -1)
    {
      LOG_INDEX_ERROR("state cannot be negative,stateID {0}", state_id);
      return false;
    }
    auto iter = state_handler_map_.find(state_id);
    if (iter != state_handler_map_.end())
    {
      LOG_INDEX_ERROR("there is exist stateID {0}", state_id);
      return false;
    }
    LOG_INDEX_DEBUG("insert state: {0}", _state_handler->className());
    if (!work_model_ptr_vec_.empty())
    {
      _state_handler->setWorkModelVec(work_model_ptr_vec_);
    }
    state_handler_map_.insert({ state_id, std::move(_state_handler) });
    return true;
  }
  /**
    * @brief 注册状态监听器
    * @param listener 
    */
  bool registerListener(std::shared_ptr<FiniteStateListener> _listener)
  {
    if (static_cast<int>(_listener->getState()) < 0)
    {
      LOG_INDEX_ERROR("state cannot be negative,stateID {0}", _listener->getState());
      return false;
    }

    auto iter = state_listener_map_.find(_listener->getState());
    if (iter != state_listener_map_.end())
    {
      LOG_INDEX_ERROR("there is exist stateID {0}", _listener->getState());
      return false;
    }
    state_listener_map_.insert({ _listener->getState(), std::move(_listener) });

    return true;
  }

  void setWorkModelVec(const std::vector<std::shared_ptr<WorkModelT>> _work_model_ptr_vec)
  {
    work_model_ptr_vec_ = _work_model_ptr_vec;
    for (auto& [state, handler] : state_handler_map_)
    {
      handler->setWorkModelVec(work_model_ptr_vec_);
    }
  }
  std::vector<std::shared_ptr<WorkModelT>>& getWorkModelVec() { return work_model_ptr_vec_; }

  /**
    * @brief 移除状态监听器
    * @param listener 
    */
  void removeListener(std::shared_ptr<FiniteStateListener> _listener);

  /**
     * @brief 获取当前状态
     * @return int 
     */
  ActionState currentState() const { return current_state_; }

  /**
     * @brief 改变当前状态
     * @param _state_id 
     * @return true 
     * @return false 
     */
  bool changeState(ActionState _state_id)
  {
    LOG_INDEX_DEBUG("FiniteStateMachine::changeState to {}", static_cast<int>(_state_id));
    std::lock_guard<std::mutex> guard(state_mutex_);
    change_state_    = _state_id;
    is_change_state_ = true;
    return true;
  }

  void setStateEndCallback(std::function<void()> _callback) { state_end_callback_ = std::move(_callback); }
  void setIndex(int _index) { index_ = _index; }
  int getIndex() const { return index_; }
  int getLidarIndex() const { return index_; }
  int getLogIndex() const { return index_; }

  void abort()
  {
    for (auto& handler : state_handler_map_)
    {
      handler.second->abort();
    }
  }
  bool isRunning() { return is_running_; }

private:
  /**
    * @brief 状态机线程函数
    */
  void run(Thread& _thread) override
  {
    if (state_handler_map_.empty())
    {
      LOG_INDEX_ERROR("state_handler_map_ is empty");
      return;
    }

    auto curr_iter = state_handler_map_.find(current_state_);
    LOG_INDEX_DEBUG("FiniteStateMachine::run first State: {0}", curr_iter->second->className());
    if (curr_iter == state_handler_map_.end())
    {
      LOG_INDEX_ERROR("current state not exist, finite state machine break");
      return;
    }
    // AppEvent::getInstance()->signalFsmStarted(index_);
    RSFSCLog::getInstance(getIndex())->info("状态机开始运行");
    is_running_ = true;
    while (_thread.isRunning())
    {
      auto& curr_handler = curr_iter->second;
      auto next_state    = curr_handler->handleState();
      if (static_cast<int>(next_state) == -1)
      {
        current_state_ = static_cast<ActionState>(-1);
        LOG_INDEX_DEBUG("nextState index -1, 退出状态机");
        break;
      }
      {
        std::lock_guard<std::mutex> guard(state_mutex_);
        if (is_change_state_)
        {
          next_state       = change_state_;
          is_change_state_ = false;
        }
      }

      auto next_iter = state_handler_map_.find(next_state);
      if (next_iter == state_handler_map_.end())
      {
        LOG_INDEX_ERROR("next state {0} not exist, finite state machine break", next_state);
        break;
      }

      curr_handler->transmitMember(current_state_, next_iter->second);
      LOG_INDEX_INFO("{} -->> {}", curr_handler->className(), next_iter->second->className());

      auto listener_iter = state_listener_map_.find(current_state_);
      if (listener_iter != state_listener_map_.end())
      {
        listener_iter->second->onStateChanged();
      }
      current_state_ = next_state;
      curr_iter      = next_iter;
    }

    is_running_ = false;
    // app()->signalFsmStopped(index_);
    if (state_end_callback_)
    {
      state_end_callback_();
    }
    LOG_INDEX_DEBUG("状态机结束");
  }

  /**
      * @brief 获取下一个状态处理器
      * @param _state_id 
      * @return std::shared_ptr<WorkHandler<WorkModelT>> 
      */
  std::shared_ptr<WorkHandler<WorkModelT>> getNextHandler(ActionState _state_id);

private:
  std::vector<std::shared_ptr<WorkModelT>> work_model_ptr_vec_;
  std::unordered_map<ActionState, std::shared_ptr<WorkHandler<WorkModelT>>> state_handler_map_;
  std::unordered_map<ActionState, std::shared_ptr<FiniteStateListener>> state_listener_map_;
  std::mutex state_mutex_;
  Thread thread_;

  std::function<void()> state_start_callback_;
  std::function<void()> state_end_callback_;

  ActionState current_state_ = static_cast<ActionState>(-1);
  ActionState change_state_  = static_cast<ActionState>(-1);
  bool is_change_state_      = false;
  bool is_running_           = false;
  std::atomic<bool> is_abort_ { false };
  int index_ = 0;
};

}  // namespace lidar
}  // namespace robosense

#endif  // FINITE_STATE_MACHINE_H
