# /******************************************************************************
#  * Copyright 2024 RoboSense All rights reserved.
#  * Suteng Innovation Technology Co., Ltd. www.robosense.ai
#  *
#  * This software is provided to you directly by RoboSense and might
#  * only be used to access RoboSense LiDAR. Any compilation,
#  * modification, exploration, reproduction and redistribution are
#  * restricted without RoboSense's prior consent.
#  *
#  * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
#  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
#  * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
#  * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
#  * INDIRECT, INCIDENT<PERSON>, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
#  * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR
#  * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
#  * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
#  * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
#  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  * POSSIBILITY OF SUCH DAMAGE.
#  *****************************************************************************/

release_dir="release"
echo "current path: $(pwd)"
# 检查release文件夹是否存在deb则删除
if [ -d "./$release_dir" ]; then
    find "./$release_dir" -type f -name "*.deb" -delete
    echo "Deleted .deb files in ./$release_dir directory"
else
    echo "Release directory does not exist"
fi

cd build
cpack
cd ..

mkdir -p ./release/doc
cp ./CHANGELOG.md ./$release_dir/
cp -r ./doc/验证报告 ./$release_dir/doc/
cp ./script/install.sh ./$release_dir/

./script/create_md5.sh ./release