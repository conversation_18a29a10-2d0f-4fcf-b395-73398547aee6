﻿# 速腾自动化线通讯协议
|修订版本号|修订时间|变更项|参与人|
|:---:|:---:|:---|:---|
|v1.0.0|20211216|初版|<PERSON>、<PERSON><PERSON>、<PERSON><PERSON>、<PERSON>、Alan <PERSON>|

## 1 背景
自动化产线工控设备与测试软件之间需要通讯完成DUT测试开始通知，测试结束通知等消息的沟通来完成整个自动化测试流程，故需要制定一标准通讯协议来完成此项任务。
## 2 实现
选择TCP协议作为测试软件与自动化设备软件通讯的协议,测试软件和设备之间通讯采用C/S架构。设备端作为服务器，测试软件作为客户端。  
### 2.1 协议内容
* 通讯协议报文由六部分组成：**主体,协议版本号,治具位号,命令,数据结果,结束标识符**
* 协议内容识别区分大小写，必须为ASCII内容
* 协议版本的更新，务必跟以下部门负责人进行沟通：**自动化部门**、**标定组**、**电控组**
* 各部分之间采用英文半角逗号进行隔开
* 数据结果可以没有也可以有多个，相互之间采用英文半角逗号进行隔开

### 2.2 协议字段定义 

|协议标识|类型|说明|
|:---|:---|:---|
|主体语句|S|服务端命令帧头，一般为自动化设备|
||C|客户端命令帧头，一般为测试软件|
|协议版本号|1|以数字表示，当前版本号为1|
|治具位号|DUT、DUT1、DUT2 ...|被测物编号，从1开始。DUT表示所有的被测产品|
|命令语句|Ready|DUT就绪|
||Begin|测试就绪|
||Finish|测试完成|
||Abort|测试终止|
|数据语句|Fail|被测物测试失败，同时传多个产品时，以英文逗号分割|
||Pass|被测物测试通过，同时传多个产品时，以英文逗号分割|
||ABC123|被测物SN码，如果是整机SN，需要以`SN-项目编号`的形式传送，同时传多个产品时，以英文逗号分割|
|结束标识语句|0x0D 0x0A|16进制|

### 2.3 协议过程描述
1. 物料准备完成事件  
   + **Server**准备好待测物后发送：`S,版本号,DUT1,Ready,ABC123,结束符`  
   + **Client**收到`Ready`信号后，回复：`C,版本号,DUT1,Ready,ABC123,结束符`，等待**Server**的开始信号
   + **Server**收到回复后，校验SN与自己发送的是否一致
       + 如果一致，则发送`S,版本号,DUT1,Begin,ABC123,结束符`,**Client**在接收到`Begin`指令后，开始测试
       + 如果不一致，则重发`Ready`

2. 测试完成事件  
   **Client**完成测试后发送信号：`C,版本号,DUT1,Finish,Pass(or Fail),结束符`  
   **Server**收到`Finish`信号后，将产品取走后，回复：`S,版本号,DUT1,Finish,Pass(or Fail),结束符`

3. 测试中断事件   
   **Server**通知Client终止测试，发送：`S,版本号,DUT1,Abort,结束符`．并自动将该标定件标记为Fail  
   **Client**收到终止信号，立即回复信号并中断测试，回复：`C,版本号,DUT1,Abort,结束符`。

**注：** 目前有两种一拖多的情况，情况一是每个单独开始单独结束，互不影响，例如老化；情况二是多个产品必须同时开始，同时结束，无法单独进行，例如整机性能评估。对于情况二，则可以将上述`DUT1`替换为`DUT`，然后在数据语句中以逗号分割放置多个产品的数据，如`S,版本号,DUT,Ready,ABC133,ABC124,结束符`。且`DUT`情况时，所有数据务必按照治具编号从小到大放置，例如只有2个产品，则只能放在治具1和治具2，不能放在治具3中。

## 3 自动化设备协议功能安全
* server发送信号后等待10s，未回复，则发送中断信号并判断为Fail(不需要重复发送，TCP是可靠连接)
* server发送InPosition命令后，等待**200s（每个工位可单独设置）**，未收到结束语句，则发送中断信号并判断为Fail;
* 同个工装连续三次均为Fail，改测试工装停止测试并报警；
* 目前考虑利用TCP自身的心跳包机制，不在协议中增加心跳包，后续有需要再增加
* 所有自动化设备、测试电脑用到的IP及端口号，务必向IT备案，满足IT对网络资源的规划

## 4 待讨论
