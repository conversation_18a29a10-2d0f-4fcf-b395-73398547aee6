﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "mainwindow.h"
#include "app_event.h"
#include "config.h"
#include "rsfsc_lib/include/widget_lidar_info.h"
#include "rsfsc_lib/include/widget_result_table.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "rsfsc_utils/csv_utils.h"
#include "ui_mainwindow.h"
#include "widgets/widget_mes.h"
#include "widgets/widget_para_setting.h"
#include <QCheckBox>
#include <QCloseEvent>
#include <QComboBox>
#include <QDir>
#include <QDoubleSpinBox>
#include <QLabel>
#include <QLineEdit>
#include <QMessageBox>
#include <QPushButton>
#include <QSerialPortInfo>
#include <QSettings>
#include <QSpinBox>
#include <QString>
#include <QTextEdit>

using namespace robosense::lidar;

// NOLINTNEXTLINE(google-build-using-namespace)
MainWindow::MainWindow(QWidget* _parent) : QMainWindow(_parent), ui_(new Ui::MainWindow)
{
  qRegisterMetaType<uint32_t>("uint32_t");
  qRegisterMetaType<uint16_t>("uint16_t");
  ui_->setupUi(this);
  app()->setUi(ui_);
  readSettings();

  // 必须先初始化csv
  initCsvLimit();

  initWidgets();
  initMesWidget();

  initSignalSlots();
  app()->getWidgetMes()->show();
}

MainWindow::~MainWindow() { delete ui_; }

void MainWindow::initWorkModel()
{
  // updateModelParaInfo();
  // initFsm();
}

void MainWindow::initMesWidget()
{
  RSFSCLog::getInstance()->setQtLogWidget(ui_->message_browser, "slotShowMessage");
  app()->setWidgetMes(new WidgetMes());
  // app()->getWidgetMes()->addCheckSumDir(std::string(INSTALL_PREFIX_SHARE) + "config");

  int row    = 0;
  int column = 0;
  for (int i = 1; i <= widget_para_setting_->getLidarNum(); ++i)
  {
    lidar_info_map_[i]                    = LidarInfoStruct();
    auto& lidar_info_struct               = lidar_info_map_[i];
    lidar_info_struct.widget_lidar_info   = new rsfsc_lib::WidgetLidarInfo(PROJECT_NAME, 1, nullptr);
    lidar_info_struct.widget_result_table = new rsfsc_lib::WidgetResultTable(i, this);
    app()->getWidgetMes()->registerWidgetLidarInfo(lidar_info_struct.widget_lidar_info);
    app()->getWidgetMes()->registerWidgetResultTable(lidar_info_struct.widget_result_table);
    lidar_info_struct.widget_lidar_info->setFixedLidarInstallPosition(rsfsc_lib::LIDAR_INSTALL_POSITION_FL);
    lidar_info_struct.widget_lidar_info->setLidarSNPos(row++, column);
    lidar_info_struct.widget_lidar_info->setCableSNPos(row++, column);  // 添加线束SN控件
    lidar_info_struct.widget_lidar_info->setProjectCodePos(row++, column);
    lidar_info_struct.widget_lidar_info->setIPPos(row++, column);
    lidar_info_struct.widget_lidar_info->setMSOPPos(row++, column);
    lidar_info_struct.widget_lidar_info->setDIFOPPos(row++, column);
    lidar_info_struct.button_connect          = new QPushButton("连接", this);
    lidar_info_struct.button_open_data_folder = new QPushButton("打开数据路径", this);
    lidar_info_struct.label_test_state        = new LabelTestState(this);

    ui_->lidar_info_groupbox->layout()->addWidget(lidar_info_struct.label_test_state);
    ui_->lidar_info_groupbox->layout()->addWidget(lidar_info_struct.widget_lidar_info);
    ui_->lidar_info_groupbox->layout()->addWidget(lidar_info_struct.button_connect);
    ui_->lidar_info_groupbox->layout()->addWidget(lidar_info_struct.button_open_data_folder);
    app()->getWidgetMes()->registerWidgetResultTable(lidar_info_struct.widget_result_table);
    QGridLayout* layout = dynamic_cast<QGridLayout*>(ui_->centralwidget->layout());
    layout->addWidget(lidar_info_struct.widget_result_table, 0, 1);
    lidar_info_struct.widget_lidar_info->setFixedProjectCode(
      lidar_info_struct.widget_lidar_info->getProjectCodeIndex());
  }
  rsfsc_lib::WidgetLidarInfo::setNotCheckSNProject("0350;0351;0352;0360");
  LOG_INFO("设置不检查SN项目: {}", "0350;0351;0352;0360");
}
void MainWindow::initWidgets()
{
  QString version_str = PROJECT_VERSION_STR;
  QString title       = fmt::format("{} {}_{}", PROJECT_NAME_ZH, PROJECT_VERSION_STR, PROJECT_COMPILE_TIME).c_str();
  setWindowTitle(title);
  setWindowIcon(QIcon(QString(":/img/%1.png").arg(PROJECT_NAME)));
  ui_->label_title->setText(QString("<h2>%1 <br>v%2</h2>").arg(PROJECT_NAME_ZH).arg(version_str));

  // 创建about窗口
  widget_about_ = new WidgetAbout(this);
  widget_about_->setName(QString::fromUtf8("MECH连通性测试"));
  widget_about_->setBrief(QString::fromUtf8("此软件用于Airy连通性测试"));
  widget_about_->setVersionStr("v" + version_str);
  widget_about_->setBuildTime(QString::fromUtf8(PROJECT_COMPILE_TIME));
  widget_about_->setBuildCommit(QString::fromUtf8(PROJECT_COMPILE_COMMIT));
  widget_about_->setYearStartCopyright(2024);
  widget_about_->setContactEmail("<EMAIL>");

  // 初始化widget setting
  widget_para_setting_ = new WidgetParaSetting(this);

  // 盒子
  connect(ui_->button_refresh_port_name, &QPushButton::clicked, this, &MainWindow::slotRefreshPortName);
  slotRefreshPortName();
  for (int i = 1; i <= widget_para_setting_->getLidarNum(); ++i)
  {
    ui_->combobox_box_lidar_index->addItem(QString::number(i));
  }
}

void MainWindow::initCsvLimit()
{
  QString root_path = INSTALL_PREFIX_SHARE;
  if (QDir::currentPath().contains("build"))
  {
    root_path = QDir::currentPath() + "/../";
  }
  else if (QDir::current().exists("build"))
  {
    root_path = QDir::currentPath() + "/";
  }

  QDir config_dir(root_path + "config");
  QStringList csv_files = config_dir.entryList(QStringList() << "*.csv", QDir::Files);
  LOG_INFO("config_dir: {}", csv_files.join(";").toStdString());

  for (const auto& csv_file : csv_files)
  {
    if (csv_file.contains("_limit.csv") || csv_file.contains("_register.csv"))
    {
      QString project;
      if (csv_file.contains("_limit.csv"))
      {
        project = csv_file.left(csv_file.indexOf("_limit.csv"));
      }
      else
      {
        project = csv_file.left(csv_file.indexOf("_register.csv"));
      }

      QString file_path                        = config_dir.filePath(csv_file);
      std::shared_ptr<CsvUtils> csv_parser_ptr = std::make_shared<CsvUtils>();

      try
      {
        if (csv_file.contains("_register.csv"))
        {
          csv_parser_ptr->loadRegisterCsvInfo(file_path);
          app()->setCsvUtils(project + "_reg", csv_parser_ptr);
          LOG_INFO("成功加载寄存器文件: {}", file_path.toStdString());
        }
        else if (csv_file.contains("_limit.csv"))
        {
          csv_parser_ptr->loadLimitCsvInfo(file_path);
          app()->setCsvUtils(project + "_limit", csv_parser_ptr);
          LOG_INFO("成功加载限值文件: {}", file_path.toStdString());
        }
      }
      catch (const std::exception& e)
      {
        LOG_ERROR("加载csv文件失败: {}, {}", file_path.toStdString(), e.what());
        continue;
      }
    }
  }
}

void MainWindow::closeEvent(QCloseEvent* _event)
{
  RSFSCLog::getInstance()->info("FUC: closeEvent call back");
  QMessageBox::StandardButton msg_box = QMessageBox::warning(this, "警告", "<font color='red'>确定退出?</font>",
                                                             QMessageBox::Yes | QMessageBox::No, QMessageBox::No);

  if (msg_box == QMessageBox::No)
  {
    _event->ignore();
    return;
  }

  writeSettings();
  if (app()->getWidgetMes()->isVisible())
  {
    app()->getWidgetMes()->close();
  }

  RSFSCLog::getInstance()->setQtLogWidget(nullptr);
  // delete app()->getWidgetMes();

  QMainWindow::closeEvent(_event);
}

void MainWindow::readSettings()
{
  LOG_INFO("FUC: read settings");
  QString suffix(PROJECT_NAME);
  suffix += QString::fromUtf8("/main_window");
  QSettings settings("RoboSense", suffix);

  restoreGeometry(settings.value("geometry").toByteArray());
  restoreState(settings.value("window_state").toByteArray());

  auto apply_setting = [&settings](auto* _widget, const QString& _key) {
    QVariant value = settings.value(_key);
    if (value.isValid())
    {
      // LOG_DEBUG("key is valid: {}, value: {}", _key.toStdString(), value.toString().toStdString());
      if (auto* check_box = dynamic_cast<QCheckBox*>(_widget))
      {
        check_box->setChecked(value.toBool());
      }
      else if (auto* spin_box = dynamic_cast<QSpinBox*>(_widget))
      {
        spin_box->setValue(value.toInt());
      }
      else if (auto* double_spin_box = dynamic_cast<QDoubleSpinBox*>(_widget))
      {
        double_spin_box->setValue(value.toDouble());
      }
      else if (auto* line_edit = dynamic_cast<QLineEdit*>(_widget))
      {
        line_edit->setText(value.toString());
      }
      else if (auto* label = dynamic_cast<QLabel*>(_widget))
      {
        label->setText(value.toString());
      }
      else if (auto* text_edit = dynamic_cast<QTextEdit*>(_widget))
      {
        text_edit->setText(value.toString());
      }
      else if (auto* combo_box = dynamic_cast<QComboBox*>(_widget))
      {
        combo_box->setCurrentText(value.toString());
      }
    }
  };

  for (auto* widget : ui_->centralwidget->findChildren<QWidget*>())
  {
    QString object_name = widget->objectName();
    if (object_name.isEmpty() || object_name.startsWith("qt_"))
    {
      continue;
    }
    // LOG_DEBUG("object_name: {}", object_name.toStdString());

    QString key = "ui/" + object_name;
    apply_setting(widget, key);
  }
}

void MainWindow::writeSettings()
{
  RSFSCLog::getInstance()->info("write settings");
  QString suffix(PROJECT_NAME);
  suffix += QString::fromUtf8("/main_window");
  QSettings settings("RoboSense", suffix);
  settings.setValue("geometry", saveGeometry());
  settings.setValue("window_state", saveState());

  auto apply_setting = [&settings](auto* _widget, const QString& _key) {
    if (auto* check_box = dynamic_cast<QCheckBox*>(_widget))
    {
      settings.setValue(_key, check_box->isChecked());
    }
    else if (auto* spin_box = dynamic_cast<QSpinBox*>(_widget))
    {
      settings.setValue(_key, spin_box->value());
    }
    else if (auto* double_spin_box = dynamic_cast<QDoubleSpinBox*>(_widget))
    {
      settings.setValue(_key, double_spin_box->value());
    }
    else if (auto* line_edit = dynamic_cast<QLineEdit*>(_widget))
    {
      settings.setValue(_key, line_edit->text());
    }
    // else if (auto* label = dynamic_cast<QLabel*>(_widget))
    // {
    //   settings.setValue(_key, label->text());
    // }
    else if (auto* text_edit = dynamic_cast<QTextEdit*>(_widget))
    {
      settings.setValue(_key, text_edit->toPlainText());
    }
    else if (auto* combo_box = dynamic_cast<QComboBox*>(_widget))
    {
      settings.setValue(_key, combo_box->currentText());
    }
  };

  for (auto* widget : ui_->centralwidget->findChildren<QWidget*>())
  {
    QString object_name = widget->objectName();
    if (object_name.isEmpty())
    {
      continue;
    }
    QString key = "ui/" + object_name;
    apply_setting(widget, key);
  }
}

void MainWindow::initSignalSlots()
{
  connect(AppEvent::getInstance(), &AppEvent::signalFsmStarting, this, &MainWindow::slotFsmStarting);
  connect(AppEvent::getInstance(), &AppEvent::signalFsmStarted, this, &MainWindow::slotFsmStarted);
  connect(AppEvent::getInstance(), &AppEvent::signalFsmStopping, this, &MainWindow::slotFsmStopping);
  connect(AppEvent::getInstance(), &AppEvent::signalFsmStopped, this, &MainWindow::slotFsmStopped);

  connect(AppEvent::getInstance(), &AppEvent::signalLidarConnecting, this, &MainWindow::slotLidarConnecting);
  connect(AppEvent::getInstance(), &AppEvent::signalLidarConnected, this, &MainWindow::slotLidarConnected);
  connect(AppEvent::getInstance(), &AppEvent::signalLidarDisconnecting, this, &MainWindow::slotLidarDisconnecting);
  connect(AppEvent::getInstance(), &AppEvent::signalLidarDisconnected, this, &MainWindow::slotLidarDisconnected);

  connect(app(), &AppEvent::signalShowErrorMessageBox, this, &MainWindow::slotShowErrorMessageBox);

  connect(ui_->action_mes_setting, &QAction::triggered, app()->getWidgetMes(), &WidgetMes::show);
  connect(ui_->action_about, &QAction::triggered, widget_about_, &WidgetAbout::show);
  connect(ui_->action_para_setting, &QAction::triggered, widget_para_setting_, &WidgetParaSetting::show);
}

void MainWindow::updateModelParaInfo() {}

void MainWindow::slotRefreshPortName()
{
  auto port_infos = QSerialPortInfo::availablePorts();
  ui_->combobox_box_port_name->clear();
  for (const auto& port_info : port_infos)
  {
    ui_->combobox_box_port_name->addItem(port_info.portName());

    // 打印串口的各种信息
    LOG_INFO("port_name: {}, description: {}, manufacturer: {}, serial_number: {}, system_location: {}, vendor_id: {}, "
             "product_id: {}",
             port_info.portName(), port_info.description(), port_info.manufacturer(), port_info.serialNumber(),
             port_info.systemLocation(), port_info.vendorIdentifier(), port_info.productIdentifier());
  }
}

void MainWindow::slotFsmStarting(const int _lidar_index) { ui_->button_one_key_run->setText("启动中"); }
void MainWindow::slotFsmStarted(const int _lidar_index) { ui_->button_one_key_run->setText("停止运行"); }
void MainWindow::slotFsmStopping(const int _lidar_index) { ui_->button_one_key_run->setText("停止中"); }
void MainWindow::slotFsmStopped(const int _lidar_index) { ui_->button_one_key_run->setText("一键运行"); }
void MainWindow::slotShowErrorMessageBox(const QString& _msg)
{
  LOG_ERROR("{}", _msg);
  QMessageBox::warning(this, "错误", _msg);
}

// void MainWindow::slotButtonOneKeyRunClicked()
// {
//   if (ui_->button_one_key_run->text() == "一键运行")
//   {
//     LOG_INFO("一键运行...");
//     slotUpdateProgressCollect(0);
//     slotUpdateProgressProcessData(0);
//     startUpFsm();
//   }
//   else
//   {
//     LOG_INFO("停止中...");
//     shutDownFsm();
//   }
// }

void MainWindow::slotLidarConnecting(const int _lidar_index)
{
  if (lidar_info_map_.find(_lidar_index) != lidar_info_map_.end())
  {
    auto& lidar_struct = lidar_info_map_[_lidar_index];
    lidar_struct.button_connect->setText("连接中");
  }
}
void MainWindow::slotLidarConnected(const int _lidar_index)
{
  if (lidar_info_map_.find(_lidar_index) != lidar_info_map_.end())
  {
    auto& lidar_struct = lidar_info_map_[_lidar_index];
    lidar_struct.button_connect->setText("断开连接");
  }
}
void MainWindow::slotLidarDisconnecting(const int _lidar_index)
{
  if (lidar_info_map_.find(_lidar_index) != lidar_info_map_.end())
  {
    auto& lidar_struct = lidar_info_map_[_lidar_index];
    lidar_struct.button_connect->setText("断开连接中");
  }
}
void MainWindow::slotLidarDisconnected(const int _lidar_index)
{
  if (lidar_info_map_.find(_lidar_index) != lidar_info_map_.end())
  {
    auto& lidar_struct = lidar_info_map_[_lidar_index];
    lidar_struct.button_connect->setText("连接");
  }
}
