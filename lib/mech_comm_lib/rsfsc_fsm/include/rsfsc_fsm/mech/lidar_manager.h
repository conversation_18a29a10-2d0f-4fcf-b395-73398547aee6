﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef LIDAR_MANAGER_H
#define LIDAR_MANAGER_H

#include "mech_communication/mech_communication.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "rsfsc_lib/include/widget_lidar_info.h"
#include "rsfsc_utils/tcpdump_utils.h"
#include <QDateTime>
#include <QString>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

class MechUdp;
struct LimitInfo;
namespace rsfsc_lib
{
class WidgetLogSetting;
}  // namespace rsfsc_lib

// NOLINTNEXTLINE(fuchsia-multiple-inheritance)
class LidarManager : public MechCommunication
{
public:
  LidarManager(const LidarManager&) = delete;
  LidarManager(LidarManager&&)      = delete;
  explicit LidarManager(rsfsc_lib::WidgetLidarInfo* _lidar_info);
  LidarManager& operator=(LidarManager&&) = delete;
  LidarManager& operator=(const LidarManager&) = delete;
  ~LidarManager() override;

  struct MotorStatus
  {
    uint8_t resp_code;       // 01 表示正常应答
    uint8_t cmd_code;        // C1 代表应答B1命令
    uint16_t motor_speed;    // 电机速度
    uint16_t motor_current;  // 电机电流
    uint16_t motor_status;   // 编码器状态
    void toBigEndian();
  };

  void abort() override;
  int getLidarIndex();
  QString getLidarInfoSn();
  rsfsc_lib::WidgetLidarInfo* getLidarInfo() { return lidar_info_; }

  bool connectAndWaitForTop(const uint32_t _msec = 50000);
  bool getConnectedIpPort(QString& _ip, int& _port);
  bool scanFirstLidarAndSetIP();
  std::optional<mech::DifopPacket> getOneDifopPacket(const int _timeout_s = 10);
  std::optional<mech::MountType> getMountType();

  bool changeLidarNet(const QString& _ip, const int _msop_port, const int _difop_port);
  bool changeLidarNet(const QString& _ip, const int _msop_port, const int _difop_port, const QString& _mes_mac_address);

  void setCsvVerificationFilePath(const QString& _file_path) { csv_verification_file_path_ = _file_path; }
  QString getCsvVerificationFilePath() const { return csv_verification_file_path_; }

  bool writeRegDataWithVer(uint32_t _address, uint32_t _value, const QString& _reg_name = "");
  bool writeRegDataWithVer(uint32_t _reg_start_addr,
                           const std::vector<uint32_t>& _reg_val,
                           const QString& _reg_name_prefix = "");

  bool writeVerificationDataToFile(const QString& _reg_name,
                                   uint32_t _address,
                                   uint32_t _write_value,
                                   uint32_t _read_value,
                                   bool _is_match);

  bool writeVerificationDataToFile(const QString& _reg_name,
                                   uint32_t _address,
                                   float _write_value,
                                   float _read_value,
                                   bool _is_match);

  bool writeReflBit(const QString& _file_path, const bool _append_crc = true);
  bool writeDynamicBit(const QString& _file_path, const bool _append_crc = true);
  bool writeStaticBit(const QString& _file_path, const bool _append_crc = true);
  bool writeTwoDimBit(const QString& _file_path, const bool _append_crc = true);
  bool writeAbsBit(const QString& _file_path, const bool _append_crc = true);
  template <typename T>
  bool writeBit(const std::string& _file_path, uint32_t _addr_start, const bool _append_crc = true);

  QString getConfigParamLidarSN();

  bool isNetNeedChange(bool& _is_need_change, const QString& _ip, const int _msop_port, const int _difop_port);
  bool startMonitorDifop();
  bool stopMonitorDifop();

  bool firmwareUpdate(const uint16_t& _cmd_type, const QString& _file_path);
  bool firmwareUpdateApp(const QString& _file_path);
  bool firmwareUpdateBot(const QString& _file_path);
  bool firmwareUpdateTop(const QString& _file_path);
  bool firmwareUpdateWriteConfig(const QString& _file_path);
  bool firmwareUpdateCheckConfig(const QString& _file_path, const QString& _read_val_file_path);

  bool writeTopFlashWithVer(const QString& _name, const std::vector<uint8_t>& _data, const uint32_t _addr_start);

  bool startEncodCalib(const int _timeout_secs = 120);
  bool stopEncodCalib();
  bool getEncodCalibData(std::vector<uint32_t>& _coeff_vec, std::vector<uint32_t>& _insert_step);
  bool sendScaleAvgToMotor(const std::vector<uint32_t>& _scale_avg);

  void setReadDifopCallBack(const std::function<void(const mech::DifopPacket&)>& _difop_cb) { difop_cb_ = _difop_cb; }
  void setReadMsopCallBack(const std::function<void(const mech::MsopPacket&)>& _msop_cb) { msop_cb_ = _msop_cb; }

  bool startTcpdumpBothOrgAndObjIPExcludeMSOP(const QString& _dump_file_path);
  bool startTcpdumpExcludeMSOP(const QString& _dump_file_path);
  void stopTcpdumpExcludeMSOP();

  bool startTcpdumpOnlyMSOP(const QString& _dump_file_path);
  void stopTcpdumpOnlyMSOP();

  std::optional<MotorStatus> readMotorStatus(const int _timeout_ms = 5000);

private:
  mech::DifopPacket difop_pkt_;
  mech::MsopPacket msop_pkt_;

  TcpdumpUtils tcpdump_exclude_msop_;
  TcpdumpUtils tcpdump_only_msop_;

  std::function<void(const mech::DifopPacket&)> difop_cb_;
  std::function<void(const mech::MsopPacket&)> msop_cb_;

  rsfsc_lib::WidgetLidarInfo* lidar_info_;
  std::atomic<bool> is_abort_ { false };
  std::shared_ptr<MechUdp> difop_udp_client_;

  QString lidar_sn_;
  QString ip_addr_;
  uint16_t msop_port_  = 0;
  uint16_t difop_port_ = 0;
  QString csv_verification_file_path_;
  uint32_t pl_version_       = 0;
  uint32_t ps_version_       = 0;
  uint32_t software_version_ = 0;
  uint32_t motor_version_    = 0;
  uint32_t config_version_   = 0;

  std::mutex mtx_msop_;
  std::condition_variable cv_msop_;

private:
};

}  // namespace lidar
}  // namespace robosense
#endif  // LIDAR_MANAGER_H