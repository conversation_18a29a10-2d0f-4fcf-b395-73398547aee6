﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "rsfsc_fsm/mech/lidar_manager.h"
#include "app_event.h"
#include "mech_communication/protocol/codec/airy_crc32_codec.h"
#include "mech_udp.h"
#include "rsfsc_lib/include/widget_lidar_info.h"
#include "rsfsc_lib/include/widget_log_setting.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QByteArray>
#include <QFile>
#include <QHostAddress>
#include <boost/endian/conversion.hpp>
#include <string>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

void LidarManager::MotorStatus::toBigEndian()
{
  motor_speed   = boost::endian::native_to_big(motor_speed);
  motor_current = boost::endian::native_to_big(motor_current);
  motor_status  = boost::endian::native_to_big(motor_status);
}

LidarManager::LidarManager(rsfsc_lib::WidgetLidarInfo* _widget_lidar_info) :
  MechCommunication(_widget_lidar_info->getProjectCodeStr().toStdString(),
                    static_cast<int>(_widget_lidar_info->getLidarIndex())),
  lidar_info_(_widget_lidar_info)
{
  tcpdump_exclude_msop_.setLogIndex(getLidarIndex());
  tcpdump_only_msop_.setLogIndex(getLidarIndex());
}

LidarManager::~LidarManager() { LOG_INDEX_INFO("destructed"); }

void LidarManager::abort()
{
  is_abort_ = true;
  cv_msop_.notify_all();
  MechCommunication::abort();
}

int LidarManager::getLidarIndex() { return static_cast<int>(lidar_info_->getLidarIndex()); }
QString LidarManager::getLidarInfoSn() { return getLidarInfo()->getLidarSN(); }

bool LidarManager::connectAndWaitForTop(const uint32_t _msec)
{
  if (!connect(_msec))
  {
    return false;
  }
  return waitForTopStartUp(_msec);
}
bool LidarManager::getConnectedIpPort(QString& _ip, int& _port)
{
  if (!isConnected())
  {
    return false;
  }
  _ip   = QString::fromStdString(getIP());
  _port = getPort();
  return true;
}
bool LidarManager::scanFirstLidarAndSetIP()
{
  if (auto ip_port = TcpdumpUtils().captureOneAiryNetInfo())
  {
    lidar_info_->setIP(QString::fromStdString(ip_port->ip));
    lidar_info_->setMSOP(ip_port->msop_port);
    lidar_info_->setDIFOP(ip_port->difop_port);
    return true;
  }
  return false;
}
std::optional<mech::DifopPacket> LidarManager::getOneDifopPacket(const int _timeout)
{
  MechUdp mech_udp(sizeof(difop_pkt_));
  if (auto difop_packet =
        mech_udp.getOneDifopPacket(lidar_info_->getIP().toStdString(), lidar_info_->getDIFOPPort(), _timeout))
  {
    difop_pkt_ = *difop_packet;
  }
  else
  {
    LOG_ERROR("雷达-{} 获取difop失败 {}:{}, difop: {}", lidar_info_->getLidarIndex(),
              lidar_info_->getIP().toStdString(), lidar_info_->getMSOPPort(), lidar_info_->getDIFOPPort());
    disconnect();
    return {};
  }
  return difop_pkt_;
}
std::optional<mech::MountType> LidarManager::getMountType()
{
  if (!isConnected())
  {
    LOG_INDEX_INFO("雷达未连接，无法获取安装方式");
    return std::nullopt;
  }
  if (!difop_pkt_.isValid())
  {
    LOG_INDEX_ERROR("获取difop无效");
  }
  return difop_pkt_.mount_type;
}

// 特化 mech::ReflBit
template bool LidarManager::writeBit<mech::ReflBit>(const std::string& _file_path,
                                                    uint32_t _addr_start,
                                                    const bool _append_crc);
template bool LidarManager::writeBit<mech::DynamicBit>(const std::string& _file_path,
                                                       uint32_t _addr_start,
                                                       const bool _append_crc);
template bool LidarManager::writeBit<mech::StaticBit>(const std::string& _file_path,
                                                      uint32_t _addr_start,
                                                      const bool _append_crc);
template bool LidarManager::writeBit<mech::TwoDimAbsBit>(const std::string& _file_path,
                                                         uint32_t _addr_start,
                                                         const bool _append_crc);
template bool LidarManager::writeBit<mech::AbsBit>(const std::string& _file_path,
                                                   uint32_t _addr_start,
                                                   const bool _append_crc);
template <typename T>
bool LidarManager::writeBit(const std::string& _file_path, uint32_t _addr_start, const bool _append_crc)
{
  std::ifstream file(_file_path, std::ios::binary);
  if (!file.is_open())
  {
    LOG_ERROR("打开文件失败，文件路径：{}", _file_path);
    return false;
  }

  file.seekg(0, std::ios::end);
  std::streamsize file_size = file.tellg();
  file.seekg(0, std::ios::beg);

  if (file_size != sizeof(T))
  {
    LOG_ERROR("文件大小不正确，文件路径：{}", _file_path);
    return false;
  }

  T data {};
  file.read(reinterpret_cast<char*>(&data), sizeof(T));
  file.close();
  std::vector<uint8_t> buffer(sizeof(T));
  std::memcpy(buffer.data(), &data, sizeof(T));
  if (_append_crc)
  {
    data.toBigEndian();
    auto crc = MechBaseCodec::calBitCrc(reinterpret_cast<const uint8_t*>(data.arr.data()), sizeof(T));
    buffer.push_back(static_cast<uint8_t>(crc & 0xFFU));
    buffer.push_back(static_cast<uint8_t>((crc >> 8U) & 0xFFU));
  }

  return writeTopFlash(buffer, _addr_start);
}

bool LidarManager::writeReflBit(const QString& _file_path, const bool _append_crc)
{
  mech::CombineBit combine_bit {};
  return writeBit<mech::ReflBit>(_file_path.toStdString(), combine_bit.getReflOffset(), _append_crc);
}
bool LidarManager::writeDynamicBit(const QString& _file_path, const bool _append_crc)
{
  mech::CombineBit combine_bit {};
  return writeBit<mech::DynamicBit>(_file_path.toStdString(), combine_bit.getDynamicOffset(), _append_crc);
}
bool LidarManager::writeStaticBit(const QString& _file_path, const bool _append_crc)
{
  mech::CombineBit combine_bit {};
  return writeBit<mech::StaticBit>(_file_path.toStdString(), combine_bit.getStaticOffset(), _append_crc);
}
bool LidarManager::writeTwoDimBit(const QString& _file_path, const bool _append_crc)
{
  mech::CombineBit combine_bit {};
  return writeBit<mech::TwoDimAbsBit>(_file_path.toStdString(), combine_bit.getTwoDimAbsOffset(), _append_crc);
}
bool LidarManager::writeAbsBit(const QString& _file_path, const bool _append_crc)
{
  mech::CombineBit combine_bit {};
  return writeBit<mech::AbsBit>(_file_path.toStdString(), combine_bit.getTwoAbsOffset(), _append_crc);
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool LidarManager::changeLidarNet(const QString& _ip, const int _msop_port, const int _difop_port)
{
  mech::NetPara net_para = getConfigParaCache().net_info;
  net_para.setIpLocal(_ip.toStdString());
  net_para.setMsopPort(_msop_port);
  net_para.setDifopPort(_difop_port);
  net_para.setNetmaskLocal("***********");
  // 0x40.0x2C.0x76.0x8.0x4A.0xCC
  net_para.mac.at(0) = 0x40;
  net_para.mac.at(1) = 0x2c;
  net_para.mac.at(2) = 0x76;
  net_para.mac.at(3) = 0x8;
  if (_ip == "*************")
  {
    net_para.mac.at(4) = 0x4a;
    net_para.mac.at(5) = 0xcc;
  }
  else
  {
    net_para.mac.at(4) = net_para.ip_local.at(2);
    net_para.mac.at(5) = net_para.ip_local.at(3);
  }

  std::vector<uint8_t> net_data(sizeof(mech::NetPara), 0);
  std::memcpy(net_data.data(), &net_para, sizeof(mech::NetPara));
  return writeCmd(mech::NET_CMD_CONFIG_SET_NETWORK, net_data);
}
bool LidarManager::changeLidarNet(const QString& _ip,
                                  const int _msop_port,
                                  const int _difop_port,
                                  const QString& _mes_mac_address)
{
  mech::NetPara net_para = getConfigParaCache().net_info;
  net_para.setIpLocal(_ip.toStdString());
  net_para.setMsopPort(_msop_port);
  net_para.setDifopPort(_difop_port);
  net_para.setMacAddr(_mes_mac_address.toStdString());
  std::vector<uint8_t> net_data(sizeof(mech::NetPara), 0);
  std::memcpy(net_data.data(), &net_para, sizeof(mech::NetPara));
  return writeCmd(mech::NET_CMD_CONFIG_SET_NETWORK, net_data);
}

// bool LidarManager::getConfigParam(mech::ConfigPara& _config_para)
// {
//   if (!readConfigParamater(_config_para) && !readConfigParamater(_config_para))
//   {
//     LOG_INDEX_ERROR("获取雷达参数失败");
//     return false;
//   }

//   lidar_sn_         = QString::fromStdString(_config_para.getSn());
//   pl_version_       = _config_para.getPlVersion();
//   ps_version_       = _config_para.getPsVersion();
//   software_version_ = _config_para.getSoftwareVersion();
//   motor_version_    = _config_para.getMotorVersion();
//   ip_addr_          = QString::fromStdString(_config_para.getIpLocal());
//   msop_port_        = _config_para.getMsopPort();
//   difop_port_       = _config_para.getDifopPort();

//   LOG_INDEX_DEBUG("获取雷达参数成功 sn: {}, pl_version: {:#x}, ps_version: {:#x}, software_version: {:#x}, "
//                   "motor_version: {:#x}, 实时转速: {}rpm",
//                   lidar_sn_, pl_version_, ps_version_, software_version_, motor_version_,
//                   _config_para.getMotorRealTimeSpeed());
//   LOG_INDEX_DEBUG("ip: {}, msop_port: {}, difop_port: {}", ip_addr_, msop_port_, difop_port_);
//   return true;
// }

QString LidarManager::getConfigParamLidarSN() { return QString::fromStdString(getConfigParaCache().getSn()); }

bool LidarManager::isNetNeedChange(bool& _is_need_change,
                                   const QString& _ip,
                                   const int _msop_port,
                                   const int _difop_port)
{
  if (!readConfigParamater() && !readConfigParamater())
  {
    return false;
  }

  auto config_para = getConfigParaCache();
  if (config_para.getIpLocal() != _ip.toStdString() || config_para.getMsopPort() != _msop_port ||
      config_para.getDifopPort() != _difop_port)
  {
    LOG_INDEX_INFO("雷达网络需要修改, 当前网络 [{}:{}, difop:{}] , 目标网络 [{}:{}, difop:{}]",
                   config_para.getIpLocal(), config_para.getMsopPort(), config_para.getDifopPort(), _ip, _msop_port,
                   _difop_port);
    _is_need_change = true;
    return true;
  }

  _is_need_change = false;
  LOG_INDEX_INFO("雷达网络不需要修改, 当前网络 [{}:{}, difop:{}] , 目标网络 [{}:{}, difop:{}]",
                 config_para.getIpLocal(), config_para.getMsopPort(), config_para.getDifopPort(), _ip, _msop_port,
                 _difop_port);
  return true;
}
bool LidarManager::startMonitorDifop()
{
  if (difop_udp_client_ != nullptr)
  {
    difop_udp_client_->stop();
    difop_udp_client_.reset();
    // std::this_thread::sleep_for(std::chrono::milliseconds(5000));
  }
  if (!readConfigParamater())
  {
    return false;
  }
  auto config_para = getConfigParaCache();
  if (config_para.getIpLocal() != getIP())
  {
    LOG_INDEX_ERROR("雷达ip地址不一致，请查看是否修改IP后未进行重启，连接IP[{}:{}], 配置文件ip[{}:{}]", getIP(),
                    getPort(), config_para.getIpLocal(), config_para.getMsopPort());
    return false;
  }

  difop_udp_client_ = std::make_shared<MechUdp>(1248);
  difop_udp_client_->setLogIndex(getLogIndex());
  difop_udp_client_->regRecvCallback([this](const char* _udp_data) {
    std::memcpy(&difop_pkt_, _udp_data, sizeof(mech::DifopPacket));
    if (difop_cb_)
    {
      difop_cb_(difop_pkt_);
    }
  });

  uint32_t ip_addr_uint = 0;
  uint16_t port         = 0;
  std::reverse(config_para.net_info.ip_local.begin(), config_para.net_info.ip_local.end());
  std::memcpy(&ip_addr_uint, config_para.net_info.ip_local.data(), 4);
  std::memcpy(&port, &config_para.net_info.difop_port, 2);

  QHostAddress ip_addr(ip_addr_uint);
  if (ip_addr.isNull())
  {
    LOG_INDEX_ERROR("雷达ip地址不合法，请联系开发者");
    return false;
  }

  if (!difop_udp_client_->start(ip_addr.toString().toStdString(), port))
  {
    LOG_INDEX_ERROR("difop监控失败 [{}:{}]", ip_addr.toString().toStdString(), port);
    return false;
  }

  LOG_INDEX_INFO("开始接收 difop数据 [{}:{}]", ip_addr.toString().toStdString(), port);
  return true;
}

bool LidarManager::stopMonitorDifop()
{
  if (difop_udp_client_ != nullptr)
  {
    difop_udp_client_->stop();
  }
  return true;
}

bool LidarManager::firmwareUpdate(const uint16_t& _cmd_type, const QString& _file_path)
{
  QFile file(_file_path);
  int timeout = 30000;
  if (!file.open(QIODevice::ReadOnly))
  {
    LOG_INDEX_ERROR("打开App文件失败，err_msg: {}，文件路径：{}", file.errorString(), _file_path.toStdString());
    return false;
  }

  uint32_t check_sum = 0;

  uint32_t data_size = file.size();
  uint32_t pkt_count = 1;
  uint32_t pkt_size  = 1024;
  // uint32_t pkt_num   = data_size / pkt_size + (data_size % pkt_size == 0 ? 0 : 1);

  std::vector<uint8_t> data;
  MechBaseCodec::pushToPayload(data, static_cast<uint8_t>(0xc1), data_size);
  if (!writeCmd(_cmd_type, data, timeout))
  {
    LOG_INDEX_ERROR("发送开始升级命令失败");
    return false;
  }

  QByteArray raw_data = file.readAll();
  uint32_t offset     = 0;

  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_INFO);
  while (offset < static_cast<uint32_t>(raw_data.size()) && !is_abort_)
  {
    std::vector<uint8_t> data;
    MechBaseCodec::pushToPayload(data, static_cast<uint8_t>(0xc2), pkt_count++);
    uint32_t chunk_size = std::min(pkt_size, static_cast<uint32_t>(raw_data.size() - offset));
    data.insert(data.end(), raw_data.begin() + offset, raw_data.begin() + offset + chunk_size);
    offset += chunk_size;

    if (!writeCmd(_cmd_type, data))
    {
      LOG_INDEX_ERROR("发送升级数据失败");
      return false;
    }
  }

  for (char item : raw_data)
  {
    check_sum += static_cast<uint8_t>(item);
  }
  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_DEBUG);
  data.clear();
  MechBaseCodec::pushToPayload(data, static_cast<uint8_t>(0xc3), check_sum);
  if (!writeCmd(_cmd_type, data, timeout))
  {
    LOG_INDEX_ERROR("发送结束升级命令失败", _cmd_type);
    return false;
  }

  return true;
}

bool LidarManager::firmwareUpdateApp(const QString& _file_path)
{
  if (!firmwareUpdate(mech::NET_CMD_FIRMWARE_UPDATE_APP, _file_path))
  {
    LOG_INDEX_ERROR("App升级失败, {}", _file_path);
    return false;
  }
  return true;
}
bool LidarManager::firmwareUpdateBot(const QString& _file_path)
{
  if (!firmwareUpdate(mech::NET_CMD_FIRMWARE_UPDATE_BOT, _file_path))
  {
    LOG_INDEX_ERROR("Bot升级失败, {}", _file_path);
    return false;
  }
  LOG_INDEX_INFO("Bot升级成功, {}", _file_path);
  return true;
}
bool LidarManager::firmwareUpdateTop(const QString& _file_path)
{
  if (!firmwareUpdate(mech::NET_CMD_FIRMWARE_UPDATE_TOP, _file_path))
  {
    LOG_INDEX_ERROR("Top升级失败, {}", _file_path);
    return false;
  }
  LOG_INDEX_INFO("Top升级成功, {}", _file_path);
  return true;
}
bool LidarManager::firmwareUpdateWriteConfig(const QString& _file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
  {
    LOG_INDEX_ERROR("打开固化Config文件失败，err_msg: {}，文件路径：{}", file.errorString(), _file_path.toStdString());
    return false;
  }
  uint32_t time_out = 100000;

  std::vector<uint32_t> bot_reg_addr;
  std::vector<uint32_t> bot_reg_val;
  std::vector<uint32_t> top_reg_addr;
  std::vector<uint32_t> top_reg_val;
  std::vector<uint32_t> reg_459_addr;
  std::vector<uint32_t> reg_459_val;
  QTextStream inp(&file);
  QStringList lines;
  while (!inp.atEnd())
  {
    auto line = inp.readLine();
    if (!line.contains("bottom_regs=") && !line.contains("top_regs=") && !line.contains("459_regs="))
    {
      continue;
    }
    auto reg_info = line.split("=");
    if (reg_info.size() != 2)
    {
      LOG_INDEX_ERROR("解析{}失败", line.toStdString());
      return false;
    }
    auto reg_num = reg_info[1].toInt();
    auto& reg_addr =
      line.contains("bottom_regs=") ? bot_reg_addr : (line.contains("top_regs=") ? top_reg_addr : reg_459_addr);
    auto& reg_val =
      line.contains("bottom_regs=") ? bot_reg_val : (line.contains("top_regs=") ? top_reg_val : reg_459_val);
    auto split_size     = 4;
    auto reg_addr_index = 2;
    auto reg_val_index  = 3;
    if (reg_info.at(0) == "459_regs")
    {
      split_size     = 3;
      reg_addr_index = 1;
      reg_val_index  = 2;
    }

    while ((reg_num--) > 0 && !inp.atEnd())
    {
      auto reg_line = inp.readLine();
      auto reg_data = reg_line.split("_");
      if (reg_data.size() != split_size)
      {
        LOG_INDEX_ERROR("解析{}数据失败", line.toStdString());
        return false;
      }
      reg_addr.push_back(reg_data[reg_addr_index].toUInt(nullptr, 16));
      reg_val.push_back(reg_data[reg_val_index].toUInt(nullptr, 16));
    }
  }

  uint16_t pkt_count = 0;
  auto size          = bot_reg_val.size();
  for (size_t i = 0; (i < size) || (size == 0);)
  {
    std::vector<uint8_t> data;
    uint16_t count = (size - i) > 100 ? 100 : (size - i);
    MechBaseCodec::pushToPayload(data, static_cast<uint16_t>(size), pkt_count++, static_cast<uint16_t>(count));
    for (; i < size && count > 0; i++, count--)
    {
      MechBaseCodec::pushToPayload(data, bot_reg_addr[i], bot_reg_val[i]);
    }
    if (!writeCmd(mech::NET_CMD_OTHER_FIRM_BOT_WRITE, data, time_out))
    {
      LOG_INDEX_ERROR("写入固化Config数据失败");
      return false;
    }
    if (size == 0)
    {
      break;
    }
  }

  pkt_count = 0;
  size      = top_reg_val.size();
  for (size_t i = 0; (i < size) || (size == 0);)
  {
    std::vector<uint8_t> data;
    uint16_t count = (size > 0 && (size - i) > 100) ? 100 : (size - i);

    MechBaseCodec::pushToPayload(data, static_cast<uint16_t>(size), pkt_count++, count);

    for (; i < size && count > 0; i++, count--)
    {
      MechBaseCodec::pushToPayload(data, top_reg_addr[i], top_reg_val[i]);
    }

    if (!writeCmd(mech::NET_CMD_OTHER_FIRM_TOP_WRITE, data, time_out))
    {
      LOG_INDEX_ERROR("写入固化顶板Config数据失败");
      return false;
    }

    if (size == 0)
    {
      break;
    }
  }

  pkt_count = 0;
  size      = reg_459_val.size();
  for (size_t i = 0; (i < size) || (size == 0);)
  {
    std::vector<uint8_t> data;
    uint16_t count = (size - i) > 100 ? 100 : (size - i);

    MechBaseCodec::pushToPayload(data, static_cast<uint16_t>(size), pkt_count++, count);
    for (; i < size && count > 0; i++, count--)
    {
      MechBaseCodec::pushToPayload(data, reg_459_addr[i], reg_459_val[i]);
    }
    if (!writeCmd(mech::NET_CMD_OTHER_FIRM_459_WRITE, data, time_out))
    {
      LOG_INDEX_ERROR("写入固化459Config数据失败");
      return false;
    }
    if (size == 0)
    {
      break;
    }
  }

  LOG_INDEX_INFO("固化配置成功, 文件路径: {}", _file_path.toStdString());
  return true;
}

bool LidarManager::firmwareUpdateCheckConfig(const QString& _file_path, const QString& _read_val_file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
  {
    LOG_INDEX_ERROR("打开固化Config文件失败，err_msg: {}，文件路径：{}", file.errorString(), _file_path.toStdString());
    return false;
  }
  // uint32_t time_out = 100000;

  std::vector<uint32_t> bot_reg_addr;
  std::vector<uint32_t> bot_reg_val;
  std::vector<uint32_t> top_reg_addr;
  std::vector<uint32_t> top_reg_val;
  std::vector<uint32_t> reg_459_addr;
  std::vector<uint32_t> reg_459_val;
  QTextStream inp(&file);
  QStringList lines;
  while (!inp.atEnd())
  {
    auto line = inp.readLine();
    if (!line.contains("bottom_regs=") && !line.contains("top_regs=") && !line.contains("459_regs="))
    {
      continue;
    }
    auto reg_info = line.split("=");
    if (reg_info.size() != 2)
    {
      LOG_INDEX_ERROR("解析{}失败", line.toStdString());
      return false;
    }
    auto reg_num = reg_info[1].toInt();
    auto& reg_addr =
      line.contains("bottom_regs=") ? bot_reg_addr : (line.contains("top_regs=") ? top_reg_addr : reg_459_addr);
    auto& reg_val =
      line.contains("bottom_regs=") ? bot_reg_val : (line.contains("top_regs=") ? top_reg_val : reg_459_val);
    auto split_size     = 4;
    auto reg_addr_index = 2;
    auto reg_val_index  = 3;
    if (reg_info.at(0) == "459_regs")
    {
      split_size     = 3;
      reg_addr_index = 1;
      reg_val_index  = 2;
    }

    while ((reg_num--) > 0 && !inp.atEnd())
    {
      auto reg_line = inp.readLine();
      auto reg_data = reg_line.split("_");
      if (reg_data.size() != split_size)
      {
        LOG_INDEX_ERROR("解析{}数据失败", line.toStdString());
        return false;
      }
      reg_addr.push_back(reg_data[reg_addr_index].toUInt(nullptr, 16));
      reg_val.push_back(reg_data[reg_val_index].toUInt(nullptr, 16));
    }
  }
  // 先读取底板后顶板最后459
  std::vector<uint32_t> bot_reg_addr_read;
  std::vector<uint32_t> bot_reg_val_read;
  std::vector<uint32_t> top_reg_addr_read;
  std::vector<uint32_t> top_reg_val_read;
  std::vector<uint32_t> reg_459_addr_read;
  std::vector<uint32_t> reg_459_val_read;

  if (!readConfigRegister(mech::NET_CMD_OTHER_FIRM_BOT_READ, bot_reg_addr_read, bot_reg_val_read))
  {
    LOG_INDEX_ERROR("读取底板配置失败");
    return false;
  }

  if (!readConfigRegister(mech::NET_CMD_OTHER_FIRM_TOP_READ, top_reg_addr_read, top_reg_val_read))
  {
    LOG_INDEX_ERROR("读取顶板配置失败");
    return false;
  }

  if (!readConfigRegister(mech::NET_CMD_OTHER_FIRM_459_READ, reg_459_addr_read, reg_459_val_read))
  {
    LOG_INDEX_ERROR("读取459配置失败");
    return false;
  }

  // 根据格式，写入到文件当中
  QFile read_val_file(_read_val_file_path);
  if (!read_val_file.open(QIODevice::WriteOnly | QIODevice::Text))
  {
    LOG_INDEX_ERROR("打开文件失败");
    return false;
  }

  QTextStream out(&read_val_file);
  out << "bottom_regs=" << bot_reg_addr_read.size() << "\n";
  for (size_t i = 0; i < bot_reg_addr_read.size(); ++i)
  {
    out << fmt::format("R_{:#x}_{:#x}\n", bot_reg_addr_read[i], bot_reg_val_read[i]).c_str();
  }
  out << "\n";
  out << "top_regs=" << top_reg_addr_read.size() << "\n";
  for (size_t i = 0; i < top_reg_addr_read.size(); ++i)
  {
    out << fmt::format("R_{:#x}_{:#x}\n", top_reg_addr_read[i], top_reg_val_read[i]).c_str();
  }
  out << "\n";
  out << "459_regs=" << reg_459_addr_read.size() << "\n";
  for (size_t i = 0; i < reg_459_addr_read.size(); ++i)
  {
    out << fmt::format("R_{:#x}_{:#x}\n", reg_459_addr_read[i], reg_459_val_read[i]).c_str();
  }
  read_val_file.close();

  if (bot_reg_addr_read.size() != bot_reg_addr.size() || top_reg_addr_read.size() != top_reg_addr.size() ||
      reg_459_addr_read.size() != reg_459_addr.size())
  {
    LOG_INDEX_ERROR("读取配置失败，配置长度不一致, bot[write:{} read:{}], top[write:{} read:{}], 459[write:{} read:{}]",
                    bot_reg_addr.size(), bot_reg_addr_read.size(), top_reg_addr.size(), top_reg_addr_read.size(),
                    reg_459_addr.size(), reg_459_addr_read.size());
    return false;
  }

  // 比较寄存器数值并记录到 verification 文件
  bool overall_verification_success = true;

  // 检查底板寄存器
  for (size_t i = 0; i < bot_reg_addr.size(); ++i)
  {
    bool is_match    = (bot_reg_val[i] == bot_reg_val_read[i]);
    QString reg_name = QString("bottom_reg_0x%1").arg(bot_reg_addr[i], 0, 16);

    // 记录校验结果到 verification 文件
    writeVerificationDataToFile(reg_name, bot_reg_addr[i], bot_reg_val[i], bot_reg_val_read[i], is_match);

    if (!is_match)
    {
      LOG_INDEX_ERROR("底板寄存器0x{:x}值不相等，文件中的值为:0x{:x}, 机器中的值为:0x{:x}", bot_reg_addr[i],
                      bot_reg_val[i], bot_reg_val_read[i]);
      overall_verification_success = false;
    }
  }

  // 检查顶板寄存器
  for (size_t i = 0; i < top_reg_addr.size(); ++i)
  {
    bool is_match    = (top_reg_val[i] == top_reg_val_read[i]);
    QString reg_name = QString("top_reg_0x%1").arg(top_reg_addr[i], 0, 16);

    // 记录校验结果到 verification 文件
    writeVerificationDataToFile(reg_name, top_reg_addr[i], top_reg_val[i], top_reg_val_read[i], is_match);

    if (!is_match)
    {
      LOG_INDEX_ERROR("顶板寄存器0x{:x}值不相等，文件中的值为:0x{:x}, 机器中的值为:0x{:x}", top_reg_addr[i],
                      top_reg_val[i], top_reg_val_read[i]);
      overall_verification_success = false;
    }
  }

  // 检查459寄存器
  for (size_t i = 0; i < reg_459_addr.size(); ++i)
  {
    bool is_match    = (reg_459_val[i] == reg_459_val_read[i]);
    QString reg_name = QString("reg_459_0x%1").arg(reg_459_addr[i], 0, 16);

    // 记录校验结果到 verification 文件
    writeVerificationDataToFile(reg_name, reg_459_addr[i], reg_459_val[i], reg_459_val_read[i], is_match);

    if (!is_match)
    {
      LOG_INDEX_ERROR("459寄存器0x{:x}值不相等，文件中的值为:0x{:x}, 机器中的值为:0x{:x}", reg_459_addr[i],
                      reg_459_val[i], reg_459_val_read[i]);
      overall_verification_success = false;
    }
  }

  // 如果有校验失败的情况，返回 false
  if (!overall_verification_success)
  {
    LOG_INDEX_ERROR("固件配置校验失败，存在寄存器值不匹配的情况");
    return false;
  }

  LOG_INDEX_INFO("配置文件检验OK, 机器中的配置值与文件中的配置相等");
  return true;
}

bool LidarManager::startEncodCalib(const int _timeout_secs)
{
  LOG_INDEX_INFO("码盘标定状态位: {:x}", getConfigParaCache().status_of_code_wheel_cali);
  uint8_t start_encode = 0;
  if (!writeCmd(mech::NET_CMD_MOTOR_CALIBRATION, start_encode, _timeout_secs * 1000))
  {
    LOG_INDEX_ERROR("等待电机稳定失败 {}s", _timeout_secs);
    if (readConfigParamater())
    {
      LOG_INDEX_ERROR("当前转速: {}rpm", getConfigParaCache().getMotorRealTimeSpeed());
    }
    return false;
  }
  if (!readConfigParamater())
  {
    LOG_INDEX_ERROR("电机稳定后读取状态失败");
    return false;
  }

  auto read_value = getConfigParaCache().status_of_code_wheel_cali;
  if (!writeVerificationDataToFile("start_encode", mech::NET_CMD_MOTOR_CALIBRATION, static_cast<uint32_t>(start_encode),
                                   static_cast<uint32_t>(read_value), start_encode == read_value))
  {
    LOG_INDEX_ERROR("启动码盘标定时候，校验失败，写入{}与回读{}值不一致", start_encode, read_value);
    return false;
  }

  return true;
}

bool LidarManager::writeTopFlashWithVer(const QString& _name,
                                        const std::vector<uint8_t>& _data,
                                        const uint32_t _addr_start)
{
  if (_data.empty())
  {
    return false;
  }
  if (!writeTopFlash(_data, _addr_start))
  {
    LOG_INDEX_ERROR("写入top flash失败");
    return false;
  }

  std::vector<uint8_t> read_data_vec;
  if (!readTopFlash(read_data_vec, _addr_start, _data.size()))
  {
    LOG_INDEX_ERROR("回读TopFlash失败");
    return false;
  }

  auto write_data_checksum = AiryCrc32Codec::checkSum32(_data.data(), _data.size());
  auto read_data_checksum  = AiryCrc32Codec::checkSum32(read_data_vec.data(), read_data_vec.size());

  writeVerificationDataToFile(_name, mech::CustomGdiFeild::getStartAddr(), write_data_checksum, read_data_checksum,
                              write_data_checksum == read_data_checksum);

  if (write_data_checksum != read_data_checksum)
  {
    LOG_INDEX_ERROR("{}回读校验失败，写入校验和: {:#x}, 回读校验和: {:#x}", _name.toStdString(), write_data_checksum,
                    read_data_checksum);
    return false;
  }

  return true;
}

bool LidarManager::stopEncodCalib()
{
  uint8_t write_value = 1;
  if (!writeCmd(mech::NET_CMD_MOTOR_CALIBRATION, write_value))
  {
    LOG_INDEX_ERROR("停止保存码盘标定失败");
    return false;
  }
  if (!readConfigParamater())
  {
    LOG_INDEX_ERROR("码盘标定完成后读取状态失败");
    return false;
  }

  auto read_value = getConfigParaCache().status_of_code_wheel_cali;
  if (!writeVerificationDataToFile("save_encode", mech::NET_CMD_MOTOR_CALIBRATION, static_cast<uint32_t>(write_value),
                                   static_cast<uint32_t>(read_value), write_value == read_value))
  {
    LOG_INDEX_ERROR("保存码盘标定时候，校验失败，写入{}与回读{}值不一致", write_value, read_value);
    return false;
  }

  return true;
}
bool LidarManager::sendScaleAvgToMotor(const std::vector<uint32_t>& _scale_avg)
{
  uint8_t size_high            = static_cast<uint8_t>((_scale_avg.size() * 4) >> 8U);
  uint8_t size_low             = static_cast<uint8_t>((_scale_avg.size() * 4) & static_cast<uint16_t>(0xff));
  std::vector<uint8_t> payload = { 0xff, 0xaa, 0x2b, size_high, size_low };
  uint32_t crc                 = 0;

  for (const auto& val : _scale_avg)
  {
    payload.push_back(static_cast<uint8_t>(val >> 24U));
    payload.push_back(static_cast<uint8_t>(val >> 16U));
    payload.push_back(static_cast<uint8_t>(val >> 8U));
    payload.push_back(static_cast<uint8_t>(val));
  }

  for (size_t i = 2; i < payload.size(); ++i)
  {
    crc += payload[i];
  }

  crc &= 0xffffU;
  payload.push_back(static_cast<uint8_t>((crc & 0xffU) >> 8U));
  payload.push_back(static_cast<uint8_t>(crc & 0xffU));

  payload.push_back(0xaa);
  payload.push_back(0xff);

  if (!writeCmd(mech::NET_CMD_MOTOR_SEND_CMD, payload))
  {
    LOG_INDEX_ERROR("发送码盘标定数据失败");
    return false;
  }

  return true;
};

bool LidarManager::getEncodCalibData(std::vector<uint32_t>& _coeff_vec, std::vector<uint32_t>& _insert_step)
{
  if (!readConfigRegister(mech::NET_CMD_OTHER_ENCOD_CALIB_READ, _insert_step, _coeff_vec))
  {
    LOG_INDEX_ERROR("读取码盘标定数据失败");
    return false;
  }
  return true;
}

bool LidarManager::startTcpdumpBothOrgAndObjIPExcludeMSOP(const QString& _dump_file_path)
{
  auto filter      = TcpdumpUtils::Filter();
  filter.file_size = "20";  // 限制抓包20MB
  filter.custom_filter =
    fmt::format("(not udp port {} and not udp port {} and not udp port 6688 and not icmp) and (host {} or host {})",
                getPort(), getLidarInfo()->getMSOPPort(), getIP(), getLidarInfo()->getIP());
  filter.network_interface = TcpdumpUtils::getNetworkInterFaceByIP("*************");
  return tcpdump_exclude_msop_.startTcpdump(_dump_file_path.toStdString(), filter);
}
bool LidarManager::startTcpdumpExcludeMSOP(const QString& _dump_file_path)
{
  auto filter              = TcpdumpUtils::Filter();
  filter.file_size         = "20";  // 限制抓包20MB
  filter.custom_filter     = fmt::format("not (udp port {} or udp port 6688 or icmp)", getPort());
  filter.host_ip           = getIP();
  filter.network_interface = TcpdumpUtils::getNetworkInterFaceByIP("*************");
  return tcpdump_exclude_msop_.startTcpdump(_dump_file_path.toStdString(), filter);
}
void LidarManager::stopTcpdumpExcludeMSOP() { tcpdump_exclude_msop_.stopTcpdump(); }

bool LidarManager::startTcpdumpOnlyMSOP(const QString& _dump_file_path)
{
  auto filter              = TcpdumpUtils::Filter();
  filter.file_size         = "20";  // 限制抓包10MB
  filter.custom_filter     = fmt::format("udp port {} or udp port {}", getPort(), getConfigParaCache().getDifopPort());
  filter.host_ip           = getIP();
  filter.network_interface = TcpdumpUtils::getNetworkInterFaceByIP("*************");
  return tcpdump_only_msop_.startTcpdump(_dump_file_path.toStdString(), filter);
}
void LidarManager::stopTcpdumpOnlyMSOP() { tcpdump_only_msop_.stopTcpdump(); }

std::optional<LidarManager::MotorStatus> LidarManager::readMotorStatus(const int _timeout_ms)
{
  std::vector<uint8_t> data = { mech::READ_MOTOR_STATUS };
  if (!readCmd(mech::NET_CMD_MOTOR_SEND_CMD, data, _timeout_ms))
  {
    LOG_INDEX_ERROR("获取电机状态失败");
    return {};
  }

  if (data.size() < sizeof(MotorStatus))
  {
    LOG_INDEX_ERROR("接收到的字节大小出错, 期望至少大于: {} , 接收到的数据大小: {}", sizeof(MotorStatus), data.size());
    return {};
  }

  MotorStatus motor_status {};
  size_t index = 0;
  MechBaseCodec::copyFromPayload(data, motor_status, index);
  motor_status.toBigEndian();

  return motor_status;
}

bool LidarManager::writeRegDataWithVer(uint32_t _address, uint32_t _value, const QString& _reg_name)
{
  // 生成寄存器名称（如果未提供）
  QString reg_name = _reg_name.isEmpty() ? QString("reg_0x%1").arg(_address, 0, 16) : _reg_name;

  // 写入寄存器
  if (!MechCommunication::writeRegData(_address, _value))
  {
    LOG_INDEX_ERROR("[{}:0x{:x}]写入[0x{:x}]失败", reg_name.toStdString(), _address, _value);

    // 记录写入失败的情况
    writeVerificationDataToFile(reg_name, _address, _value, 0, false);
    return false;
  }

  LOG_INDEX_INFO("[{}:0x{:x}]写入[0x{:x}]成功", reg_name.toStdString(), _address, _value);

  // 进行回读校验（仅当CSV校验文件路径不为空时）
  if (!csv_verification_file_path_.isEmpty())
  {
    // 等待一小段时间确保写入完成
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    uint32_t read_back_value  = 0;
    bool verification_success = false;

    // 回读寄存器数据
    if (MechCommunication::readRegData(_address, read_back_value))
    {
      verification_success = (read_back_value == _value);
      if (verification_success)
      {
        LOG_INDEX_INFO("[{}:0x{:x}]回读校验成功, 写入值:0x{:x}, 读取值:0x{:x}", reg_name.toStdString(), _address,
                       _value, read_back_value);
      }
      else
      {
        LOG_INDEX_ERROR("[{}:0x{:x}]回读校验失败, 写入值:0x{:x}, 读取值:0x{:x}", reg_name.toStdString(), _address,
                        _value, read_back_value);
      }
    }
    else
    {
      LOG_INDEX_ERROR("[{}:0x{:x}]回读失败", reg_name.toStdString(), _address);
      verification_success = false;
    }

    // 记录校验结果到文件
    writeVerificationDataToFile(reg_name, _address, _value, read_back_value, verification_success);

    // 如果回读校验失败，返回 false
    if (!verification_success)
    {
      return false;
    }
  }

  return true;
}

bool LidarManager::writeRegDataWithVer(uint32_t _reg_start_addr,
                                       const std::vector<uint32_t>& _reg_val,
                                       const QString& _reg_name_prefix)
{
  if (_reg_val.empty())
  {
    LOG_INDEX_ERROR("寄存器值向量不能为空");
    return false;
  }

  // 生成寄存器名称前缀（如果未提供）
  QString reg_name_prefix =
    _reg_name_prefix.isEmpty() ? QString("con_reg_0x%1").arg(_reg_start_addr, 0, 16) : _reg_name_prefix;

  // 写入连续寄存器
  if (!MechCommunication::writeRegData(_reg_start_addr, _reg_val))
  {
    LOG_INDEX_ERROR("[{}:0x{:x}]连续写入{}个寄存器失败", reg_name_prefix.toStdString(), _reg_start_addr,
                    _reg_val.size());

    // 记录写入失败的情况（只记录起始地址）
    writeVerificationDataToFile(reg_name_prefix, _reg_start_addr, _reg_val.empty() ? 0 : _reg_val[0], 0, false);
    return false;
  }

  LOG_INDEX_INFO("[{}:0x{:x}]连续写入{}个寄存器成功", reg_name_prefix.toStdString(), _reg_start_addr, _reg_val.size());

  // 进行回读校验（仅当CSV校验文件路径不为空时）
  if (!csv_verification_file_path_.isEmpty())
  {
    // 等待一小段时间确保写入完成
    std::this_thread::sleep_for(std::chrono::milliseconds(20));

    std::vector<uint32_t> read_back_values;
    bool overall_verification_success = true;

    // 回读连续寄存器数据
    if (MechCommunication::readRegData(_reg_start_addr, static_cast<uint32_t>(_reg_val.size()), read_back_values))
    {
      // 逐个比较每个寄存器的值
      for (size_t i = 0; i < _reg_val.size(); ++i)
      {
        uint32_t current_addr = _reg_start_addr + static_cast<uint32_t>(i);
        uint32_t write_value  = _reg_val[i];
        uint32_t read_value   = (i < read_back_values.size()) ? read_back_values[i] : 0;
        bool is_match         = (write_value == read_value);

        // 生成每个寄存器的名称
        QString individual_reg_name = QString("%1[%2]").arg(reg_name_prefix).arg(i);

        // 记录每个寄存器的校验结果
        writeVerificationDataToFile(individual_reg_name, current_addr, write_value, read_value, is_match);

        if (is_match)
        {
          LOG_INDEX_INFO("[{}:0x{:x}]回读校验成功, 写入值:0x{:x}, 读取值:0x{:x}", individual_reg_name.toStdString(),
                         current_addr, write_value, read_value);
        }
        else
        {
          LOG_INDEX_ERROR("[{}:0x{:x}]回读校验失败, 写入值:0x{:x}, 读取值:0x{:x}", individual_reg_name.toStdString(),
                          current_addr, write_value, read_value);
          overall_verification_success = false;
        }
      }
    }
    else
    {
      LOG_INDEX_ERROR("[{}:0x{:x}]连续回读{}个寄存器失败", reg_name_prefix.toStdString(), _reg_start_addr,
                      _reg_val.size());

      // 记录所有寄存器的回读失败
      for (size_t i = 0; i < _reg_val.size(); ++i)
      {
        uint32_t current_addr       = _reg_start_addr + static_cast<uint32_t>(i);
        QString individual_reg_name = QString("%1[%2]").arg(reg_name_prefix).arg(i);
        writeVerificationDataToFile(individual_reg_name, current_addr, _reg_val[i], 0, false);
      }

      overall_verification_success = false;
    }

    // 如果回读校验失败，返回 false
    if (!overall_verification_success)
    {
      return false;
    }
  }

  return true;
}

bool LidarManager::writeVerificationDataToFile(const QString& _reg_name,
                                               uint32_t _address,
                                               uint32_t _write_value,
                                               uint32_t _read_value,
                                               bool _is_match)
{
  // 如果CSV校验文件路径为空，则不进行记录
  if (csv_verification_file_path_.isEmpty())
  {
    return true;
  }

  QFile verification_file(csv_verification_file_path_);
  if (!verification_file.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text))
  {
    LOG_INDEX_ERROR("无法打开CSV校验文件: {}", csv_verification_file_path_.toStdString());
    return false;
  }

  QTextStream stream(&verification_file);

  // 如果文件为空，写入表头
  if (verification_file.size() == 0)
  {
    stream << QString("时间戳,名称,地址,写入值,读取值,匹配状态\n");
  }

  // 使用 fmt 格式化数据记录
  QString timestamp        = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
  std::string match_status = _is_match ? "PASS" : "FAIL";

  std::string csv_line = fmt::format("{},{},{:#x},{:#x},{:#x},{}\n", timestamp.toStdString(), _reg_name.toStdString(),
                                     _address, _write_value, _read_value, match_status);

  stream << QString::fromStdString(csv_line);
  verification_file.close();

  // LOG_INDEX_INFO("CSV校验记录: {} [0x{:x}] 写入:0x{:x} 读取:0x{:x} 状态:{}", _reg_name.toStdString(), _address,
  //                _write_value, _read_value, match_status);

  return true;
}

bool LidarManager::writeVerificationDataToFile(const QString& _reg_name,
                                               uint32_t _address,
                                               float _write_value,
                                               float _read_value,
                                               bool _is_match)
{
  // 如果CSV校验文件路径为空，则不进行记录
  if (csv_verification_file_path_.isEmpty())
  {
    return true;
  }

  QFile verification_file(csv_verification_file_path_);
  if (!verification_file.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text))
  {
    LOG_INDEX_ERROR("无法打开CSV校验文件: {}", csv_verification_file_path_.toStdString());
    return false;
  }

  QTextStream stream(&verification_file);

  // 如果文件为空，写入表头
  if (verification_file.size() == 0)
  {
    stream << QString("时间戳,名称,地址,写入值,读取值,匹配状态\n");
  }

  // 使用 fmt 格式化数据记录
  QString timestamp        = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
  std::string match_status = _is_match ? "PASS" : "FAIL";

  // 使用 fmt::format 构建 CSV 行，float 值保留6位小数
  std::string csv_line = fmt::format("{},{},0x{:x},{:.6f},{:.6f},{}\n", timestamp.toStdString(),
                                     _reg_name.toStdString(), _address, _write_value, _read_value, match_status);

  stream << QString::fromStdString(csv_line);
  verification_file.close();

  // LOG_INDEX_INFO("CSV校验记录: {} [0x{:x}] 写入:{:.6f} 读取:{:.6f} 状态:{}", _reg_name.toStdString(), _address,
  //                _write_value, _read_value, match_status);

  return true;
}

}  // namespace lidar
}  // namespace robosense