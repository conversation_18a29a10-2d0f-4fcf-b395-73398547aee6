﻿# RSFSCLog使用方法

**RSFSCLog -> RoboSense Factory Software Common Log**基于spdlog实现，其将spdlog源码封装成了一个.h和一个.so文件，提供trace、debug、info、warn、error 5种level的log。

RSFSCLog以单例的形式存在，所有的库（目前添加了RSFSCLog的库有MEMSTCP、RSFSCLib），以及你的程序的log日志都将统一输出到同一个地方。

只要实例化了这个单例（第一次调用），就会启动崩溃监测功能，崩溃监测时将会在`~/.RoboSense/crash_log`目录下生成崩溃调用堆栈记录

## 1 调用方式

### 1.1 源码库

对于提供源码的库，将`rsfsc_log.h`拷贝到你的工程，然后将头文件以下的宏注释掉：

```cpp
#ifndef RSFSCLOG_UES_SPDLOG
#  define RSFSCLOG_UES_SPDLOG
#  ifndef RSFSCLOG_USE_QT
#    define RSFSCLOG_USE_QT
#  endif  // RSFSCLOG_USE_QT
#endif    // RSFSCLOG_UES_SPDLOG
```

在需要进行log输出的`.cpp`文件中include头文件：

```cpp
#if __has_include("rsfsc_lib/rsfsc_log.h")
#  include "rsfsc_lib/rsfsc_log.h"
#elif __has_include("rsfsc_log/rsfsc_log.h")
#  include "rsfsc_log/rsfsc_log.h"
#else
#  include "rsfsc_log.h"
#endif
```

因增加了一层```include```目录，所以上述写法应该更改为

```cpp
#if __has_include("rsfsc_lib/include/rsfsc_log.h")
#  include "rsfsc_lib/include/rsfsc_log.h"
#elif __has_include("rsfsc_log/rsfsc_log.h")
#  include "rsfsc_log/rsfsc_log.h"
#else
#  include "rsfsc_log.h"
#endif
```

此时，RSFSCLog会完全退化为`std::cout`和`std::cerr`，你可以在不包含RSFSCLog的情况下测试和提供你的代码；当一个使用了RSFSCLog的用户想用你的源码库时，又能直接生效

### 1.2 动态库

当你的动态库想要使用RSFSCLog时，我们建议你启用spdlog的功能，也即非退化的使用。此时你就像使用一个普通的动态库一样使用RSFSCLog即可：

1. include头文件：`#include "rsfsc_log/rsfsc_log.h"`
2. 在CmakeLists.txt里链接对应的`librsfsc_log.so`即可

### 1.3 通过rsfsc_lib调用

RSFSCLog同样集成在RSFSCLib中，此时，你只需要：

1. 在CMakeLists.txt里修改一下，将include的目录放到rsfsc_lib的上一层
2. 在需要使用的.cpp文件中`#include "rsfsc_lib/rsfsc_log.h"`
3. link的时候除了`librsfsc_lib.so`再link一下`librsfsc_log.so`即可
4. 其他与rsfsc_lib原有使用方式一致
5. 需要注意的是，旧版本的rsfsc_lib是推荐把CMakeLists.txt中的的include目录直接写到rsfsc_lib的，这种方式已经不推荐了，需要稍作修改，原有在.cpp中的include就需要加上rsfsc_lib了

**这种模式下有个特别的好处，就是可以直接通过MES设置界面设置终端、文件、Qt界面的输出level**

## 2 保存路径

**由于C++无法保证某段程序必然运行在整个程序的最前面**：main函数前面有全局变量的构造函数，而每个文件的全局构造函数的执行顺序是无法保证的。所以单例情况下，也就无法保证众多使用了这个单例的库哪个先执行，可能是MEMSTCP、可能是RSFSCLib，也可能是你的程序，所以无法给RSFSCLog传一个初始化的文件保存路径。

默认保存路径：`~/.RoboSense/normal_log`

当你的程序启动起来之后，可以通过`bool changeFileSavePath(const std::string& _file_path);`、`bool resetFileSavePath();`函数去修改保存的路径，但是需要注意的是，由于多线程情况下实现切换文件句柄，为了保证安全，在切换后约100ms内，所有的log都会被丢弃。

## 3 log level

一共提供了trace、debug、info、warn、error 5种level的log。我们提倡：

+ **trace：** 只做代码级别的调试用，也就是说开发者以外的人不应该看到这个log。一般情况下，代码调试完毕，也就保证了代码肯定不会出错，我们建议您直接删除相关语句
+ **debug：** 提供给高级用户，在出现BUG时进行debug用的信息。对普通用户，也不应该显示这些消息
+ **info：** 普通用户接触到的第一个level的log信息，用于记录关键流程和关键结果，方便出现异常时进行初步排查定位
+ **warn：** 警告用户，如果不注意可能会出现危险、影响软件正常功能的信息
+ **error：** 警告用户，已经出现了严重的，影响到软件正常功能，或者软件结果出现意想不到的异常信息

**一切的终端输出、界面输出、文件输出，对计算机而言都是非常低效的。所以应该怜惜每一句log，非必要不log**

可以通过调用下面的函数来调整输出的最小level：

```cpp
void setFileLogLevel(const LogLevel);
void setTerminalLogLevel(const LogLevel);
void setQMessageBrowserLogLevel(const LogLevel);
```

## 4 使用办法

### 4.1 log用法

spdlog是模板类，而且都是可变参数长度的函数，他可以支持任意个数的参数。但是封装成.so以后，需要提前实例化，目前仅实例化了一小部分常用的，如有需要其他的用法，请告知RSFSCLog的维护者：

```cpp
template void RSFSCLog::info<>(const std::string& fmt);
template void RSFSCLog::info<int>(const std::string& fmt, const int&);
template void RSFSCLog::info<std::size_t>(const std::string& fmt, const std::size_t&);
template void RSFSCLog::info<double>(const std::string& fmt, const double&);
template void RSFSCLog::info<std::string>(const std::string& fmt, const std::string&);
```

对应的用法有：

```cpp
RSFSCLog::getInstance()->info("this is test");
RSFSCLog::getInstance()->info("this is {0} test", "first");
RSFSCLog::getInstance()->info("there are {0} test", 2);
RSFSCLog::getInstance()->info("there are {0} apple", 2.5);

RSFSCLog::getInstance()->debug("Easy padding in numbers like {:08d}", 12);
RSFSCLog::getInstance()->info("Support for int: {0:d};  hex: {0:#x};  oct: {0:o}; bin: {0:b}", 42);
RSFSCLog::getInstance()->warn("Support for floats {:0.3f}", 11.23456);
RSFSCLog::getInstance()->error("Support for floats {}\n", 1.23456);

RSFSCLog::getInstance()->trace("{:<30}", std::string("left aligned"));
RSFSCLog::getInstance()->info("{:>30}", std::string("right aligned"));
RSFSCLog::getInstance()->info("{:^30}", std::string("centered"));
}
```

### 4.2 Qt控件用法

提供了一个配套的`MessageBrowser`的控件，只需要像普通Qt控件一样，在CMakeLists.txt里解析头文件、添加cpp即可使用：

```cpp
// NOTE must new first cause it define many show event
browser_message_ = new MessageBrowser(QString::fromUtf8(PROJECT_NAME), this);  
RSFSCLog::getInstance()->setQtLogWidget(browser_message_, "slotShowMessage");
```

然后就可以将他放到你放到的任何位置去了，需要注意的是这个需要在程序比较早的地方去构造，不然很多输出信息就看不到了。

注意，要在析构`MessageBrowser`之前，先解除绑定

```cpp
RSFSCLog::getInstance()->setQtLogWidget(Q_NULLPTR);  // delete sink
delete browser_message_;
```


